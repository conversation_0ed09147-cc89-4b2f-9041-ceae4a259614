import { getUser } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'
import { NewRecordingInterface } from '@/components/recording/new-recording-interface'

interface DashboardDataProps {
  doctorId: string
  isMobile?: boolean
}

export async function DashboardData({ doctorId, isMobile = false }: DashboardDataProps) {
  // This runs inside Suspense boundary, not blocking initial page render
  // OPTIMIZED: Load only initial page (15 items) for fast initial render
  const [user, consultationsResult] = await Promise.all([
    getUser(),
    getConsultations({ page: 1, pageSize: 15 }), // Small initial load
  ])

  const consultations = consultationsResult.success ? consultationsResult.data.consultations || [] : []
  const hasMore = consultationsResult.success ? consultationsResult.data.hasMore : false

  return (
    <NewRecordingInterface
      user={user}
      consultations={consultations}
      hasMore={hasMore}
      doctorId={doctorId}
      isMobile={isMobile}
    />
  )
}
