import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/auth/supabase-helpers'
import { createClient } from '@/lib/supabase/server'

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user session
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: consultationId } = await params
    const body = await request.json()

    if (!consultationId) {
      return NextResponse.json(
        { error: 'Consultation ID is required' },
        { status: 400 }
      )
    }

    const { edited_note_json } = body

    if (!edited_note_json) {
      return NextResponse.json(
        { error: 'edited_note_json is required' },
        { status: 400 }
      )
    }

    console.log(`💾 Saving edited note for consultation: ${consultationId}`)

    const supabase = await createClient()
    
    try {
      const { data, error } = await supabase
        .from('consultations')
        .update({ 
          edited_note_json: edited_note_json,
          updated_at: new Date().toISOString()
        })
        .eq('id', consultationId)
        .select()
        .single()

      if (error) {
        console.error(`❌ Failed to save edited note for consultation ${consultationId}:`, error)
        return NextResponse.json(
          { error: 'Failed to save edited note' },
          { status: 500 }
        )
      }

      console.log(`✅ Successfully saved edited note for consultation ${consultationId}`)

      return NextResponse.json({
        success: true,
        data: data
      })

    } catch (dbError) {
      console.error(`❌ Database error saving consultation ${consultationId}:`, dbError)
      return NextResponse.json(
        { error: 'Database error occurred' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error(`❌ Error in PATCH /api/consultations/[id]:`, error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
