Of course. Based on our discussions and the files you've provided, I will construct a comprehensive, self-contained project plan. This document is designed to be given to a developer or another LLM with zero prior context.

It will focus exclusively on the foundational refactoring to a durable, JSON-first architecture with an improved UI, as you requested, deferring the "Celer Assist" features for a later phase. I will integrate my previous suggestions and the information from your provided files directly into this plan.

Project Brief & Technical Specification: Celer AI Foundation Refactor
Document Purpose: This document outlines the foundational architectural refactor for the Celer AI application. The goal is to move from a real-time, streaming-based architecture to a more durable, scalable, and modern JSON-first system. This will provide a rock-solid foundation for future features and immediately improve UI/UX and system resilience.

Part 1: The Core Objective & Architectural Shift
The Problem: The current architecture relies on a fragile, long-lived HTTP connection (Browser <-> Vercel API <-> Python Backend) to stream AI-generated summaries. If this chain breaks for any reason (network issues, user closes tab), the entire generation process fails and work is lost. The frontend also uses a simple content-editable div, which is not suitable for handling structured medical data.

The Solution: We will implement an industry-standard Asynchronous Worker Pattern.

Current (Fragile) Flow:
Browser --(holds connection open)--> Vercel API --(proxy)--> Python Backend
New (Durable) Flow:
Job Dispatch: Browser --(fast request)--> Vercel API --(publishes job)--> Google Pub/Sub
Background Work: Google Pub/Sub --(triggers)--> Python Worker --(processes & saves to DB)--> Supabase
UI Update: Browser --(polls for status)--> Vercel API <--> Supabase
This decouples the user from the long-running AI task, ensuring no work is ever lost and providing a more responsive user experience.

Part 2: The Foundational Changes (The "What")
1. Database & Data Schema
The cornerstone of this refactor is moving from unstructured text blobs to structured JSON.

Database Schema (consultations table):
Two new columns have been added to the consultations table in Supabase:
ai_generated_note_json (JSONB): Stores the structured summary directly from the AI.
edited_note_json (JSONB): Stores the user-edited version of the summary.
Type Definition (types.ts): Your types.ts file needs to be updated to reflect these changes.
Generated typescript
// In types.ts, update the Consultation type
export type Consultation = {
  // ... all existing fields
  ai_generated_note: string | null // This can remain for legacy data
  edited_note: string | null       // This can also remain
  ai_generated_note_json: Json | null // ADD THIS
  edited_note_json: Json | null       // ADD THIS
}
content_copy
download
Use code with caution.
TypeScript
Schema Validation (Recommendation):
Suggestion: Implement Zod for schema validation. Define a canonical schema for the consultation note in a shared file (e.g., lib/schemas/note.ts). This will provide both TypeScript types and runtime validation, ensuring data integrity across the entire application.
2. Backend: From Web Server to Durable Worker
Python Backend (main.py): The FastAPI application will be completely replaced by a Google Cloud Run worker triggered by Pub/Sub messages.
Structured JSON Output (Per your document): The worker will now instruct Gemini to output structured JSON directly. It will not receive unstructured text and format it later. This is a more reliable and efficient approach.
3. API Layer (Vercel/Next.js)
The existing streaming endpoint (/api/generate-summary-stream) will be repurposed into a fast, non-blocking job dispatcher.
A new polling endpoint (/api/consultation-status/[id]) will be created for the frontend to check the status of a generation job.
4. Frontend: State Management & UI Upgrade
State Management: We will implement Zustand as a centralized state store. This will dramatically simplify component logic, remove "prop drilling," and improve performance. All UI state (e.g., selectedConsultation, isGenerating) will live in this store.
UI Editor: The current ContentEditableEditor component will be replaced.
Suggestion: Upgrade to a modern, JSON-native editor framework like TipTap. This is crucial. A simple content-editable div cannot properly manage or render structured JSON data. TipTap will allow us to build a rich editing experience where each section (Diagnosis, Prescription, etc.) is a distinct, manageable part of the JSON object.
Part 3: Detailed Implementation Guide (The "How")
This is a step-by-step guide for the developer.

Step 1: Python Backend Refactor (main.py)
Objective: Convert main.py from a FastAPI server to a Pub/Sub-triggered worker that generates structured JSON.

Define the JSON Schema with Pydantic: Based on your prompts.json, create Pydantic models that represent the desired JSON structure. This will be used to guide Gemini.
Generated python
# In main.py or a new schema.py file
from pydantic import BaseModel
from typing import List, Optional

class Vitals(BaseModel):
    bp: Optional[str]
    pulse: Optional[str]
    temp: Optional[str]
    spo2: Optional[str]

class Examination(BaseModel):
    vitals: Vitals
    general_examination: Optional[str]
    systemic_exam: Optional[dict]

class CelerNote(BaseModel):
    patient_details: dict
    chief_complaints: List[str]
    history_of_present_illness: str
    past_medical_history: List[str]
    examination_findings: Examination
    provisional_diagnosis: dict # { "diagnosis": "...", "icd_10_code": "..." }
    investigations_ordered: List[str]
    prescription: List[dict] # { "drug": "...", "dose": "...", "freq": "...", "duration": "..." }
    follow_up_plan: str
    notes: Optional[str]
content_copy
download
Use code with caution.
Python
Update the Worker Logic: Refactor main.py to remove FastAPI and handle Pub/Sub messages.
Generated python
# main.py - High-level structure
import os
import base64
import json
# ... other imports

# (Initialization code for Supabase and Gemini clients remains similar)

async def process_summary_job(event, context):
    # 1. Decode Pub/Sub message to get consultation_id
    message_data = json.loads(base64.b64decode(event['data']).decode('utf-8'))
    consultation_id = message_data.get('consultation_id')
    
    # 2. Perform idempotency check & update status to "processing" in Supabase
    # ...

    # 3. Fetch consultation details, process files (audio, images)
    # ... (This logic remains the same)

    # 4. Generate the prompt using the logic from prompts.json
    prompt = generate_prompt(...) # Your existing function

    # 5. Call Gemini with JSON Mode
    gemini_model = genai.GenerativeModel("gemini-2.5-flash-lite-preview-06-17")
    response = await gemini_model.generate_content_async(
        contents=[prompt] + file_parts, # Combine prompt with file data
        generation_config={
            "response_mime_type": "application/json",
            # Use the Pydantic model defined above
            "response_schema": CelerNote,
        },
    )

    # 6. Save the structured JSON response to the database
    # The 'response.text' will be a JSON string.
    # It's better to parse and re-serialize to ensure it's valid JSON.
    try:
        summary_json = json.loads(response.text)
        update_payload = {
            "ai_generated_note_json": summary_json,
            "status": "generated",
        }
        supabase.table("consultations").update(update_payload).eq("id", consultation_id).execute()
    except json.JSONDecodeError:
        # Handle cases where Gemini fails to produce valid JSON
        # Log the error and set status to "failed"
        # ...
content_copy
download
Use code with caution.
Python
Step 2: Vercel/Next.js API Refactor
Objective: Change the API to dispatch jobs and provide status updates.

Refactor Job Dispatch Endpoint (/api/generate-summary-stream/route.ts):
This route becomes very simple. It takes the consultation_id, publishes it to the generate-summary-jobs Pub/Sub topic, and immediately returns a success response. It no longer proxies a stream.
Create New Status Polling Endpoint (app/api/consultation-status/[id]/route.ts):
This GET route takes a consultation id.
It queries the consultations table in Supabase.
It returns the current status and, if the status is "generated", it returns the content of edited_note_json (if it exists) or ai_generated_note_json.
Step 3: Frontend Implementation (streamlined-recording-area.tsx and new files)
Objective: Implement Zustand, the new polling logic, and the UI editor upgrade.

Install Dependencies: npm install zustand immer tiptap-react @tiptap/starter-kit
Create Zustand Store (lib/stores/consultation-store.ts):
Create a store to manage selectedConsultation, isGenerating, isEditing, and the consultationNoteJson.
Update UI Component (streamlined-recording-area.tsx):
Refactor the component to pull state from the Zustand store instead of local useState.
Implement the new handleGenerate flow:
Generated typescript
const handleGenerate = async (consultationToUse?: Consultation) => {
  const consultationId = selectedConsultation?.id || consultationToUse?.id;
  if (!consultationId) { /* show error */ return; }

  // 1. Set loading state in Zustand store
  useConsultationStore.getState().setIsGenerating(true);

  // 2. Dispatch the job (fast, non-blocking call)
  const response = await fetch('/api/start-generation', { // New or repurposed endpoint
    method: 'POST',
    body: JSON.stringify({ consultation_id: consultationId }),
  });

  if (!response.ok) { /* handle error, stop loading */ return; }

  // 3. Start polling for the result
  const pollingInterval = setInterval(async () => {
    const statusResponse = await fetch(`/api/consultation-status/${consultationId}`);
    const statusResult = await statusResponse.json();

    if (statusResult.status === 'generated') {
      clearInterval(pollingInterval);
      // Update Zustand store with the received JSON note
      useConsultationStore.getState().setConsultationNote(statusResult.noteJson);
      useConsultationStore.getState().setIsGenerating(false);
    } else if (statusResult.status === 'failed') {
      clearInterval(pollingInterval);
      // Show error message
      useConsultationStore.getState().setIsGenerating(false);
    }
    // If status is 'processing', do nothing and let the loop continue.
  }, 3000); // Poll every 3 seconds
};
content_copy
download
Use code with caution.
TypeScript
Implement TipTap Editor (Recommendation):
Create a new component, e.g., JsonDrivenEditor.tsx.
This component will take the consultationNoteJson from the Zustand store as a prop.
It will render separate, editable TipTap instances for each relevant field (e.g., a TipTap instance for "History of Present Illness," another for "Notes").
When a field is edited, the component will update the corresponding key in the JSON object and notify the Zustand store. This provides a clean, structured way to manage edits.
Implement Auto-Save (Recommendation):
Create a useAutosave hook as previously suggested.
In the JsonDrivenEditor component, when the user edits a field, trigger a debounced saveNote function that sends the updated edited_note_json to a new server action to be persisted in the database. Provide subtle UI feedback (Saving..., All changes saved).
This refactor will result in a more robust, scalable, and professional application, setting a strong foundation for future development.
Project Brief & Technical Specification: Celer AI Foundation Refactor
Document Version: 1.0
Date: July 13, 2025
Project Goal: To execute a foundational refactor of the Celer AI application, moving from a fragile real-time architecture to a durable, scalable, and modern JSON-first system. This document outlines the vision, architecture, and step-by-step implementation plan.

Part 1: Executive Summary & Core Objectives
The "Why": The current system architecture, which relies on a single, long-lived HTTP connection to stream AI summaries, is not robust. Network interruptions or user navigation can lead to a total loss of the generated work, which is unacceptable for a medical application. Furthermore, the data is stored as an unstructured text blob, limiting our ability to build intelligent features.

The Core Objective: We will refactor the system to use an Asynchronous Worker Pattern. This decouples the user from the long-running AI task, ensuring every request is processed durably. We will simultaneously transition to a JSON-first data model, enabling structured, reliable data handling and paving the way for advanced future capabilities.

From This (Fragile):
Browser --(holds connection open for 30s+)--> Vercel API --(proxy)--> Python Backend
To This (Durable):
Job Dispatch: Browser --(fast request)--> Vercel API --(publishes job)--> Google Pub/Sub
Background Work: Google Pub/Sub --(triggers)--> Python Worker --(processes & saves JSON to DB)--> Supabase
UI Update: Browser --(polls for status)--> Vercel API <--> Supabase
Part 2: Key Architectural & Feature Requirements
2.1. Database & Data Schema
The new source of truth will be structured JSON, not text.

Database Columns: The consultations table has been extended with two JSONB columns:
ai_generated_note_json: Stores the structured JSON directly from the Gemini model.
edited_note_json: Stores the user-edited version of the note, preserving its structure.
Type Safety (Recommendation):
Action: Update lib/types.ts to include the new JSON fields.
Best Practice: Implement Zod to define a canonical schema for the consultation note. This schema should be the single source of truth, providing both TypeScript types for compile-time safety and a validation function for runtime data integrity.
2.2. Multi-Template Prompt System
The system's intelligence is driven by its ability to use different prompts for different contexts. This must be a core part of the refactor.

Configuration Driven: The entire prompt engineering logic is defined in prompts.json. The Python worker must load this configuration on startup and use it to select the correct prompt template based on the consultation_type sent from the frontend.
Structured JSON Output: The Python worker will now leverage Gemini's JSON mode. For each template in prompts.json, the worker must configure the Gemini API call to enforce a JSON output that matches the template_structure defined for that prompt. This is a critical change from parsing unstructured text.
2.3. Frontend UI & State Management
Centralized State: All frontend state related to the consultation process will be managed by a Zustand store. This includes selectedConsultation, isGenerating, loading states, and the consultation note JSON itself.
JSON-Native Editor (Critical Upgrade): The existing ContentEditableEditor component is insufficient.
Action: It must be replaced with a modern, JSON-driven editor framework like TipTap. This will allow the UI to render distinct, editable components for each key in the edited_note_json object, providing a truly structured editing experience.
Debounced Auto-Save: To ensure no edits are lost and to provide a seamless UX, a useAutosave hook (as defined in useAutosave.ts) should be implemented. Any change in the TipTap editor should trigger a debounced function that saves the entire edited_note_json object to the database via a server action. The UI should provide subtle feedback (Saving..., All changes saved).
Part 3: Non-Functional Requirements & Best Practices
A developer with no context must be aware of these cross-cutting concerns.

Error Handling & Resilience:
AI Errors: If Gemini fails to return valid JSON, the Python worker must catch the error, log the malformed response, and update the consultation status to "failed" in the database.
UI Errors: The frontend must never crash due to a backend error. The polling logic should handle "failed" statuses gracefully, displaying a clear, non-intrusive error message to the user.
Performance & UX:
Optimistic Updates: The UI should feel instant. When a user creates a consultation, the frontend should optimistically update and immediately begin polling for the generation status.
Background Preloading: The "infinite scroll" logic seen in consultations-sidebar.tsx that preloads the next page of results when the user scrolls 70% of the way down is an excellent UX pattern that should be maintained.
Logging & Observability:
Crucial for Distributed Systems: Implement centralized logging (e.g., Sentry, Logtail). It must be possible to trace a single request from the initial API call on Vercel, through the Pub/Sub message, to the execution logs in the Python Cloud Run worker. Log the unique consultation_id at every step.
Developer Experience (DevEx):
Local Setup: The project README.md must be updated with clear instructions on how to run this multi-service architecture locally. This will likely involve using the Google Cloud CLI to emulate Pub/Sub or using a local Redis instance for job queuing simulation.
Schema Management & Versioning (Forward-Looking):
Suggestion: Add a schema_version field to the root of the note's JSON schema (e.g., "schema_version": "1.0"). This will enable the creation of a migrator function in the future to handle schema changes without breaking old data.
Part 4: Detailed Implementation Plan
Phase 1: Backend Refactor (Python Worker)
Objective: Convert main.py into a durable worker that generates structured JSON based on dynamic prompts.

Configuration:
In the Google Cloud Console, change the Cloud Run service's trigger from HTTP to Pub/Sub, subscribed to a new topic named generate-summary-jobs.
Code Implementation:
Remove FastAPI: The application will no longer be a web server.
Load prompts.json: On startup, load and validate prompts.json using the Pydantic PromptConfig model defined in main.py. Store this in memory.
Pydantic Models for JSON Output: For each template in prompts.json, define a corresponding Pydantic BaseModel that represents its template_structure.
Main Handler (process_summary_job):
Receives a Pub/Sub message with consultation_id and consultation_type.
Fetches the corresponding prompt template from the loaded configuration using the consultation_type.
Calls Gemini's generate_content method with generation_config set for JSON output:
Generated python
# Example call within the worker
response = await gemini_model.generate_content_async(
    contents=...,
    generation_config={
        "response_mime_type": "application/json",
        # Dynamically select the correct Pydantic schema model
        # based on the consultation_type.
        "response_schema": get_schema_for_type(request.consultation_type),
    },
)
Use code with caution.
Python
Saves the validated JSON response to the ai_generated_note_json column in Supabase and sets the status to "generated".
Phase 2: API Layer Refactor (Next.js)
Objective: Adapt the Next.js API to dispatch jobs and poll for status.

Install Dependencies: npm install @google-cloud/pubsub.
Refactor Job Dispatch Endpoint: The endpoint at /api/generate-summary-stream/route.ts should be repurposed. It will now:
Accept a POST request with a consultation_id.
Publish a message to the generate-summary-jobs topic.
Return an immediate 200 OK response.
Create New Status Polling Endpoint:
Create a new file: app/api/consultation-status/[id]/route.ts.
This GET route will query the consultations table for the given ID and return its status and edited_note_json (or ai_generated_note_json).
Phase 3: Frontend Refactor (React)
Objective: Implement Zustand, the new polling UI, and a JSON-native editor.

Install Dependencies: npm install zustand immer @tiptap/react @tiptap/starter-kit.
Implement Zustand Store: Create a consultation-store.ts to manage all relevant state.
Update streamlined-recording-area.tsx:
Refactor the component to use the Zustand store.
Modify the handleGenerate function to dispatch the job and then initiate a polling loop using setInterval that calls the new status endpoint every 3 seconds.
When polling reveals a "generated" status, it should update the Zustand store with the new JSON note, which will cause the UI to re-render.
Replace ContentEditableEditor.tsx:
Create a new JsonDrivenEditor.tsx component that uses TipTap.
This component will receive the consultationNoteJson from the Zustand store.
It will dynamically render separate, editable TipTap instances for the free-text fields within the JSON (e.g., History of Present Illness).
On every edit, it updates the JSON object in the Zustand store and triggers the debounced auto-save function.
Appendix: Asset Checklist
This project will involve modifying or referencing the following key files:

prompts.json: The source of truth for all AI prompt templates.
main.py: The Python backend, to be refactored into a worker.
lib/types.ts: The database schema definitions.
app/api/generate-summary-stream/route.ts: The Vercel API endpoint to be refactored.
components/recording/streamlined-recording-area.tsx: The primary UI component for the recording and summary display area.
components/recording/content-editable-editor.tsx: The legacy editor component to be replaced.
Problem 2: The Autosave Problem ("Thundering Writes")
The goal of autosaving is to prevent data loss, but saving on every single keystroke is even more dangerous than polling.

The Flaw: If a user types 300 characters, a naive onChange handler would trigger 300 separate API calls and 300 UPDATE statements to the database. This write pressure would lock up tables and bring the system to a crawl.
Solution: Debounced Autosaving (Bundling Changes)

This is the industry-standard solution and aligns with the useAutosave.ts file you provided. We must ensure it's implemented correctly.

Recommended Architecture:
User Types: The user types in the new TipTap editor. The UI state (in the Zustand store) is updated instantly on every keystroke, so the UI feels responsive.
Debounce Timer: With each keystroke, a timer on the frontend is reset.
Fire on Pause: Only when the user pauses typing for a set period (e.g., 2 seconds) does the save function actually execute.
Single Write: This save function takes the latest version of the edited_note_json from the Zustand store and sends it to the server in a single API call, resulting in a single database UPDATE.
Benefit: This strategy bundles dozens or even hundreds of keystrokes into a single, efficient write operation, reducing database load by over 95% while still ensuring user edits are saved frequently.

First Principles Analysis
What is the event we are waiting for? The completion of an asynchronous job on a separate service (Google Cloud Run).
Who needs to know about this event? A specific user's browser session.
What is the core problem? The service that completes the job (the Python worker) has no direct, cheap, or persistent communication channel to the user's browser.
What are our constraints? We must minimize Supabase database load, avoid persistent connections, and minimize Vercel function execution time.
The root of the problem is notification. How does the Python worker, upon completion, notify the system that the result is ready without the client having to constantly ask the most expensive part of our system (the Postgres database)?

The answer is to separate the notification from the data. The database is our persistent data store. It is not our event notification system. Using it as one is the source of all these issues.

The Root Cause Solution: Webhook + Lightweight Cache (Redis)
This architecture is designed for this exact scenario. It's efficient, scalable, and respects the cost model of serverless functions. We will use Redis, which you already have in the architecture for quota management, as our high-speed, low-cost notification cache.

Here is the new, correct data flow:

Job Dispatch: (Unchanged)
Frontend -> Vercel API -> Pub/Sub -> Python Worker starts.
Worker Completion & Notification (The "Webhook"):
The Python worker finishes generating the summary.
It makes one write to Supabase to save the final ai_generated_note_json.
Its final action is to make a single, fast POST request to a new, dedicated Vercel endpoint: /api/notify-completion. The payload is simple: { "consultationId": "...", "status": "generated" }.
Caching the Notification (The Lightweight Store):
The /api/notify-completion endpoint is extremely fast and cheap.
It receives the webhook from the Python worker.
It does not talk to Supabase.
It makes one write to Redis: redis.setex('status:<consultationId>', 300, 'generated'). (Set with a 5-minute expiry).
It returns 200 OK instantly.
Efficient Polling (Client polls the Cache, not the Database):
The frontend client needs to know the status. It polls a different Vercel endpoint: /api/job-status/[id].
This endpoint is also extremely fast and cheap. Its only job is to check Redis.
It makes one read from Redis: redis.get('status:<consultationId>').
If the key does not exist, it means the job is still running. It returns an empty 202 Accepted response immediately.
If the key does exist, it means the job is done. Only now does it make a single, final query to the main Supabase database to fetch the full JSON note and return it to the client.
Diagram of the Scalable Flow:

Generated code
[Frontend]                               [Vercel Serverless]                          [Google Cloud Run]
    |                                            |                                        |
    |----1. Start Job (POST)-------------------->|                                        |
    |                                            |----2. Publish to Pub/Sub-------------->|
    |                                            |                                        |----3. Process (20-50s)
    |                                            |                                        |----4. Save to Supabase (1x)
    |                                            |                                        |----5. Notify (Webhook)------>[API: /notify-completion]
    |                                            |                                        |      |
    |                                            |      |----6. Set Redis Key-----------|      |
    |                                            |<-------------------------------------|      |
    |<---Poll /api/job-status (cheap)-------------|----7. Check Redis (fast)------------>|      |
    |                                            |      | (Key not found)                      |
    |<---202 Accepted (fast)----------------------|<-------------------------------------|      |
    |                                            |                                        |
    |<---Poll /api/job-status (cheap)-------------|----8. Check Redis (fast)------------>|      |
    |                                            |      | (Key FOUND!)                         |
    |                                            |      |----9. Get Data from Supabase (1x)-->|
    |<---200 OK (final data)----------------------|<-------------------------------------|
Use code with caution.
Why This Is the Correct Architecture:

Solves Database Load: The client polls an endpoint that only hits Redis. A single Redis instance can handle tens of thousands of these simple GET requests per second with sub-millisecond latency. Your expensive Postgres database is only queried once at the very end.
Solves Vercel Cost: Both the /api/notify-completion and /api/job-status endpoints are now incredibly fast. Their execution time will be ~50ms, not 55 seconds. This is a ~1000x reduction in the primary cost driver for the status check.
Solent Autosave Problem: This architecture doesn't directly solve autosaving, but the principle is the same. The debounced save from the client hits an endpoint that does a single, fast UPDATE to the database. This is efficient and correct.
Final Comprehensive Plan for the Developer (Version 3.0 - The CTO-Approved Plan)
This is the definitive, context-free plan. It mandates the Webhook + Redis Cache architecture for status updates.

Project Brief: Celer AI Foundation Refactor & Scalable Architecture

Primary Goal: To execute a foundational architectural refactor of the Celer AI application, building a durable, high-performance, and cost-effective system.

Key Architectural Mandates:

Asynchronous Processing: Long-running AI generation jobs must be fully decoupled from the user's session and processed by a background worker.
Cache-Based Notification: The system must not use database polling or long-polling for status updates. It will use a Webhook + Redis Cache pattern. The database is the source of truth for data; Redis is the source of truth for ephemeral job status.
Structured Data-First: The application will treat JSONB as the primary data format for consultation notes.
Efficient Database Writes: All user-driven saves (e.g., text editing) must be debounced on the client to bundle multiple actions into a single write operation.
Implementation Plan:

Backend (Python Worker):
Action: Convert the FastAPI application into a Google Cloud Run worker triggered by Pub/Sub.
Requirement: Use Gemini's JSON Mode to generate structured JSONB output based on the prompts.json configuration.
Completion Logic: After successfully saving the final JSON to the Supabase Postgres database, the worker's final step is to make a single POST request (a "webhook") to a new /api/notify-completion endpoint on the Vercel application. This request must be secured with a shared secret/token.
API Layer (Next.js):
Job Dispatch Endpoint: A simple, non-blocking function that publishes a job to Pub/Sub and returns immediately.
Completion Notification Endpoint (New):
Path: app/api/notify-completion/route.ts
Action: This secure endpoint receives the webhook from the Python worker. It validates the secret token. Its only job is to write the completion status to a Redis key (e.g., redis.setex('status:<consultationId>', 300, 'generated')). It must be fast and stateless.
Job Status Endpoint (Modified for Redis):
Path: app/api/job-status/[id]/route.ts
Action: This is the endpoint the client will poll. It first checks Redis for the status key.
If the key does not exist, it returns 202 Accepted immediately.
If the key exists, it makes a single query to Supabase Postgres to retrieve the final ai_generated_note_json and returns the full payload.
Frontend (React/Next.js):
State Management: Centralize all UI state in a Zustand store.
Editor: Replace the current editor with a TipTap-based component designed to render and edit a structured JSON object.
Autosaving: Implement a debounced save hook for the TipTap editor that saves the edited_note_json to the database after a 2-second pause in user typing.
Status Handling (Smart Short Polling):
After dispatching a job, the client will begin polling the /api/job-status/[id] endpoint every 2-3 seconds.
Because this endpoint is extremely cheap and fast (hitting Redis), this polling frequency is acceptable and provides a responsive user experience without high cost or database load.
The polling loop stops as soon as it receives a 200 OK with the final data.
Project Brief: Celer AI Foundation Refactor & Scalable Architecture

Primary Goal: To execute a foundational architectural refactor of the Celer AI application, building a durable, high-performance, and cost-effective system capable of scaling to thousands of concurrent users.

Key Architectural Mandates:

Asynchronous Processing: Long-running AI generation jobs must be fully decoupled from the user's session and processed by a background worker.
Cache-Based Notification: The system must use a Webhook + Redis Cache pattern for job status notification. This architecture is mandated to prevent database load and manage costs effectively. Direct database polling or long-polling for status updates is disallowed.
Separation of Concerns: The database (Supabase Postgres) is the source of truth for persistent data. The cache (Redis) is the source of truth for ephemeral job status.
Efficient Database Writes: All user-driven saves must be debounced on the client to bundle multiple actions into a single write operation.
Implementation Plan:

Backend (Python Worker):
Action: Convert the FastAPI application into a Google Cloud Run worker triggered by Pub/Sub.
Requirement: Use Gemini's JSON Mode to generate structured JSONB output based on the prompts.json configuration.
Completion Logic: After saving the final JSON to the Supabase database, the worker's final step is to make a single, secured POST request (a "webhook") to a new /api/notify-completion endpoint on the Vercel application.
API Layer (Next.js):
Job Dispatch Endpoint: A simple, non-blocking function that publishes a job to Pub/Sub and returns immediately.
Completion Notification Endpoint (/api/notify-completion): A secure endpoint that receives the webhook from the worker. Its only job is to write the completion status to a Redis key with a 5-minute expiry (e.g., redis.setex('status:<consultationId>', 300, 'generated')). This function must be fast and stateless.
Job Status Endpoint (/api/job-status/[id]): This is the endpoint the client will poll. It must only query Redis for the status key. If the key exists, it then makes a single query to Supabase to get the final data. If the key does not exist, it returns 202 Accepted immediately.
Frontend (React/Next.js):
State Management: Centralize all UI state in a Zustand store.
Editor: Replace the current editor with a TipTap-based component designed to render and edit a structured JSON object.
Autosaving: Implement a debounced save hook for the TipTap editor.
Status Handling (Efficient Short Polling):
After dispatching a job, the client will begin polling the /api/job-status/[id] endpoint every 2-3 seconds. This polling frequency is acceptable only because the endpoint is hitting the Redis cache, not the primary database.
The polling loop stops as soon as it receives a 200 OK with the final data.
