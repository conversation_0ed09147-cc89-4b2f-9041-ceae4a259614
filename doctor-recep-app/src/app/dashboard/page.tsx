import { Metadata } from 'next'
import { Suspense } from 'react'
import { headers } from 'next/headers'
import { verifySession, getUser } from '@/lib/auth/dal'
import { DashboardData } from '@/components/data/dashboard-data'
import { DashboardClient } from '@/components/shared/dashboard-client'
import { PWAInstallPrompt } from '@/components/pwa/pwa-install-prompt'
import { DashboardSkeleton } from '@/components/ui/skeleton-loaders'

export const metadata: Metadata = {
  title: 'Dashboard - Celer AI',
  description: 'Create new patient consultations with AI-powered summaries',
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
}

export default async function DashboardPage() {
  // OPTIMIZED: Only verify session (fast), then stream the rest
  const [session, user, headersList] = await Promise.all([
    verifySession(),
    getUser(),
    headers()
  ])

  // Server-side mobile detection to prevent hydration mismatch
  const userAgent = headersList.get('user-agent') || ''
  const isMobile = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|Windows Phone/i.test(userAgent)

  return (
    <>
      {/* STREAMING: Data loads progressively while user sees immediate structure */}
      <Suspense fallback={<DashboardSkeleton />}>
        <DashboardData doctorId={session.userId} isMobile={isMobile} />
      </Suspense>

      {/* Client-side components for modals */}
      <DashboardClient doctorId={session.userId} user={user} />

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />
    </>
  )
}
