"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 3
Intelligent Caching and Optimization

This module implements multi-level caching with intelligent eviction policies,
cache warming, and performance optimization strategies.
"""

import asyncio
import time
import hashlib
import pickle
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import redis
import json
from functools import wraps

from monitoring import logger, metrics, performance_context

# =====================================================
# CACHING CONFIGURATION
# =====================================================

CACHE_CONFIG = {
    'redis': {
        'default_ttl': 3600,        # 1 hour
        'max_key_length': 250,
        'compression_threshold': 1024,  # Compress values > 1KB
        'connection_pool_size': 10
    },
    
    'memory': {
        'max_size_mb': 100,
        'default_ttl': 300,         # 5 minutes
        'cleanup_interval': 60,     # 1 minute
        'eviction_policy': 'lru'    # lru, lfu, ttl
    },
    
    'file_cache': {
        'max_size_mb': 500,
        'default_ttl': 86400,       # 24 hours
        'cleanup_interval': 3600,   # 1 hour
        'base_path': '/tmp/celer_cache'
    },
    
    'warming': {
        'enabled': True,
        'batch_size': 10,
        'concurrent_warmers': 3,
        'warm_on_startup': True
    }
}

# =====================================================
# CACHE ENUMS AND DATA STRUCTURES
# =====================================================

class CacheLevel(str, Enum):
    """Cache levels in order of speed"""
    MEMORY = "memory"
    REDIS = "redis"
    FILE = "file"

class EvictionPolicy(str, Enum):
    """Cache eviction policies"""
    LRU = "lru"          # Least Recently Used
    LFU = "lfu"          # Least Frequently Used
    TTL = "ttl"          # Time To Live
    RANDOM = "random"    # Random eviction

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: Optional[int]
    size_bytes: int
    
    def is_expired(self) -> bool:
        """Check if entry is expired"""
        if self.ttl_seconds is None:
            return False
        
        age = (datetime.utcnow() - self.created_at).total_seconds()
        return age > self.ttl_seconds
    
    def touch(self):
        """Update last accessed time and increment access count"""
        self.last_accessed = datetime.utcnow()
        self.access_count += 1

@dataclass
class CacheStats:
    """Cache statistics"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size_bytes: int = 0
    entry_count: int = 0
    
    def hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0

# =====================================================
# MEMORY CACHE
# =====================================================

class MemoryCache:
    """High-speed in-memory cache with intelligent eviction"""
    
    def __init__(self, max_size_mb: int = None, eviction_policy: EvictionPolicy = None):
        self.max_size_bytes = (max_size_mb or CACHE_CONFIG['memory']['max_size_mb']) * 1024 * 1024
        self.eviction_policy = eviction_policy or EvictionPolicy(CACHE_CONFIG['memory']['eviction_policy'])
        
        self.entries: Dict[str, CacheEntry] = {}
        self.stats = CacheStats()
        self.cleanup_task: Optional[asyncio.Task] = None
        
        logger.info("Memory cache initialized", 
                   max_size_mb=max_size_mb,
                   eviction_policy=self.eviction_policy.value)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        entry = self.entries.get(key)
        
        if entry is None:
            self.stats.misses += 1
            metrics.increment_counter('cache_operations_total', 
                                    {'level': 'memory', 'operation': 'miss'})
            return None
        
        if entry.is_expired():
            await self.delete(key)
            self.stats.misses += 1
            metrics.increment_counter('cache_operations_total', 
                                    {'level': 'memory', 'operation': 'miss'})
            return None
        
        entry.touch()
        self.stats.hits += 1
        metrics.increment_counter('cache_operations_total', 
                                {'level': 'memory', 'operation': 'hit'})
        
        return entry.value
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in cache"""
        try:
            # Calculate size
            size_bytes = len(pickle.dumps(value))
            
            # Check if we need to evict entries
            await self._ensure_space(size_bytes)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
                access_count=1,
                ttl_seconds=ttl_seconds or CACHE_CONFIG['memory']['default_ttl'],
                size_bytes=size_bytes
            )
            
            # Remove old entry if exists
            if key in self.entries:
                old_entry = self.entries[key]
                self.stats.size_bytes -= old_entry.size_bytes
                self.stats.entry_count -= 1
            
            # Add new entry
            self.entries[key] = entry
            self.stats.size_bytes += size_bytes
            self.stats.entry_count += 1
            
            metrics.increment_counter('cache_operations_total', 
                                    {'level': 'memory', 'operation': 'set'})
            
            return True
            
        except Exception as e:
            logger.error("Failed to set memory cache entry", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete entry from cache"""
        if key in self.entries:
            entry = self.entries.pop(key)
            self.stats.size_bytes -= entry.size_bytes
            self.stats.entry_count -= 1
            
            metrics.increment_counter('cache_operations_total', 
                                    {'level': 'memory', 'operation': 'delete'})
            return True
        
        return False
    
    async def _ensure_space(self, needed_bytes: int):
        """Ensure enough space for new entry"""
        while (self.stats.size_bytes + needed_bytes) > self.max_size_bytes and self.entries:
            await self._evict_entry()
    
    async def _evict_entry(self):
        """Evict entry based on eviction policy"""
        if not self.entries:
            return
        
        if self.eviction_policy == EvictionPolicy.LRU:
            # Evict least recently used
            oldest_key = min(self.entries.keys(), 
                           key=lambda k: self.entries[k].last_accessed)
        
        elif self.eviction_policy == EvictionPolicy.LFU:
            # Evict least frequently used
            oldest_key = min(self.entries.keys(), 
                           key=lambda k: self.entries[k].access_count)
        
        elif self.eviction_policy == EvictionPolicy.TTL:
            # Evict entry with shortest remaining TTL
            oldest_key = min(self.entries.keys(), 
                           key=lambda k: self.entries[k].created_at)
        
        else:  # RANDOM
            import random
            oldest_key = random.choice(list(self.entries.keys()))
        
        await self.delete(oldest_key)
        self.stats.evictions += 1
        
        logger.debug("Cache entry evicted", 
                    key=oldest_key, 
                    policy=self.eviction_policy.value)
    
    async def start_cleanup(self):
        """Start cleanup task"""
        if self.cleanup_task:
            return
        
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Memory cache cleanup started")
    
    async def _cleanup_loop(self):
        """Cleanup expired entries"""
        while True:
            try:
                await asyncio.sleep(CACHE_CONFIG['memory']['cleanup_interval'])
                
                expired_keys = [
                    key for key, entry in self.entries.items()
                    if entry.is_expired()
                ]
                
                for key in expired_keys:
                    await self.delete(key)
                
                if expired_keys:
                    logger.debug("Expired cache entries cleaned up", count=len(expired_keys))
                
            except Exception as e:
                logger.error("Error in memory cache cleanup", error=str(e))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'stats': asdict(self.stats),
            'hit_rate': self.stats.hit_rate(),
            'size_mb': self.stats.size_bytes / (1024 * 1024),
            'max_size_mb': self.max_size_bytes / (1024 * 1024),
            'utilization': self.stats.size_bytes / self.max_size_bytes,
            'eviction_policy': self.eviction_policy.value
        }

# =====================================================
# REDIS CACHE
# =====================================================

class RedisCache:
    """Redis-based distributed cache"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.stats = CacheStats()
        
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=False,  # We'll handle encoding ourselves
                connection_pool_max_connections=CACHE_CONFIG['redis']['connection_pool_size']
            )
        except Exception as e:
            logger.error("Failed to initialize Redis cache", error=str(e))
            return None
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        if not self.redis_client:
            return None
        
        try:
            cache_key = self._build_cache_key(key)
            data = self.redis_client.get(cache_key)
            
            if data is None:
                self.stats.misses += 1
                metrics.increment_counter('cache_operations_total', 
                                        {'level': 'redis', 'operation': 'miss'})
                return None
            
            # Deserialize data
            value = pickle.loads(data)
            
            self.stats.hits += 1
            metrics.increment_counter('cache_operations_total', 
                                    {'level': 'redis', 'operation': 'hit'})
            
            return value
            
        except Exception as e:
            logger.error("Failed to get Redis cache entry", key=key, error=str(e))
            self.stats.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in Redis cache"""
        if not self.redis_client:
            return False
        
        try:
            cache_key = self._build_cache_key(key)
            data = pickle.dumps(value)
            
            # Compress if large
            if len(data) > CACHE_CONFIG['redis']['compression_threshold']:
                import gzip
                data = gzip.compress(data)
            
            ttl = ttl_seconds or CACHE_CONFIG['redis']['default_ttl']
            
            self.redis_client.setex(cache_key, ttl, data)
            
            self.stats.entry_count += 1
            self.stats.size_bytes += len(data)
            
            metrics.increment_counter('cache_operations_total', 
                                    {'level': 'redis', 'operation': 'set'})
            
            return True
            
        except Exception as e:
            logger.error("Failed to set Redis cache entry", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete entry from Redis cache"""
        if not self.redis_client:
            return False
        
        try:
            cache_key = self._build_cache_key(key)
            result = self.redis_client.delete(cache_key)
            
            if result:
                metrics.increment_counter('cache_operations_total', 
                                        {'level': 'redis', 'operation': 'delete'})
            
            return bool(result)
            
        except Exception as e:
            logger.error("Failed to delete Redis cache entry", key=key, error=str(e))
            return False
    
    def _build_cache_key(self, key: str) -> str:
        """Build Redis cache key with prefix"""
        prefix = "celer:cache:"
        
        # Hash long keys
        if len(key) > CACHE_CONFIG['redis']['max_key_length']:
            key = hashlib.md5(key.encode()).hexdigest()
        
        return f"{prefix}{key}"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics"""
        redis_info = {}
        if self.redis_client:
            try:
                redis_info = self.redis_client.info('memory')
            except Exception:
                pass
        
        return {
            'stats': asdict(self.stats),
            'hit_rate': self.stats.hit_rate(),
            'redis_info': redis_info
        }

# =====================================================
# MULTI-LEVEL CACHE MANAGER
# =====================================================

class MultiLevelCacheManager:
    """Manages multiple cache levels with intelligent promotion/demotion"""
    
    def __init__(self):
        self.memory_cache = MemoryCache()
        self.redis_cache = RedisCache()
        
        # Cache warming configuration
        self.warming_enabled = CACHE_CONFIG['warming']['enabled']
        self.warming_tasks: List[asyncio.Task] = []
        
        logger.info("🚀 Multi-level cache manager initialized")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache hierarchy"""
        # Try memory cache first (fastest)
        value = await self.memory_cache.get(key)
        if value is not None:
            return value
        
        # Try Redis cache (medium speed)
        value = await self.redis_cache.get(key)
        if value is not None:
            # Promote to memory cache
            await self.memory_cache.set(key, value)
            return value
        
        return None
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in all cache levels"""
        success = True
        
        # Set in memory cache
        memory_success = await self.memory_cache.set(key, value, ttl_seconds)
        success = success and memory_success
        
        # Set in Redis cache
        redis_success = await self.redis_cache.set(key, value, ttl_seconds)
        success = success and redis_success
        
        return success
    
    async def delete(self, key: str) -> bool:
        """Delete from all cache levels"""
        memory_result = await self.memory_cache.delete(key)
        redis_result = await self.redis_cache.delete(key)
        
        return memory_result or redis_result
    
    async def warm_cache(self, keys_and_loaders: List[tuple[str, Callable]]):
        """Warm cache with predefined data"""
        if not self.warming_enabled:
            return
        
        logger.info("Starting cache warming", keys_count=len(keys_and_loaders))
        
        batch_size = CACHE_CONFIG['warming']['batch_size']
        concurrent_warmers = CACHE_CONFIG['warming']['concurrent_warmers']
        
        # Process in batches
        for i in range(0, len(keys_and_loaders), batch_size):
            batch = keys_and_loaders[i:i + batch_size]
            
            # Create warming tasks
            warming_tasks = [
                self._warm_single_key(key, loader)
                for key, loader in batch
            ]
            
            # Execute with concurrency limit
            semaphore = asyncio.Semaphore(concurrent_warmers)
            
            async def warm_with_semaphore(task):
                async with semaphore:
                    return await task
            
            await asyncio.gather(*[warm_with_semaphore(task) for task in warming_tasks])
        
        logger.info("Cache warming completed")
    
    async def _warm_single_key(self, key: str, loader: Callable):
        """Warm a single cache key"""
        try:
            # Check if already cached
            if await self.get(key) is not None:
                return
            
            # Load data
            if asyncio.iscoroutinefunction(loader):
                value = await loader()
            else:
                value = loader()
            
            # Cache the value
            await self.set(key, value)
            
            logger.debug("Cache key warmed", key=key)
            
        except Exception as e:
            logger.error("Failed to warm cache key", key=key, error=str(e))
    
    async def start_cleanup(self):
        """Start cleanup tasks for all cache levels"""
        await self.memory_cache.start_cleanup()
        logger.info("Cache cleanup tasks started")
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get statistics from all cache levels"""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'redis_cache': self.redis_cache.get_stats(),
            'warming_enabled': self.warming_enabled,
            'active_warming_tasks': len(self.warming_tasks)
        }

# =====================================================
# CACHE DECORATORS
# =====================================================

def cached(key_func: Callable = None, ttl_seconds: int = None, cache_manager: MultiLevelCacheManager = None):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Get cache manager
            cache = cache_manager or get_cache_manager()
            
            # Try to get from cache
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            await cache.set(cache_key, result, ttl_seconds)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, we need to handle differently
            # This is a simplified version
            return func(*args, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# =====================================================
# GLOBAL CACHE MANAGER
# =====================================================

_cache_manager: Optional[MultiLevelCacheManager] = None

def get_cache_manager() -> MultiLevelCacheManager:
    """Get singleton cache manager"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = MultiLevelCacheManager()
    return _cache_manager
