/**
 * CELER AI ENTERPRISE ARCHITECTURE 3.0
 * Validation Schemas for Event-Driven API Gateway
 * 
 * This file contains Zod schemas for validating consultation creation requests
 * in the new event-driven architecture.
 */

import { z } from 'zod'

// =====================================================
// CORE VALIDATION SCHEMAS
// =====================================================

// File validation schema
export const FileValidationSchema = z.object({
  name: z.string().min(1, 'File name is required'),
  type: z.string().min(1, 'File type is required'),
  size: z.number().positive('File size must be positive'),
})

// Audio file validation
export const AudioFileSchema = FileValidationSchema.extend({
  type: z.string().refine(
    (type) => [
      'audio/webm',
      'audio/mp3', 
      'audio/wav',
      'audio/m4a',
      'audio/mpeg',
      'audio/mp4',
      'audio/ogg'
    ].includes(type),
    'Invalid audio file type'
  ),
  size: z.number().max(100 * 1024 * 1024, 'Audio file must be less than 100MB')
})

// Image file validation
export const ImageFileSchema = FileValidationSchema.extend({
  type: z.string().refine(
    (type) => [
      'image/jpeg',
      'image/jpg',
      'image/png', 
      'image/webp',
      'image/heic'
    ].includes(type),
    'Invalid image file type'
  ),
  size: z.number().max(50 * 1024 * 1024, 'Image file must be less than 50MB')
})

// Consultation type validation
export const ConsultationTypeSchema = z.enum([
  'outpatient',
  'discharge',
  'surgery',
  'radiology',
  'dermatology',
  'cardiology_echo',
  'ivf_cycle',
  'pathology'
])

// Submitted by validation
export const SubmittedBySchema = z.enum(['doctor', 'receptionist'])

// =====================================================
// CONSULTATION CREATION SCHEMAS
// =====================================================

// Legacy schema for server-side file upload (deprecated)
export const ConsultationCreateSchema = z.object({
  // Required fields
  primary_audio_file: AudioFileSchema,
  submitted_by: SubmittedBySchema,
  consultation_type: ConsultationTypeSchema.default('outpatient'),

  // Optional fields
  patient_name: z.string().max(255, 'Patient name too long').optional(),
  doctor_notes: z.string().max(2000, 'Doctor notes too long').optional(),
  additional_notes: z.string().max(2000, 'Additional notes too long').optional(),

  // File arrays
  additional_audio_files: z.array(AudioFileSchema).max(5, 'Maximum 5 additional audio files').default([]),
  image_files: z.array(ImageFileSchema).max(10, 'Maximum 10 image files').default([]),

  // Metadata
  metadata: z.record(z.any()).optional(),

  // Client information for tracking
  client_version: z.string().optional(),
  user_agent: z.string().optional(),
  ip_address: z.string().optional(),
}).refine(
  (data) => {
    // Calculate total file size
    const primarySize = data.primary_audio_file.size
    const additionalAudioSize = data.additional_audio_files.reduce((sum, file) => sum + file.size, 0)
    const imageSize = data.image_files.reduce((sum, file) => sum + file.size, 0)
    const totalSize = primarySize + additionalAudioSize + imageSize

    return totalSize <= 200 * 1024 * 1024 // 200MB total limit
  },
  {
    message: 'Total file size must not exceed 200MB',
    path: ['total_file_size']
  }
)

// NEW: Direct-to-R2 schema (GOLD STANDARD - files already uploaded)
export const ConsultationCreateDirectSchema = z.object({
  // Required fields
  consultation_id: z.string().uuid('Invalid consultation ID'),
  consultation_type: ConsultationTypeSchema.default('outpatient'),
  submitted_by: SubmittedBySchema,

  // Optional fields
  patient_name: z.string().max(255, 'Patient name too long').optional(),
  doctor_notes: z.string().max(2000, 'Doctor notes too long').optional(),
  additional_notes: z.string().max(2000, 'Additional notes too long').optional(),

  // File URLs (already uploaded to R2)
  primary_audio_url: z.string().url('Invalid primary audio URL'),
  additional_audio_urls: z.array(z.string().url()).max(5, 'Maximum 5 additional audio files').default([]),
  image_urls: z.array(z.string().url()).max(10, 'Maximum 10 image files').default([]),

  // File metadata for integrity checking
  file_metadata: z.object({
    primary_audio: z.object({
      size: z.number().positive(),
      type: z.string(),
      checksum: z.string().optional(),
      filename: z.string()
    }),
    additional_audio: z.array(z.object({
      size: z.number().positive(),
      type: z.string(),
      checksum: z.string().optional(),
      filename: z.string()
    })).max(5).default([]),
    images: z.array(z.object({
      size: z.number().positive(),
      type: z.string(),
      checksum: z.string().optional(),
      filename: z.string()
    })).max(10).default([])
  }),

  // Total file size for quota checking
  total_file_size_bytes: z.number().positive('Total file size must be positive'),

  // Metadata
  metadata: z.record(z.any()).optional(),

  // Client information for tracking
  client_version: z.string().optional(),
  user_agent: z.string().optional(),
}).refine(
  (data) => {
    // Validate total file size matches sum of individual files
    const calculatedSize = data.file_metadata.primary_audio.size +
      data.file_metadata.additional_audio.reduce((sum, file) => sum + file.size, 0) +
      data.file_metadata.images.reduce((sum, file) => sum + file.size, 0)

    return Math.abs(calculatedSize - data.total_file_size_bytes) < 1024 // Allow 1KB difference for rounding
  },
  {
    message: 'Total file size does not match sum of individual file sizes',
    path: ['total_file_size_bytes']
  }
).refine(
  (data) => {
    // Validate file size limits
    return data.total_file_size_bytes <= 200 * 1024 * 1024 // 200MB total limit
  },
  {
    message: 'Total file size must not exceed 200MB',
    path: ['total_file_size_bytes']
  }
)

// =====================================================
// EVENT SCHEMAS FOR PUB/SUB
// =====================================================

export const ConsultationEventSchema = z.object({
  // Event metadata
  schema_version: z.string().default('1.0'),
  correlation_id: z.string().uuid('Invalid correlation ID'),
  consultation_id: z.string().uuid('Invalid consultation ID'),
  event_type: z.literal('consultation.create'),
  timestamp: z.string().datetime('Invalid timestamp'),
  
  // User context
  user_id: z.string().uuid('Invalid user ID'),
  session_id: z.string().optional(),
  
  // File URLs (after upload)
  primary_audio_url: z.string().url('Invalid primary audio URL'),
  additional_audio_urls: z.array(z.string().url()).default([]),
  image_urls: z.array(z.string().url()).default([]),
  
  // File checksums for integrity
  primary_audio_checksum: z.string().optional(),
  additional_audio_checksums: z.array(z.string()).default([]),
  image_checksums: z.array(z.string()).default([]),
  
  // Consultation metadata
  consultation_type: ConsultationTypeSchema,
  submitted_by: SubmittedBySchema,
  patient_name: z.string().optional(),
  doctor_notes: z.string().optional(),
  additional_notes: z.string().optional(),
  
  // File information
  total_file_size_bytes: z.number().positive('Total file size must be positive'),
  
  // Processing metadata
  retry_count: z.number().default(0),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  
  // Additional metadata
  metadata: z.record(z.any()).default({}),
  
  // Client information
  client_version: z.string().optional(),
  user_agent: z.string().optional(),
  ip_address: z.string().optional(),
})

// =====================================================
// RESPONSE SCHEMAS
// =====================================================

export const ConsultationCreateResponseSchema = z.object({
  consultation_id: z.string().uuid(),
  correlation_id: z.string().uuid(),
  status: z.literal('accepted'),
  estimated_processing_time: z.string(),
  created_at: z.string().datetime(),
  
  // File upload results
  uploaded_files: z.object({
    primary_audio: z.object({
      url: z.string().url(),
      checksum: z.string().optional(),
      size: z.number()
    }),
    additional_audio: z.array(z.object({
      url: z.string().url(),
      checksum: z.string().optional(),
      size: z.number()
    })).default([]),
    images: z.array(z.object({
      url: z.string().url(),
      checksum: z.string().optional(),
      size: z.number()
    })).default([])
  }),
  
  // Tracking information
  tracking: z.object({
    job_id: z.string().uuid(),
    status_url: z.string().url(),
    webhook_url: z.string().url().optional()
  })
})

// =====================================================
// ERROR SCHEMAS
// =====================================================

export const ValidationErrorSchema = z.object({
  error: z.literal('validation_error'),
  message: z.string(),
  field_errors: z.record(z.array(z.string())),
  correlation_id: z.string().uuid().optional()
})

export const RateLimitErrorSchema = z.object({
  error: z.literal('rate_limit_exceeded'),
  message: z.string(),
  retry_after: z.number(),
  quota_info: z.object({
    current_usage: z.number(),
    quota_limit: z.number(),
    reset_at: z.string().datetime()
  }),
  correlation_id: z.string().uuid().optional()
})

export const FileUploadErrorSchema = z.object({
  error: z.literal('file_upload_error'),
  message: z.string(),
  failed_files: z.array(z.object({
    filename: z.string(),
    error: z.string()
  })),
  correlation_id: z.string().uuid().optional()
})

export const SystemErrorSchema = z.object({
  error: z.literal('system_error'),
  message: z.string(),
  correlation_id: z.string().uuid().optional(),
  support_reference: z.string().optional()
})

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Validate consultation creation request
 */
export function validateConsultationCreate(data: unknown) {
  return ConsultationCreateSchema.safeParse(data)
}

/**
 * Validate consultation event for Pub/Sub
 */
export function validateConsultationEvent(data: unknown) {
  return ConsultationEventSchema.safeParse(data)
}

/**
 * Create standardized error response
 */
export function createErrorResponse(
  type: 'validation_error' | 'rate_limit_exceeded' | 'file_upload_error' | 'system_error',
  message: string,
  details?: any,
  correlationId?: string
) {
  const baseError = {
    error: type,
    message,
    correlation_id: correlationId
  }
  
  switch (type) {
    case 'validation_error':
      return {
        ...baseError,
        field_errors: details || {}
      }
    case 'rate_limit_exceeded':
      return {
        ...baseError,
        retry_after: details?.retry_after || 60,
        quota_info: details?.quota_info || {}
      }
    case 'file_upload_error':
      return {
        ...baseError,
        failed_files: details?.failed_files || []
      }
    case 'system_error':
      return {
        ...baseError,
        support_reference: details?.support_reference
      }
    default:
      return baseError
  }
}

/**
 * Calculate file checksum (placeholder - implement with crypto)
 */
export async function calculateFileChecksum(file: File): Promise<string> {
  // TODO: Implement actual checksum calculation
  // For now, return a placeholder based on file properties
  return `${file.name}-${file.size}-${file.lastModified}`
}

/**
 * Validate file integrity
 */
export function validateFileIntegrity(file: File, expectedChecksum: string): boolean {
  // TODO: Implement actual integrity validation
  // For now, return true as placeholder
  return true
}

// =====================================================
// TYPE EXPORTS
// =====================================================

export type ConsultationCreateRequest = z.infer<typeof ConsultationCreateSchema>
export type ConsultationEvent = z.infer<typeof ConsultationEventSchema>
export type ConsultationCreateResponse = z.infer<typeof ConsultationCreateResponseSchema>
export type ValidationError = z.infer<typeof ValidationErrorSchema>
export type RateLimitError = z.infer<typeof RateLimitErrorSchema>
export type FileUploadError = z.infer<typeof FileUploadErrorSchema>
export type SystemError = z.infer<typeof SystemErrorSchema>

// Legacy compatibility
export type ConsultationType = z.infer<typeof ConsultationTypeSchema>
export type SubmittedBy = z.infer<typeof SubmittedBySchema>
