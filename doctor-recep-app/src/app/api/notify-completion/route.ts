import { NextRequest, NextResponse } from 'next/server'
import { getRedisClient } from '@/lib/redis'

export async function POST(request: NextRequest) {
  try {
    // CRITICAL: Webhook security as mandated by phase1.md
    // This endpoint receives webhooks from the Python worker and must be secured
    const authHeader = request.headers.get('authorization')
    const expectedSecret = process.env.WEBHOOK_SECRET_TOKEN

    if (!expectedSecret) {
      console.error('❌ WEBHOOK_SECRET_TOKEN environment variable not configured')
      return NextResponse.json(
        { error: 'Webhook endpoint not properly configured' },
        { status: 500 }
      )
    }

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('❌ Missing or invalid authorization header in webhook request')
      return NextResponse.json(
        { error: 'Unauthorized - missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const providedSecret = authHeader.substring(7) // Remove 'Bearer ' prefix
    if (providedSecret !== expectedSecret) {
      console.error('❌ Invalid webhook secret token provided')
      return NextResponse.json(
        { error: 'Unauthorized - invalid secret token' },
        { status: 401 }
      )
    }

    // Parse webhook payload
    const body = await request.json()
    const { consultationId, status } = body

    if (!consultationId || !status) {
      console.error('❌ Missing required fields in webhook payload:', { consultationId, status })
      return NextResponse.json(
        { error: 'Missing required fields: consultationId and status' },
        { status: 400 }
      )
    }

    console.log(`📨 Webhook received: consultation ${consultationId} status: ${status}`)

    // Write completion status to Redis with 5-minute expiry
    const redis = getRedisClient()
    const statusKey = `status:${consultationId}`
    
    try {
      await redis.setEx(statusKey, 300, status) // 5 minutes TTL
      console.log(`✅ Updated Redis status for consultation ${consultationId}: ${status}`)
      
      return NextResponse.json({
        success: true,
        message: 'Status updated successfully'
      })
      
    } catch (redisError) {
      console.error('❌ Failed to update Redis status:', redisError)
      return NextResponse.json(
        { error: 'Failed to update status cache' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Webhook processing error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
