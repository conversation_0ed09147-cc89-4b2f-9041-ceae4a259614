import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/auth/supabase-helpers'
import { createClient } from '@/lib/supabase/server'
import { checkQuotaRedis, incrementQuotaRedis, getRedisClient } from '@/lib/redis'
import { incrementQuotaUsage } from '@/lib/actions/quota'
import { v4 as uuidv4 } from 'uuid'
import { pubsubService } from '@/lib/services/pubsub-service'
// // Dynamic import to avoid build issues with Google Cloud SDK

export async function POST(request: NextRequest) {
  try {
    // Verify user session and check quota before proceeding
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Generate unique job ID for tracking
    const jobId = uuidv4()

    // Redis-first quota check for instant response
    const quotaCheck = await checkQuotaRedis(session.userId)

    if (!quotaCheck.allowed) {
      return NextResponse.json(
        { error: 'Quota exceeded. You have reached your monthly AI generation limit. Please contact admin or wait for next month.' },
        { status: 429 }
      )
    }

    // Create job tracking record in Redis using v2 atomic format
    const redis = getRedisClient()
    const jobKeyV2 = `job:v2:${jobId}`
    const jobData = {
      jobId,
      userId: session.userId,
      type: 'ai_generation',
      status: 'pending',
      createdAt: new Date().toISOString(),
      metadata: JSON.stringify({
        quotaBeforeIncrement: quotaCheck.currentUsage
      })
    }

    // Store job with 30-minute TTL using atomic hash operations
    await redis.hSet(jobKeyV2, jobData)
    await redis.expire(jobKeyV2, 1800)

    // Optimistically increment quota in Redis (instant)
    try {
      await incrementQuotaRedis(session.userId)
    } catch (error) {
      console.error('Redis quota increment failed:', error)
      // Update job status to reflect the error using atomic operations
      await redis.hSet(jobKeyV2, {
        status: 'failed',
        error: 'Quota increment failed',
        completedAt: new Date().toISOString()
      })
      // Continue anyway - we'll sync with database later
    }

    // Fire-and-forget database quota update (async) with job tracking
    incrementQuotaUsage(session.userId)
      .then(async () => {
        // Update job status to indicate successful database sync using atomic operations
        const updatedMetadata = JSON.parse(jobData.metadata)
        updatedMetadata.databaseSyncCompleted = true
        updatedMetadata.databaseSyncAt = new Date().toISOString()

        await redis.hSet(jobKeyV2, {
          status: 'processing',
          metadata: JSON.stringify(updatedMetadata)
        })
      })
      .catch(async (error) => {
        console.error('Background database quota update failed:', error)
        // Update job status to reflect database sync failure using atomic operations
        const failedMetadata = JSON.parse(jobData.metadata)
        failedMetadata.databaseSyncFailed = true
        failedMetadata.databaseSyncError = error.message

        await redis.hSet(jobKeyV2, {
          status: 'failed',
          error: 'Database quota update failed',
          completedAt: new Date().toISOString(),
          metadata: JSON.stringify(failedMetadata)
        })
        await redis.expire(jobKeyV2, 3600) // Keep failed jobs longer
      })

    const supabase = await createClient()
    const body = await request.json()

    console.log('🚀 Pub/Sub Job Dispatch - Received request:', {
      consultation_id: body.consultation_id || 'NEW',
      primary_audio_url: body.primary_audio_url ? '✅ Present' : '❌ Missing',
      additional_audio_urls: body.additional_audio_urls?.length || 0,
      image_urls: body.image_urls?.length || 0,
      submitted_by: body.submitted_by,
      consultation_type: body.consultation_type,
      patient_name: body.patient_name
    })

    let consultation

    if (body.consultation_id) {
      // REGENERATE: Use existing consultation
      console.log('🔄 Regenerating existing consultation:', body.consultation_id)

      const { data: existingConsultation, error: fetchError } = await supabase
        .from('consultations')
        .select('*')
        .eq('id', body.consultation_id)
        .eq('doctor_id', session.userId) // Security: ensure user owns this consultation
        .single()

      if (fetchError || !existingConsultation) {
        return NextResponse.json(
          { error: 'Consultation not found or access denied' },
          { status: 404 }
        )
      }

      consultation = existingConsultation
    } else {
      // CREATE NEW: Create consultation record in Supabase first
      console.log('✨ Creating new consultation')

      const { data: newConsultation, error: insertError } = await supabase
        .from('consultations')
        .insert({
          doctor_id: session.userId,
          primary_audio_url: body.primary_audio_url,
          additional_audio_urls: body.additional_audio_urls || [],
          image_urls: body.image_urls || [],
          submitted_by: body.submitted_by || 'doctor',
          consultation_type: body.consultation_type || 'outpatient',
          patient_name: body.patient_name,
          doctor_notes: body.doctor_notes,
          additional_notes: body.additional_notes,
          status: 'pending_generation'
        })
        .select()
        .single()

      if (insertError || !newConsultation) {
        console.error('❌ Failed to create consultation:', insertError)
        return NextResponse.json(
          { error: 'Failed to create consultation record' },
          { status: 500 }
        )
      }

      consultation = newConsultation
    }

    console.log('✅ Using consultation:', consultation.id)

    const topicName = 'generate-summary-jobs'

    // Environment-based job dispatch
    if (process.env.NODE_ENV === 'production') {
      // PRODUCTION: Use centralized Pub/Sub service
      try {
        const messageData = {
          consultation_id: consultation.id,
          consultation_type: body.consultation_type || 'outpatient',
          event_type: 'consultation.generate_summary'
        }

        const publishResult = await pubsubService.publishMessage(
          topicName,
          messageData,
          {
            correlationId: jobId,
            orderingKey: consultation.id,
            attributes: {
              consultationId: consultation.id,
              consultationType: body.consultation_type || 'outpatient',
              userId: session.userId
            }
          }
        )

        if (!publishResult.success) {
          throw new Error(`Failed to publish message: ${publishResult.error}`)
        }

        console.log('✅ Published Pub/Sub message:', publishResult.messageId)
      } catch (pubsubError) {
        console.error('❌ Pub/Sub dispatch failed:', pubsubError)
        return NextResponse.json(
          { error: 'Failed to dispatch job to processing queue' },
          { status: 500 }
        )
      }
    } else {
      // LOCAL DEVELOPMENT: Direct HTTP call to Python worker
      try {
        const workerUrl = process.env.LOCAL_WORKER_URL || 'http://localhost:3005'
        console.log('🔧 Local development: calling Python worker at', workerUrl)

        const response = await fetch(`${workerUrl}/local/trigger-job`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Celer-AI-Frontend/1.0'
          },
          body: JSON.stringify({
            consultation_id: consultation.id,
            user_id: session.userId,
            correlation_id: `local_${consultation.id}_${Date.now()}`,
            primary_audio_url: consultation.primary_audio_url || '',
            additional_audio_urls: consultation.additional_audio_urls || [],
            image_urls: consultation.image_urls || [],
            consultation_type: body.consultation_type || 'outpatient',
            submitted_by: consultation.submitted_by || 'doctor',
            patient_name: consultation.patient_name,
            doctor_notes: consultation.doctor_notes,
            additional_notes: consultation.additional_notes,
            total_file_size_bytes: 0,
            retry_count: 0,
            priority: 'normal',
            timestamp: new Date().toISOString(),
            source: 'local_development'
          }),
        })

        if (!response.ok) {
          throw new Error(`Worker responded with status ${response.status}`)
        }

        const result = await response.json()
        console.log('✅ Local worker response:', result)
      } catch (localError) {
        console.error('❌ Local Python worker call failed:', localError)
        return NextResponse.json(
          {
            error: 'Local worker is not running or accessible. Make sure Python backend is running on port 3005.',
            details: localError instanceof Error ? localError.message : 'Unknown error'
          },
          { status: 503 }
        )
      }
    }

    // Return immediate success response with consultation ID
    return NextResponse.json({
      success: true,
      consultationId: consultation.id,
      jobId: jobId,
      status: 'dispatched',
      message: `Job dispatched successfully via ${process.env.NODE_ENV === 'production' ? 'Pub/Sub' : 'direct HTTP'}. Use the consultation ID to poll for status.`
    }, {
      status: 200,
      headers: {
        'X-Job-ID': jobId,
        'X-Consultation-ID': consultation.id,
        'X-Dispatch-Method': process.env.NODE_ENV === 'production' ? 'pubsub' : 'direct-http'
      }
    })
  } catch (error) {
    console.error('Streaming API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}