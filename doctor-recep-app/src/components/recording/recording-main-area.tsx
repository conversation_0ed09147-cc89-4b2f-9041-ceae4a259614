'use client'

import { useState, useEffect } from 'react'
import { Consultation } from '@/lib/types'
import { StreamlinedRecordingArea } from './streamlined-recording-area'
import { motion } from 'framer-motion'
import { useRecording } from '@/hooks/useRecording'
import { useConsultationStore, ConsultationData } from '@/lib/stores/consultation-store'
import { useAutosaveManager } from '@/hooks/useAutosaveManager'

interface RecordingMainAreaProps {
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  doctorName?: string
  onConsultationUpdate: (consultation: Consultation) => void
}

// Helper function to convert Consultation to ConsultationData
function convertToConsultationData(consultation: Consultation): ConsultationData {
  return {
    id: consultation.id,
    primary_audio_url: consultation.primary_audio_url || '',
    additional_audio_urls: Array.isArray(consultation.additional_audio_urls)
      ? consultation.additional_audio_urls as string[]
      : [],
    image_urls: Array.isArray(consultation.image_urls)
      ? consultation.image_urls as string[]
      : [],
    submitted_by: consultation.submitted_by,
    consultation_type: consultation.consultation_type,
    patient_name: consultation.patient_name || undefined,
    doctor_notes: consultation.doctor_notes || undefined,
    additional_notes: consultation.additional_notes || undefined,
    ai_generated_note_json: consultation.ai_generated_note_json,
    edited_note_json: consultation.edited_note_json,
    status: consultation.status as 'pending' | 'processing' | 'generated' | 'failed',
    created_at: consultation.created_at,
    updated_at: consultation.updated_at
  }
}

export function RecordingMainArea({
  selectedConsultation,
  isDarkMode,
  doctorId,
  doctorName,
  onConsultationUpdate
}: RecordingMainAreaProps) {
  // Use the new recording hook (pure logic, no side effects)
  const {
    isRecording,
    isPaused,
    recordingDuration,
    audioBlob,
    audioFile,
    startRecording,
    pauseRecording,
    stopRecording,
    clearAudio,
    error: recordingError
  } = useRecording()

  // All state from Zustand store (following CTO's Phase 2 plan)
  const patientName = useConsultationStore(state => state.patientName)
  const selectedTemplate = useConsultationStore(state => state.selectedTemplate)
  const additionalNotes = useConsultationStore(state => state.additionalNotes)
  const images = useConsultationStore(state => state.images)
  const summary = useConsultationStore(state => state.summary)
  const isGenerating = useConsultationStore(state => state.isGenerating)
  const isEditing = useConsultationStore(state => state.isEditing)
  const setPatientName = useConsultationStore(state => state.setPatientName)
  const setSelectedTemplate = useConsultationStore(state => state.setSelectedTemplate)
  const setAdditionalNotes = useConsultationStore(state => state.setAdditionalNotes)
  const setSummary = useConsultationStore(state => state.setSummary)
  const setGenerationStatus = useConsultationStore(state => state.setGenerationStatus)
  const setIsEditing = useConsultationStore(state => state.setEditing)
  const addImages = useConsultationStore(state => state.addImages)
  const removeImage = useConsultationStore(state => state.removeImage)
  const setCurrentConsultation = useConsultationStore(state => state.setCurrentConsultation)

  // Centralized autosave manager (following CTO's Phase 2 specification)
  const { handleAudioBlobChange, handleImageFilesChange, handleAdditionalNotesChange } = useAutosaveManager()

  // Load consultation data when selected
  useEffect(() => {
    // Update Zustand store with consultation data (this will trigger all form field updates)
    const consultationData = selectedConsultation ? convertToConsultationData(selectedConsultation) : null
    setCurrentConsultation(consultationData)

    // Clear recording state when consultation changes
    clearAudio()
  }, [selectedConsultation, clearAudio, setCurrentConsultation])

  // Show recording errors if any
  useEffect(() => {
    if (recordingError) {
      alert(recordingError)
    }
  }, [recordingError])

  // Centralized autosave trigger for audio recording completion
  useEffect(() => {
    if (audioBlob && patientName.trim() && selectedTemplate) {
      // Trigger centralized autosave through the manager
      handleAudioBlobChange(audioBlob, patientName, selectedTemplate)
    }
  }, [audioBlob, patientName, selectedTemplate, handleAudioBlobChange])

  // Handle image upload with centralized autosave
  const handleImageUpload = async (files: FileList) => {
    try {
      const newImages: Array<{ id: string, file: File, preview?: string }> = []
      const imageFiles: File[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        if (!file.type.startsWith('image/')) continue
        if (file.size > 10 * 1024 * 1024) continue // 10MB limit

        const preview = URL.createObjectURL(file)

        newImages.push({
          id: `${Date.now()}-${i}`,
          file,
          preview
        })

        imageFiles.push(file)
      }

      addImages(newImages)

      // Trigger centralized autosave for images
      if (imageFiles.length > 0) {
        handleImageFilesChange(imageFiles)
      }
    } catch {
      console.error('Failed to process images')
    }
  }



  // Wrapper for additional notes with centralized autosave
  const handleNotesChange = (value: string) => {
    setAdditionalNotes(value)
    // Trigger centralized autosave for notes
    handleAdditionalNotesChange(value)
  }

  // Wrapper for generation status to match expected interface
  const handleSetIsGenerating = (generating: boolean) => {
    if (generating) {
      setGenerationStatus('processing')
    } else {
      setGenerationStatus('idle')
    }
  }

  return (
    <div className={`h-full flex flex-col relative ${
      isDarkMode ? 'bg-transparent' : 'bg-transparent'
    }`}>
      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-5xl mx-auto"
        >
          <StreamlinedRecordingArea
            selectedConsultation={selectedConsultation}
            isDarkMode={isDarkMode}
            doctorId={doctorId}
            doctorName={doctorName}
            onConsultationUpdate={(updated) => {
              onConsultationUpdate(updated)
              // SEAMLESS TRANSITION: Clear recording state when consultation is saved
              if (updated && !selectedConsultation) {
                // Clear recording state to show saved content
                clearAudio()
              }
            }}
            // Pass all the state and handlers (patientName and selectedTemplate now from store)
            isRecording={isRecording}
            isPaused={isPaused}
            recordingDuration={recordingDuration}
            audioBlob={audioBlob}
            audioFile={audioFile}
            images={images}
            setImages={addImages}
            isGenerating={isGenerating}
            setIsGenerating={handleSetIsGenerating}
            summary={summary}
            setSummary={setSummary}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
            additionalNotes={additionalNotes}
            setAdditionalNotes={handleNotesChange}
            startRecording={startRecording}
            pauseRecording={pauseRecording}
            stopRecording={stopRecording}
            handleImageUpload={handleImageUpload}
            removeImage={removeImage}
            clearAudio={clearAudio}
          />
        </motion.div>
      </div>
    </div>
  )
}
