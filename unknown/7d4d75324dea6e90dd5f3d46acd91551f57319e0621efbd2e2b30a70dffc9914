# Test Google One Tap with HTTPS Locally

## Option 1: Use ngrok (Recommended)
```bash
# Install ngrok if you haven't
npm install -g ngrok

# In one terminal, start your Next.js app
npm run dev

# In another terminal, expose it via HTTPS
ngrok http 3004
```

Then:
1. Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)
2. Add it to Google Cloud Console Authorized JavaScript Origins
3. Test Google One Tap with the HTTPS URL

## Option 2: Use Next.js with HTTPS
```bash
# Install mkcert for local HTTPS
brew install mkcert  # macOS
# or
choco install mkcert  # Windows

# Create local certificate
mkcert -install
mkcert localhost

# Start Next.js with HTTPS
HTTPS=true npm run dev
```

## Current Issue
Google One Tap requires proper CORS headers and sometimes has issues with localhost HTTP.
The HTTPS approach often resolves these CORS/network errors.
