'use client'

import React, { useState } from 'react'
import { useConsultationStore, type Suggestion } from '@/lib/stores/consultation-store'

interface ContextualAssistButtonProps {
  fieldName: string
  currentValue: string
  onSuggestionApplied?: () => void
}

export function ContextualAssistButton({ 
  fieldName, 
  currentValue, 
  onSuggestionApplied 
}: ContextualAssistButtonProps) {
  const {
    currentSuggestions,
    isLoadingSuggestions,
    requestSuggestions,
    applySuggestion,
    clearSuggestions
  } = useConsultationStore()

  const [showSuggestions, setShowSuggestions] = useState(false)
  const [customRequest, setCustomRequest] = useState('')

  const handleAssistClick = async () => {
    console.log('✨ [ContextualAssistButton] Assist button clicked:', { fieldName, currentValue })

    if (showSuggestions) {
      console.log('🔄 [ContextualAssistButton] Hiding suggestions')
      setShowSuggestions(false)
      clearSuggestions()
      return
    }

    console.log('🚀 [ContextualAssistButton] Requesting suggestions...')
    setShowSuggestions(true)
    await requestSuggestions(fieldName, currentValue)
  }

  const handleApplySuggestion = (suggestion: Suggestion) => {
    console.log('🔧 Applying suggestion:', { fieldName, suggestion })
    applySuggestion(fieldName, suggestion)
    setShowSuggestions(false)
    setCustomRequest('')
    onSuggestionApplied?.()
    console.log('✅ Suggestion applied and UI updated')
  }

  const handleCustomRequest = async () => {
    if (!customRequest.trim()) return
    
    await requestSuggestions(fieldName, currentValue, customRequest)
    setCustomRequest('')
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleCustomRequest()
    }
  }

  return (
    <>
      {/* ✨ Button - positioned at top-right of field */}
      <button
        onClick={handleAssistClick}
        className="absolute -top-1 -right-8
                   opacity-0 group-hover:opacity-100 transition-all duration-200
                   w-6 h-6 rounded-full bg-gradient-to-r from-purple-500 to-cyan-500
                   flex items-center justify-center text-white text-xs
                   hover:scale-110 hover:shadow-lg z-10"
        title="AI Assist"
      >
        ✨
      </button>

      {/* Suggestions Panel */}
      {showSuggestions && (
        <div className="absolute top-8 left-0 right-0 mt-2 p-4
                        bg-gradient-to-r from-indigo-50 to-purple-50
                        rounded-lg border border-purple-200 shadow-lg
                        animate-slide-up z-20 min-w-[400px]">
          
          {/* Loading state */}
          {isLoadingSuggestions && (
            <div className="flex items-center space-x-2 py-4">
              <div className="animate-spin w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full" />
              <span className="text-sm text-purple-600">AI is thinking...</span>
            </div>
          )}

          {/* Suggestions */}
          {!isLoadingSuggestions && currentSuggestions.length > 0 && (
            <div className="space-y-3">
              {currentSuggestions.map((suggestion) => (
                <div key={suggestion.id} className="bg-white rounded-md p-3 border border-purple-100">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-800 text-sm">{suggestion.title}</h4>
                    <span className="text-xs text-gray-500">
                      {Math.round(suggestion.confidence * 100)}% confident
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                  
                  {/* Preview of new value */}
                  <div className="bg-gray-50 rounded p-2 mb-2 text-xs text-gray-700 max-h-20 overflow-y-auto">
                    {suggestion.newValue}
                  </div>
                  
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => handleApplySuggestion(suggestion)}
                      className="px-3 py-1 bg-purple-500 text-white text-xs rounded-md 
                               hover:bg-purple-600 transition-colors"
                    >
                      Apply
                    </button>
                    <button 
                      onClick={() => setShowSuggestions(false)}
                      className="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md 
                               hover:bg-gray-300 transition-colors"
                    >
                      Discard
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Custom input */}
          {!isLoadingSuggestions && (
            <div className="mt-3 pt-3 border-t border-purple-200">
              <div className="flex space-x-2">
                <input 
                  value={customRequest}
                  onChange={(e) => setCustomRequest(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Or tell AI what you want to do..."
                  className="flex-1 px-3 py-2 text-sm border border-purple-200 rounded-md 
                           focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                <button
                  onClick={handleCustomRequest}
                  disabled={!customRequest.trim()}
                  className="px-3 py-2 bg-purple-500 text-white text-sm rounded-md 
                           hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed
                           transition-colors"
                >
                  Ask
                </button>
              </div>
            </div>
          )}

          {/* Close button */}
          <button
            onClick={() => setShowSuggestions(false)}
            className="absolute top-2 right-2 w-6 h-6 rounded-full bg-gray-200 
                     hover:bg-gray-300 flex items-center justify-center text-gray-600
                     text-xs transition-colors"
          >
            ×
          </button>
        </div>
      )}
    </>
  )
}
