"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 2
Circuit Breaker Pattern Implementation

This module implements circuit breakers for service protection and
graceful degradation under failure conditions.
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import redis
import json

from monitoring import logger, metrics

# =====================================================
# CIRCUIT BREAKER CONFIGURATION
# =====================================================

CIRCUIT_BREAKER_CONFIG = {
    'failure_threshold': 5,  # Number of failures before opening
    'recovery_timeout_seconds': 60,  # Time to wait before trying again
    'success_threshold': 3,  # Successes needed to close circuit
    'timeout_seconds': 30,  # Request timeout
    'monitoring_window_seconds': 300,  # 5 minutes
    'half_open_max_calls': 3,  # Max calls in half-open state
}

# =====================================================
# CIRCUIT BREAKER STATES
# =====================================================

class CircuitState(str, Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, rejecting calls
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitBreakerMetrics:
    """Circuit breaker metrics"""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    timeout_calls: int = 0
    rejected_calls: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    
    def failure_rate(self) -> float:
        """Calculate failure rate"""
        if self.total_calls == 0:
            return 0.0
        return self.failed_calls / self.total_calls
    
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_calls == 0:
            return 0.0
        return self.successful_calls / self.total_calls

@dataclass
class CircuitBreakerState:
    """Circuit breaker state"""
    name: str
    state: CircuitState
    failure_count: int
    success_count: int
    last_failure_time: Optional[datetime]
    next_attempt_time: Optional[datetime]
    half_open_calls: int
    metrics: CircuitBreakerMetrics
    
    def can_attempt_call(self) -> bool:
        """Check if a call can be attempted"""
        now = datetime.utcnow()
        
        if self.state == CircuitState.CLOSED:
            return True
        elif self.state == CircuitState.OPEN:
            return self.next_attempt_time and now >= self.next_attempt_time
        elif self.state == CircuitState.HALF_OPEN:
            return self.half_open_calls < CIRCUIT_BREAKER_CONFIG['half_open_max_calls']
        
        return False

# =====================================================
# CIRCUIT BREAKER IMPLEMENTATION
# =====================================================

class CircuitBreaker:
    """Circuit breaker for service protection"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = {**CIRCUIT_BREAKER_CONFIG, **(config or {})}
        self.redis_client = self._init_redis()
        
        # Initialize state
        self.state = CircuitBreakerState(
            name=name,
            state=CircuitState.CLOSED,
            failure_count=0,
            success_count=0,
            last_failure_time=None,
            next_attempt_time=None,
            half_open_calls=0,
            metrics=CircuitBreakerMetrics()
        )
        
        # Load state from Redis if available
        asyncio.create_task(self._load_state())
        
        logger.info("Circuit breaker initialized", 
                   name=name, 
                   config=self.config)
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client for state persistence"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
        except Exception as e:
            logger.error("Failed to initialize Redis for circuit breaker", 
                        name=self.name, error=str(e))
            return None
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        if not self.state.can_attempt_call():
            self._record_rejected_call()
            raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is open")
        
        start_time = time.time()
        
        try:
            # Execute function with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs),
                timeout=self.config['timeout_seconds']
            )
            
            # Record success
            execution_time = time.time() - start_time
            await self._record_success(execution_time)
            
            return result
            
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            await self._record_timeout(execution_time)
            raise CircuitBreakerTimeoutError(f"Circuit breaker {self.name} timeout")
            
        except Exception as e:
            execution_time = time.time() - start_time
            await self._record_failure(execution_time, str(e))
            raise
    
    async def _record_success(self, execution_time: float):
        """Record successful call"""
        self.state.metrics.total_calls += 1
        self.state.metrics.successful_calls += 1
        self.state.metrics.last_success_time = datetime.utcnow()
        
        if self.state.state == CircuitState.HALF_OPEN:
            self.state.success_count += 1
            self.state.half_open_calls += 1
            
            # Check if we can close the circuit
            if self.state.success_count >= self.config['success_threshold']:
                await self._close_circuit()
        
        elif self.state.state == CircuitState.CLOSED:
            # Reset failure count on success
            self.state.failure_count = 0
        
        # Record metrics
        metrics.increment_counter('circuit_breaker_calls_total', 
                                {'name': self.name, 'result': 'success'})
        metrics.record_histogram('circuit_breaker_execution_time_seconds', 
                               execution_time, {'name': self.name})
        
        await self._persist_state()
        
        logger.debug("Circuit breaker call succeeded", 
                    name=self.name, 
                    execution_time=execution_time,
                    state=self.state.state.value)
    
    async def _record_failure(self, execution_time: float, error: str):
        """Record failed call"""
        self.state.metrics.total_calls += 1
        self.state.metrics.failed_calls += 1
        self.state.metrics.last_failure_time = datetime.utcnow()
        
        self.state.failure_count += 1
        self.state.last_failure_time = datetime.utcnow()
        
        if self.state.state == CircuitState.HALF_OPEN:
            # Failure in half-open state - go back to open
            await self._open_circuit()
        
        elif self.state.state == CircuitState.CLOSED:
            # Check if we should open the circuit
            if self.state.failure_count >= self.config['failure_threshold']:
                await self._open_circuit()
        
        # Record metrics
        metrics.increment_counter('circuit_breaker_calls_total', 
                                {'name': self.name, 'result': 'failure'})
        metrics.record_histogram('circuit_breaker_execution_time_seconds', 
                               execution_time, {'name': self.name})
        
        await self._persist_state()
        
        logger.warning("Circuit breaker call failed", 
                      name=self.name, 
                      execution_time=execution_time,
                      error=error,
                      failure_count=self.state.failure_count,
                      state=self.state.state.value)
    
    async def _record_timeout(self, execution_time: float):
        """Record timed out call"""
        self.state.metrics.total_calls += 1
        self.state.metrics.timeout_calls += 1
        
        # Treat timeout as failure
        await self._record_failure(execution_time, "timeout")
        
        logger.warning("Circuit breaker call timed out", 
                      name=self.name, 
                      execution_time=execution_time,
                      timeout_seconds=self.config['timeout_seconds'])
    
    def _record_rejected_call(self):
        """Record rejected call"""
        self.state.metrics.rejected_calls += 1
        
        metrics.increment_counter('circuit_breaker_calls_total', 
                                {'name': self.name, 'result': 'rejected'})
        
        logger.debug("Circuit breaker call rejected", 
                    name=self.name, 
                    state=self.state.state.value)
    
    async def _open_circuit(self):
        """Open the circuit breaker"""
        self.state.state = CircuitState.OPEN
        self.state.next_attempt_time = datetime.utcnow() + timedelta(
            seconds=self.config['recovery_timeout_seconds']
        )
        self.state.success_count = 0
        self.state.half_open_calls = 0
        
        metrics.increment_counter('circuit_breaker_state_changes_total', 
                                {'name': self.name, 'new_state': 'open'})
        
        logger.warning("Circuit breaker opened", 
                      name=self.name,
                      failure_count=self.state.failure_count,
                      next_attempt_time=self.state.next_attempt_time.isoformat())
        
        await self._persist_state()
    
    async def _close_circuit(self):
        """Close the circuit breaker"""
        self.state.state = CircuitState.CLOSED
        self.state.failure_count = 0
        self.state.success_count = 0
        self.state.next_attempt_time = None
        self.state.half_open_calls = 0
        
        metrics.increment_counter('circuit_breaker_state_changes_total', 
                                {'name': self.name, 'new_state': 'closed'})
        
        logger.info("Circuit breaker closed", 
                   name=self.name)
        
        await self._persist_state()
    
    async def _transition_to_half_open(self):
        """Transition to half-open state"""
        self.state.state = CircuitState.HALF_OPEN
        self.state.success_count = 0
        self.state.half_open_calls = 0
        
        metrics.increment_counter('circuit_breaker_state_changes_total', 
                                {'name': self.name, 'new_state': 'half_open'})
        
        logger.info("Circuit breaker transitioned to half-open", 
                   name=self.name)
        
        await self._persist_state()
    
    async def _check_state_transition(self):
        """Check if state transition is needed"""
        now = datetime.utcnow()
        
        if (self.state.state == CircuitState.OPEN and 
            self.state.next_attempt_time and 
            now >= self.state.next_attempt_time):
            await self._transition_to_half_open()
    
    async def _persist_state(self):
        """Persist circuit breaker state to Redis"""
        if not self.redis_client:
            return
        
        try:
            state_key = f"circuit_breaker:state:{self.name}"
            state_data = {
                'state_data': json.dumps(asdict(self.state), default=str),
                'updated_at': datetime.utcnow().isoformat()
            }
            
            self.redis_client.hset(state_key, mapping=state_data)
            self.redis_client.expire(state_key, 86400)  # 24 hours
            
        except Exception as e:
            logger.error("Failed to persist circuit breaker state", 
                        name=self.name, error=str(e))
    
    async def _load_state(self):
        """Load circuit breaker state from Redis"""
        if not self.redis_client:
            return
        
        try:
            state_key = f"circuit_breaker:state:{self.name}"
            state_data = self.redis_client.hget(state_key, 'state_data')
            
            if state_data:
                loaded_state = json.loads(state_data)
                
                # Reconstruct state object
                self.state = CircuitBreakerState(
                    name=loaded_state['name'],
                    state=CircuitState(loaded_state['state']),
                    failure_count=loaded_state['failure_count'],
                    success_count=loaded_state['success_count'],
                    last_failure_time=datetime.fromisoformat(loaded_state['last_failure_time']) if loaded_state['last_failure_time'] else None,
                    next_attempt_time=datetime.fromisoformat(loaded_state['next_attempt_time']) if loaded_state['next_attempt_time'] else None,
                    half_open_calls=loaded_state['half_open_calls'],
                    metrics=CircuitBreakerMetrics(**loaded_state['metrics'])
                )
                
                logger.info("Circuit breaker state loaded from Redis", 
                           name=self.name, 
                           state=self.state.state.value)
                
        except Exception as e:
            logger.error("Failed to load circuit breaker state", 
                        name=self.name, error=str(e))
    
    def get_status(self) -> Dict[str, Any]:
        """Get current circuit breaker status"""
        return {
            'name': self.name,
            'state': self.state.state.value,
            'failure_count': self.state.failure_count,
            'success_count': self.state.success_count,
            'last_failure_time': self.state.last_failure_time.isoformat() if self.state.last_failure_time else None,
            'next_attempt_time': self.state.next_attempt_time.isoformat() if self.state.next_attempt_time else None,
            'metrics': asdict(self.state.metrics),
            'config': self.config
        }
    
    async def reset(self):
        """Reset circuit breaker to closed state"""
        await self._close_circuit()
        self.state.metrics = CircuitBreakerMetrics()
        await self._persist_state()
        
        logger.info("Circuit breaker reset", name=self.name)

# =====================================================
# CIRCUIT BREAKER EXCEPTIONS
# =====================================================

class CircuitBreakerError(Exception):
    """Base circuit breaker exception"""
    pass

class CircuitBreakerOpenError(CircuitBreakerError):
    """Circuit breaker is open"""
    pass

class CircuitBreakerTimeoutError(CircuitBreakerError):
    """Circuit breaker timeout"""
    pass

# =====================================================
# CIRCUIT BREAKER MANAGER
# =====================================================

class CircuitBreakerManager:
    """Manages multiple circuit breakers"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.monitoring_task: Optional[asyncio.Task] = None
    
    def get_circuit_breaker(self, name: str, config: Dict[str, Any] = None) -> CircuitBreaker:
        """Get or create circuit breaker"""
        if name not in self.circuit_breakers:
            self.circuit_breakers[name] = CircuitBreaker(name, config)
        return self.circuit_breakers[name]
    
    async def start_monitoring(self):
        """Start monitoring task for state transitions"""
        if self.monitoring_task:
            return
        
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Circuit breaker monitoring started")
    
    async def _monitoring_loop(self):
        """Monitor circuit breakers for state transitions"""
        while True:
            try:
                await asyncio.sleep(10)  # Check every 10 seconds
                
                for cb in self.circuit_breakers.values():
                    await cb._check_state_transition()
                    
            except Exception as e:
                logger.error("Error in circuit breaker monitoring", error=str(e))
    
    def get_all_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all circuit breakers"""
        return {
            name: cb.get_status() 
            for name, cb in self.circuit_breakers.items()
        }
    
    async def shutdown(self):
        """Shutdown circuit breaker manager"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
        
        logger.info("Circuit breaker manager shutdown")

# =====================================================
# GLOBAL INSTANCE
# =====================================================

_circuit_breaker_manager: Optional[CircuitBreakerManager] = None

def get_circuit_breaker_manager() -> CircuitBreakerManager:
    """Get singleton circuit breaker manager"""
    global _circuit_breaker_manager
    if _circuit_breaker_manager is None:
        _circuit_breaker_manager = CircuitBreakerManager()
    return _circuit_breaker_manager

def get_circuit_breaker(name: str, config: Dict[str, Any] = None) -> CircuitBreaker:
    """Get circuit breaker by name"""
    manager = get_circuit_breaker_manager()
    return manager.get_circuit_breaker(name, config)
