-- =====================================================
-- CELER AI ENTERPRISE ARCHITECTURE 3.0 - DATABASE SCHEMA
-- =====================================================
-- This file contains all database schema changes for the
-- enterprise architecture implementation.
-- 
-- IMPORTANT: Run these migrations in order and test thoroughly
-- before applying to production.
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- ENHANCED CONSULTATION STATUS ENUM
-- =====================================================
DO $$ BEGIN
    CREATE TYPE consultation_status_v3 AS ENUM (
        'pending',
        'accepted',
        'processing', 
        'generated',
        'failed',
        'cancelled',
        'approved'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- ENHANCED CONSULTATIONS TABLE WITH VERSIONING
-- =====================================================
CREATE TABLE IF NOT EXISTS consultations_v3 (
    -- Primary identifiers
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    correlation_id UUID NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Status and workflow
    status consultation_status_v3 NOT NULL DEFAULT 'pending',
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    
    -- File references with integrity checks
    primary_audio_url TEXT,
    primary_audio_checksum TEXT,
    additional_audio_urls TEXT[] DEFAULT '{}',
    additional_audio_checksums TEXT[] DEFAULT '{}',
    image_urls TEXT[] DEFAULT '{}',
    image_checksums TEXT[] DEFAULT '{}',
    
    -- Content with versioning
    ai_generated_note_json JSONB,
    edited_note_json JSONB,
    ai_generated_note_text TEXT, -- Backward compatibility
    edited_note_text TEXT, -- Backward compatibility
    
    -- Metadata and configuration
    consultation_type consultation_type NOT NULL DEFAULT 'outpatient',
    patient_name TEXT,
    patient_number TEXT,
    doctor_notes TEXT,
    additional_notes TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- File management
    total_file_size_bytes BIGINT DEFAULT 0,
    file_retention_until TIMESTAMPTZ,
    
    -- Versioning and concurrency control
    version INTEGER NOT NULL DEFAULT 1,
    schema_version TEXT NOT NULL DEFAULT '3.0',
    
    -- Audit and timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id),
    updated_by UUID REFERENCES profiles(id),
    
    -- Performance and integrity constraints
    CONSTRAINT consultations_v3_correlation_id_unique UNIQUE (correlation_id),
    CONSTRAINT consultations_v3_valid_status_transitions CHECK (
        (status = 'pending' AND processing_started_at IS NULL) OR
        (status IN ('accepted', 'processing') AND processing_started_at IS NOT NULL) OR
        (status IN ('generated', 'failed', 'cancelled', 'approved'))
    ),
    CONSTRAINT consultations_v3_file_size_positive CHECK (total_file_size_bytes >= 0),
    CONSTRAINT consultations_v3_version_positive CHECK (version > 0)
);

-- =====================================================
-- CONSULTATION AUDIT TRAIL
-- =====================================================
CREATE TABLE IF NOT EXISTS consultation_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    consultation_id UUID NOT NULL REFERENCES consultations_v3(id) ON DELETE CASCADE,
    correlation_id UUID NOT NULL,
    
    -- Operation details
    operation TEXT NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE', 'STATUS_CHANGE'
    operation_source TEXT NOT NULL, -- 'API', 'WORKER', 'ADMIN', 'SYSTEM'
    
    -- Change tracking
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    
    -- Context
    user_id UUID REFERENCES profiles(id),
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes for performance
    CONSTRAINT consultation_audit_operation_valid CHECK (
        operation IN ('INSERT', 'UPDATE', 'DELETE', 'STATUS_CHANGE', 'FILE_UPLOAD', 'FILE_DELETE')
    )
);

-- =====================================================
-- EVENT SOURCING TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS consultation_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    consultation_id UUID NOT NULL,
    correlation_id UUID NOT NULL,
    
    -- Event details
    event_type TEXT NOT NULL,
    event_version TEXT NOT NULL DEFAULT '1.0',
    event_data JSONB NOT NULL,
    
    -- Metadata
    aggregate_version INTEGER NOT NULL,
    causation_id UUID, -- What caused this event
    correlation_id_chain UUID[], -- Full correlation chain
    
    -- Context
    user_id UUID REFERENCES profiles(id),
    source_system TEXT NOT NULL DEFAULT 'celer-ai',
    
    -- Timestamps
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT consultation_events_aggregate_version_positive CHECK (aggregate_version > 0),
    CONSTRAINT consultation_events_event_type_valid CHECK (
        event_type IN (
            'consultation.created',
            'consultation.accepted', 
            'consultation.processing_started',
            'consultation.files_uploaded',
            'consultation.ai_generation_started',
            'consultation.ai_generation_completed',
            'consultation.ai_generation_failed',
            'consultation.note_edited',
            'consultation.approved',
            'consultation.cancelled'
        )
    )
);

-- =====================================================
-- SAGA STATE MANAGEMENT
-- =====================================================
CREATE TABLE IF NOT EXISTS saga_state (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    saga_type TEXT NOT NULL,
    saga_id UUID NOT NULL UNIQUE,
    correlation_id UUID NOT NULL,
    
    -- State management
    current_step TEXT NOT NULL,
    completed_steps TEXT[] DEFAULT '{}',
    failed_steps TEXT[] DEFAULT '{}',
    compensation_steps TEXT[] DEFAULT '{}',
    
    -- Data
    saga_data JSONB NOT NULL DEFAULT '{}',
    step_data JSONB DEFAULT '{}',
    
    -- Status
    status TEXT NOT NULL DEFAULT 'running',
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Timestamps
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT saga_state_status_valid CHECK (
        status IN ('running', 'completed', 'failed', 'compensating', 'compensated')
    ),
    CONSTRAINT saga_state_retry_count_valid CHECK (retry_count >= 0),
    CONSTRAINT saga_state_max_retries_valid CHECK (max_retries >= 0)
);

-- =====================================================
-- BATCH PROCESSING TRACKING
-- =====================================================
CREATE TABLE IF NOT EXISTS batch_processing (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID NOT NULL UNIQUE,
    
    -- Batch details
    batch_size INTEGER NOT NULL,
    consultation_ids UUID[] NOT NULL,
    correlation_ids UUID[] NOT NULL,
    
    -- Processing status
    status TEXT NOT NULL DEFAULT 'pending',
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Results
    successful_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    failed_consultation_ids UUID[] DEFAULT '{}',
    error_details JSONB DEFAULT '{}',
    
    -- Performance metrics
    processing_duration_ms INTEGER,
    memory_usage_mb INTEGER,
    cpu_usage_percent DECIMAL(5,2),
    
    -- Metadata
    worker_instance TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT batch_processing_status_valid CHECK (
        status IN ('pending', 'processing', 'completed', 'failed', 'partial_success')
    ),
    CONSTRAINT batch_processing_batch_size_positive CHECK (batch_size > 0),
    CONSTRAINT batch_processing_counts_valid CHECK (
        successful_count >= 0 AND failed_count >= 0 AND
        successful_count + failed_count <= batch_size
    )
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Consultations table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultations_v3_user_status_created 
    ON consultations_v3 (user_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultations_v3_correlation_id 
    ON consultations_v3 (correlation_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultations_v3_status_created 
    ON consultations_v3 (status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultations_v3_processing_times 
    ON consultations_v3 (processing_started_at, processing_completed_at) 
    WHERE status IN ('processing', 'generated');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultations_v3_file_retention 
    ON consultations_v3 (file_retention_until) 
    WHERE file_retention_until IS NOT NULL;

-- Audit table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultation_audit_consultation_timestamp 
    ON consultation_audit (consultation_id, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultation_audit_correlation_id 
    ON consultation_audit (correlation_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultation_audit_user_timestamp 
    ON consultation_audit (user_id, timestamp DESC) 
    WHERE user_id IS NOT NULL;

-- Event sourcing indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultation_events_consultation_version 
    ON consultation_events (consultation_id, aggregate_version);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultation_events_correlation_timestamp 
    ON consultation_events (correlation_id, timestamp);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consultation_events_type_timestamp 
    ON consultation_events (event_type, timestamp DESC);

-- Saga state indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_saga_state_correlation_status 
    ON saga_state (correlation_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_saga_state_expires_at 
    ON saga_state (expires_at) 
    WHERE expires_at IS NOT NULL AND status = 'running';

-- Batch processing indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_batch_processing_status_created 
    ON batch_processing (status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_batch_processing_consultation_ids 
    ON batch_processing USING GIN (consultation_ids);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
CREATE TRIGGER update_consultations_v3_updated_at 
    BEFORE UPDATE ON consultations_v3 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_saga_state_updated_at 
    BEFORE UPDATE ON saga_state 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_consultation_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO consultation_audit (
            consultation_id, correlation_id, operation, operation_source,
            new_values, user_id, metadata
        ) VALUES (
            NEW.id, NEW.correlation_id, 'INSERT', 'SYSTEM',
            to_jsonb(NEW), NEW.created_by, 
            jsonb_build_object('trigger', 'audit_consultation_changes')
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO consultation_audit (
            consultation_id, correlation_id, operation, operation_source,
            old_values, new_values, user_id, metadata
        ) VALUES (
            NEW.id, NEW.correlation_id, 'UPDATE', 'SYSTEM',
            to_jsonb(OLD), to_jsonb(NEW), NEW.updated_by,
            jsonb_build_object('trigger', 'audit_consultation_changes')
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO consultation_audit (
            consultation_id, correlation_id, operation, operation_source,
            old_values, metadata
        ) VALUES (
            OLD.id, OLD.correlation_id, 'DELETE', 'SYSTEM',
            to_jsonb(OLD),
            jsonb_build_object('trigger', 'audit_consultation_changes')
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit trigger
CREATE TRIGGER audit_consultations_v3_changes
    AFTER INSERT OR UPDATE OR DELETE ON consultations_v3
    FOR EACH ROW EXECUTE FUNCTION audit_consultation_changes();

-- =====================================================
-- BULK OPERATION STORED PROCEDURES
-- =====================================================

-- Atomic batch insert with conflict resolution
CREATE OR REPLACE FUNCTION bulk_insert_consultations(
    consultations_json JSONB
) RETURNS TABLE(id UUID, status TEXT, correlation_id UUID) AS $$
DECLARE
    consultation JSONB;
    inserted_count INTEGER := 0;
BEGIN
    -- Create temporary table for batch processing
    CREATE TEMP TABLE temp_consultations (
        id UUID,
        correlation_id UUID,
        user_id UUID,
        status consultation_status_v3,
        consultation_type consultation_type,
        patient_name TEXT,
        doctor_notes TEXT,
        additional_notes TEXT,
        primary_audio_url TEXT,
        additional_audio_urls TEXT[],
        image_urls TEXT[],
        metadata JSONB,
        created_by UUID,
        total_file_size_bytes BIGINT
    ) ON COMMIT DROP;

    -- Parse JSON and insert into temp table
    FOR consultation IN SELECT * FROM jsonb_array_elements(consultations_json)
    LOOP
        INSERT INTO temp_consultations (
            id, correlation_id, user_id, status, consultation_type,
            patient_name, doctor_notes, additional_notes,
            primary_audio_url, additional_audio_urls, image_urls,
            metadata, created_by, total_file_size_bytes
        ) VALUES (
            (consultation->>'id')::UUID,
            (consultation->>'correlation_id')::UUID,
            (consultation->>'user_id')::UUID,
            (consultation->>'status')::consultation_status_v3,
            COALESCE((consultation->>'consultation_type')::consultation_type, 'outpatient'),
            consultation->>'patient_name',
            consultation->>'doctor_notes',
            consultation->>'additional_notes',
            consultation->>'primary_audio_url',
            CASE
                WHEN consultation->'additional_audio_urls' IS NOT NULL
                THEN ARRAY(SELECT jsonb_array_elements_text(consultation->'additional_audio_urls'))
                ELSE '{}'::TEXT[]
            END,
            CASE
                WHEN consultation->'image_urls' IS NOT NULL
                THEN ARRAY(SELECT jsonb_array_elements_text(consultation->'image_urls'))
                ELSE '{}'::TEXT[]
            END,
            COALESCE(consultation->'metadata', '{}'::JSONB),
            (consultation->>'created_by')::UUID,
            COALESCE((consultation->>'total_file_size_bytes')::BIGINT, 0)
        );
    END LOOP;

    -- Atomic insert with conflict resolution
    INSERT INTO consultations_v3 (
        id, correlation_id, user_id, status, consultation_type,
        patient_name, doctor_notes, additional_notes,
        primary_audio_url, additional_audio_urls, image_urls,
        metadata, created_by, total_file_size_bytes, processing_started_at
    )
    SELECT
        tc.id, tc.correlation_id, tc.user_id, tc.status, tc.consultation_type,
        tc.patient_name, tc.doctor_notes, tc.additional_notes,
        tc.primary_audio_url, tc.additional_audio_urls, tc.image_urls,
        tc.metadata, tc.created_by, tc.total_file_size_bytes,
        CASE WHEN tc.status IN ('accepted', 'processing') THEN NOW() ELSE NULL END
    FROM temp_consultations tc
    ON CONFLICT (correlation_id) DO UPDATE SET
        status = EXCLUDED.status,
        updated_at = NOW(),
        version = consultations_v3.version + 1
    RETURNING consultations_v3.id, consultations_v3.status::TEXT, consultations_v3.correlation_id;

    GET DIAGNOSTICS inserted_count = ROW_COUNT;
    RAISE NOTICE 'Bulk inserted/updated % consultations', inserted_count;
END;
$$ LANGUAGE plpgsql;

-- Atomic batch update with optimistic locking
CREATE OR REPLACE FUNCTION bulk_update_consultations(
    updates_json JSONB
) RETURNS TABLE(id UUID, status TEXT, version INTEGER, updated BOOLEAN) AS $$
DECLARE
    update_record JSONB;
    updated_count INTEGER := 0;
    current_consultation consultations_v3%ROWTYPE;
BEGIN
    FOR update_record IN SELECT * FROM jsonb_array_elements(updates_json)
    LOOP
        -- Get current consultation for optimistic locking
        SELECT * INTO current_consultation
        FROM consultations_v3
        WHERE consultations_v3.id = (update_record->>'id')::UUID;

        IF NOT FOUND THEN
            -- Return record indicating not found
            RETURN QUERY SELECT
                (update_record->>'id')::UUID,
                'not_found'::TEXT,
                0::INTEGER,
                FALSE::BOOLEAN;
            CONTINUE;
        END IF;

        -- Check optimistic lock if version provided
        IF update_record ? 'expected_version' AND
           current_consultation.version != (update_record->>'expected_version')::INTEGER THEN
            -- Return record indicating version conflict
            RETURN QUERY SELECT
                current_consultation.id,
                'version_conflict'::TEXT,
                current_consultation.version,
                FALSE::BOOLEAN;
            CONTINUE;
        END IF;

        -- Perform update
        UPDATE consultations_v3 SET
            ai_generated_note_json = COALESCE(
                (update_record->>'ai_generated_note_json')::JSONB,
                ai_generated_note_json
            ),
            edited_note_json = COALESCE(
                (update_record->>'edited_note_json')::JSONB,
                edited_note_json
            ),
            status = COALESCE(
                (update_record->>'status')::consultation_status_v3,
                status
            ),
            processing_completed_at = CASE
                WHEN (update_record->>'status')::consultation_status_v3 IN ('generated', 'failed')
                THEN NOW()
                ELSE processing_completed_at
            END,
            updated_at = NOW(),
            version = version + 1,
            updated_by = (update_record->>'updated_by')::UUID
        WHERE consultations_v3.id = (update_record->>'id')::UUID
        RETURNING consultations_v3.id, consultations_v3.status::TEXT, consultations_v3.version, TRUE;

        IF FOUND THEN
            updated_count := updated_count + 1;
        END IF;
    END LOOP;

    RAISE NOTICE 'Bulk updated % consultations', updated_count;
END;
$$ LANGUAGE plpgsql;

-- Get consultation statistics with performance optimization
CREATE OR REPLACE FUNCTION get_consultation_stats(
    p_user_id UUID,
    p_date_from TIMESTAMPTZ DEFAULT NULL,
    p_date_to TIMESTAMPTZ DEFAULT NULL
) RETURNS TABLE(
    total_consultations BIGINT,
    pending_consultations BIGINT,
    processing_consultations BIGINT,
    generated_consultations BIGINT,
    failed_consultations BIGINT,
    approved_consultations BIGINT,
    avg_processing_time_seconds NUMERIC,
    total_file_size_mb NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_consultations,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_consultations,
        COUNT(*) FILTER (WHERE status = 'processing') as processing_consultations,
        COUNT(*) FILTER (WHERE status = 'generated') as generated_consultations,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_consultations,
        COUNT(*) FILTER (WHERE status = 'approved') as approved_consultations,
        AVG(
            EXTRACT(EPOCH FROM (processing_completed_at - processing_started_at))
        ) FILTER (WHERE processing_completed_at IS NOT NULL AND processing_started_at IS NOT NULL) as avg_processing_time_seconds,
        ROUND(SUM(total_file_size_bytes) / (1024.0 * 1024.0), 2) as total_file_size_mb
    FROM consultations_v3
    WHERE user_id = p_user_id
        AND (p_date_from IS NULL OR created_at >= p_date_from)
        AND (p_date_to IS NULL OR created_at <= p_date_to);
END;
$$ LANGUAGE plpgsql;

-- Cleanup orphaned files and expired consultations
CREATE OR REPLACE FUNCTION cleanup_orphaned_files(
    older_than INTERVAL DEFAULT '7 days'
) RETURNS TABLE(
    deleted_consultations INTEGER,
    orphaned_files TEXT[]
) AS $$
DECLARE
    deleted_count INTEGER := 0;
    orphaned_file_urls TEXT[] := '{}';
    consultation_record consultations_v3%ROWTYPE;
BEGIN
    -- Find consultations with expired file retention
    FOR consultation_record IN
        SELECT * FROM consultations_v3
        WHERE file_retention_until IS NOT NULL
        AND file_retention_until < NOW()
    LOOP
        -- Collect file URLs for cleanup
        IF consultation_record.primary_audio_url IS NOT NULL THEN
            orphaned_file_urls := array_append(orphaned_file_urls, consultation_record.primary_audio_url);
        END IF;

        orphaned_file_urls := orphaned_file_urls || consultation_record.additional_audio_urls;
        orphaned_file_urls := orphaned_file_urls || consultation_record.image_urls;

        -- Delete the consultation (audit trail will be preserved)
        DELETE FROM consultations_v3 WHERE id = consultation_record.id;
        deleted_count := deleted_count + 1;
    END LOOP;

    -- Also find failed consultations older than specified interval
    FOR consultation_record IN
        SELECT * FROM consultations_v3
        WHERE status = 'failed'
        AND created_at < (NOW() - older_than)
    LOOP
        -- Collect file URLs for cleanup
        IF consultation_record.primary_audio_url IS NOT NULL THEN
            orphaned_file_urls := array_append(orphaned_file_urls, consultation_record.primary_audio_url);
        END IF;

        orphaned_file_urls := orphaned_file_urls || consultation_record.additional_audio_urls;
        orphaned_file_urls := orphaned_file_urls || consultation_record.image_urls;

        -- Delete the consultation
        DELETE FROM consultations_v3 WHERE id = consultation_record.id;
        deleted_count := deleted_count + 1;
    END LOOP;

    RETURN QUERY SELECT deleted_count, orphaned_file_urls;

    RAISE NOTICE 'Cleaned up % consultations and % orphaned files',
        deleted_count, array_length(orphaned_file_urls, 1);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MIGRATION UTILITIES
-- =====================================================

-- Migrate existing consultations to v3 schema
CREATE OR REPLACE FUNCTION migrate_consultations_to_v3()
RETURNS TABLE(migrated_count INTEGER, error_count INTEGER) AS $$
DECLARE
    consultation_record RECORD;
    migrated INTEGER := 0;
    errors INTEGER := 0;
BEGIN
    -- Check if old consultations table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'consultations') THEN
        RAISE NOTICE 'No existing consultations table found. Migration not needed.';
        RETURN QUERY SELECT 0, 0;
        RETURN;
    END IF;

    -- Migrate existing consultations
    FOR consultation_record IN
        SELECT * FROM consultations
        WHERE NOT EXISTS (
            SELECT 1 FROM consultations_v3
            WHERE consultations_v3.id = consultations.id
        )
    LOOP
        BEGIN
            INSERT INTO consultations_v3 (
                id, user_id, status, primary_audio_url, additional_audio_urls,
                image_urls, ai_generated_note_json, edited_note_json,
                consultation_type, patient_name, patient_number,
                doctor_notes, additional_notes, total_file_size_bytes,
                file_retention_until, created_at, updated_at,
                correlation_id, schema_version
            ) VALUES (
                consultation_record.id,
                consultation_record.doctor_id, -- Map doctor_id to user_id
                consultation_record.status::consultation_status_v3,
                consultation_record.primary_audio_url,
                consultation_record.additional_audio_urls,
                consultation_record.image_urls,
                consultation_record.ai_generated_note_json,
                consultation_record.edited_note_json,
                consultation_record.consultation_type,
                consultation_record.patient_name,
                consultation_record.patient_number,
                consultation_record.doctor_notes,
                consultation_record.additional_notes,
                consultation_record.total_file_size_bytes,
                consultation_record.file_retention_until,
                consultation_record.created_at,
                consultation_record.updated_at,
                COALESCE(consultation_record.id, gen_random_uuid()), -- Use ID as correlation_id if not exists
                '3.0'
            );

            migrated := migrated + 1;

        EXCEPTION WHEN OTHERS THEN
            errors := errors + 1;
            RAISE NOTICE 'Failed to migrate consultation %: %', consultation_record.id, SQLERRM;
        END;
    END LOOP;

    RETURN QUERY SELECT migrated, errors;

    RAISE NOTICE 'Migration completed: % migrated, % errors', migrated, errors;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PERFORMANCE MONITORING VIEWS
-- =====================================================

-- View for consultation processing performance
CREATE OR REPLACE VIEW consultation_performance_stats AS
SELECT
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as total_consultations,
    COUNT(*) FILTER (WHERE status = 'generated') as successful_consultations,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_consultations,
    AVG(EXTRACT(EPOCH FROM (processing_completed_at - processing_started_at)))
        FILTER (WHERE processing_completed_at IS NOT NULL AND processing_started_at IS NOT NULL)
        as avg_processing_time_seconds,
    PERCENTILE_CONT(0.95) WITHIN GROUP (
        ORDER BY EXTRACT(EPOCH FROM (processing_completed_at - processing_started_at))
    ) FILTER (WHERE processing_completed_at IS NOT NULL AND processing_started_at IS NOT NULL)
        as p95_processing_time_seconds,
    SUM(total_file_size_bytes) / (1024 * 1024) as total_file_size_mb
FROM consultations_v3
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;

-- View for batch processing performance
CREATE OR REPLACE VIEW batch_performance_stats AS
SELECT
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as total_batches,
    AVG(batch_size) as avg_batch_size,
    AVG(processing_duration_ms) as avg_processing_duration_ms,
    AVG(successful_count::DECIMAL / batch_size * 100) as avg_success_rate_percent,
    SUM(successful_count) as total_successful_consultations,
    SUM(failed_count) as total_failed_consultations
FROM batch_processing
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;

-- =====================================================
-- SECURITY AND PERMISSIONS
-- =====================================================

-- Grant appropriate permissions to application roles
-- Note: Adjust role names based on your actual database roles

-- GRANT SELECT, INSERT, UPDATE ON consultations_v3 TO celer_app_role;
-- GRANT SELECT, INSERT ON consultation_audit TO celer_app_role;
-- GRANT SELECT, INSERT, UPDATE ON consultation_events TO celer_app_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON saga_state TO celer_app_role;
-- GRANT SELECT, INSERT, UPDATE ON batch_processing TO celer_app_role;

-- GRANT EXECUTE ON FUNCTION bulk_insert_consultations(JSONB) TO celer_app_role;
-- GRANT EXECUTE ON FUNCTION bulk_update_consultations(JSONB) TO celer_app_role;
-- GRANT EXECUTE ON FUNCTION get_consultation_stats(UUID, TIMESTAMPTZ, TIMESTAMPTZ) TO celer_app_role;
-- GRANT EXECUTE ON FUNCTION cleanup_orphaned_files(INTERVAL) TO celer_cleanup_role;

-- =====================================================
-- COMPLETION NOTICE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'CELER AI ENTERPRISE ARCHITECTURE 3.0 DATABASE SCHEMA';
    RAISE NOTICE 'Schema installation completed successfully!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run migration: SELECT * FROM migrate_consultations_to_v3();';
    RAISE NOTICE '2. Update application connection strings';
    RAISE NOTICE '3. Deploy new application code';
    RAISE NOTICE '4. Monitor performance views';
    RAISE NOTICE '=================================================';
END $$;
