#!/bin/bash

# =====================================================
# GOOGLE CLOUD RESOURCES SETUP FOR ENTERPRISE V3
# =====================================================

set -e

echo "🚀 Setting up Google Cloud resources for Celer AI Enterprise v3..."

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT_ID:-"your-project-id"}
REGION="asia-south1"
TOPIC_NAME="consultation-processing"
SUBSCRIPTION_NAME="consultation-processing-sub"
DEAD_LETTER_TOPIC="consultation-processing-dlq"
DEAD_LETTER_SUBSCRIPTION="consultation-processing-dlq-sub"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    log_error "gcloud CLI not found. Please install Google Cloud SDK"
    exit 1
fi

# Set project
log_info "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
log_info "Enabling required APIs..."
gcloud services enable pubsub.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Create Pub/Sub topics
log_info "Creating Pub/Sub topics..."
gcloud pubsub topics create $TOPIC_NAME --quiet || log_warning "Topic $TOPIC_NAME already exists"
gcloud pubsub topics create $DEAD_LETTER_TOPIC --quiet || log_warning "Dead letter topic already exists"

# Create subscriptions with enterprise configuration
log_info "Creating Pub/Sub subscriptions..."
gcloud pubsub subscriptions create $SUBSCRIPTION_NAME \
    --topic=$TOPIC_NAME \
    --ack-deadline=600 \
    --max-outstanding-messages=100 \
    --max-outstanding-bytes=104857600 \
    --dead-letter-topic=$DEAD_LETTER_TOPIC \
    --max-delivery-attempts=5 \
    --quiet || log_warning "Subscription already exists"

gcloud pubsub subscriptions create $DEAD_LETTER_SUBSCRIPTION \
    --topic=$DEAD_LETTER_TOPIC \
    --quiet || log_warning "Dead letter subscription already exists"

# Create Redis instance
log_info "Creating Redis instance..."
gcloud redis instances create celer-ai-redis \
    --size=1 \
    --region=$REGION \
    --redis-version=redis_6_x \
    --quiet || log_warning "Redis instance already exists"

# Get Redis connection info
log_info "Getting Redis connection details..."
REDIS_HOST=$(gcloud redis instances describe celer-ai-redis --region=$REGION --format="value(host)")
REDIS_PORT=$(gcloud redis instances describe celer-ai-redis --region=$REGION --format="value(port)")

# Create service account for Cloud Run
log_info "Creating service account..."
gcloud iam service-accounts create celer-ai-worker \
    --display-name="Celer AI Worker Service Account" \
    --quiet || log_warning "Service account already exists"

# Grant necessary permissions
log_info "Granting permissions..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:celer-ai-worker@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/pubsub.subscriber"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:celer-ai-worker@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/redis.editor"

# Deploy Cloud Run service
log_info "Deploying Cloud Run service..."
cd python-backend

gcloud run deploy celer-ai-worker \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 1Gi \
    --cpu 1 \
    --concurrency 10 \
    --max-instances 100 \
    --timeout 900 \
    --service-account celer-ai-worker@$PROJECT_ID.iam.gserviceaccount.com \
    --set-env-vars "ENVIRONMENT=production,GOOGLE_CLOUD_PROJECT_ID=$PROJECT_ID,PUBSUB_SUBSCRIPTION_NAME=$SUBSCRIPTION_NAME,REDIS_HOST=$REDIS_HOST,REDIS_PORT=$REDIS_PORT" \
    --quiet

cd ..

log_info "✅ Google Cloud resources setup completed!"
echo ""
echo "📋 Resource Summary:"
echo "  🔔 Pub/Sub Topic: $TOPIC_NAME"
echo "  📥 Subscription: $SUBSCRIPTION_NAME"
echo "  💀 Dead Letter Topic: $DEAD_LETTER_TOPIC"
echo "  🗄️ Redis Host: $REDIS_HOST:$REDIS_PORT"
echo "  ☁️ Cloud Run: celer-ai-worker"
echo ""
echo "🔧 Next Steps:"
echo "  1. Update your .env files with the Redis connection details"
echo "  2. Set GOOGLE_CLOUD_PROJECT_ID=$PROJECT_ID in your environment"
echo "  3. Test the deployment with the frontend"
