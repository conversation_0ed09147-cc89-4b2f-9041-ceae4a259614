/**
 * ENTERPRISE ARCHITECTURE 3.0 - CENTRALIZED PUBSUB SERVICE
 * 
 * CEO/Guardian Approach: Centralized, scalable, build-time safe PubSub service
 * Following Google Cloud 2025 July best practices
 * 
 * Features:
 * - Singleton pattern with lazy initialization
 * - Build-time safety (no module-level initialization)
 * - Centralized error handling and retry logic
 * - Clean interface abstracting Google Cloud complexity
 * - Easy testing and mocking
 * - Correlation ID tracking
 */

// Dynamic import to avoid build-time execution
type PubSub = any
type Topic = any

// =====================================================
// CENTRALIZED CONFIGURATION
// =====================================================

const PUBSUB_CONFIG = {
  topics: {
    consultation_processing: process.env.PUBSUB_TOPIC_NAME || 'consultation-processing',
    consultation_updates: 'consultation-updates',
    dead_letter: 'consultation-dlq'
  },
  retry: {
    maxAttempts: 3,
    initialDelayMs: 1000,
    maxDelayMs: 10000,
    multiplier: 2.0
  },
  timeouts: {
    publishTimeoutMs: 30000,
    healthCheckTimeoutMs: 5000
  },
  circuitBreaker: {
    failureThreshold: 5,
    resetTimeoutMs: 60000 // 1 minute
  }
}

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface PublishOptions {
  correlationId?: string
  orderingKey?: string
  attributes?: Record<string, string>
  retryAttempts?: number
}

export interface PublishResult {
  success: boolean
  messageId?: string
  error?: string
  correlationId: string
  publishedAt: Date
}

export interface HealthStatus {
  healthy: boolean
  error?: string
  topicExists?: boolean
  timestamp: Date
  connectionStatus?: 'connected' | 'disconnected' | 'retrying' | 'circuit_open'
  lastError?: string
  retryCount?: number
}

// =====================================================
// CENTRALIZED PUBSUB SERVICE
// =====================================================

class PubSubService {
  private static instance: PubSubService | null = null
  private client: PubSub | null = null
  private topics: Map<string, Topic> = new Map()
  private isInitialized = false

  // Resilient error handling state
  private connectionStatus: 'connected' | 'disconnected' | 'retrying' | 'circuit_open' = 'disconnected'
  private retryCount = 0
  private lastError: string | null = null
  private circuitBreakerOpenUntil: number | null = null

  private constructor() {
    // Private constructor for singleton
  }

  /**
   * Get singleton instance
   */
  static getInstance(): PubSubService {
    if (!PubSubService.instance) {
      PubSubService.instance = new PubSubService()
    }
    return PubSubService.instance
  }

  /**
   * Check if circuit breaker is open
   */
  private isCircuitBreakerOpen(): boolean {
    if (this.circuitBreakerOpenUntil === null) return false

    if (Date.now() > this.circuitBreakerOpenUntil) {
      // Reset circuit breaker
      this.circuitBreakerOpenUntil = null
      this.retryCount = 0
      this.connectionStatus = 'disconnected'
      console.log('🔄 Circuit breaker reset - attempting reconnection')
      return false
    }

    return true
  }

  /**
   * Check if we're in build time environment
   */
  private isBuildTime(): boolean {
    // Multiple ways to detect build time
    return (
      typeof window === 'undefined' && // Server-side
      (
        process.env.NODE_ENV === 'production' && // Production build
        !process.env.GOOGLE_CLOUD_PROJECT_ID || // Missing runtime env vars
        process.env.NEXT_PHASE === 'phase-production-build' || // Next.js build phase
        process.argv.includes('build') // Build command
      )
    )
  }

  /**
   * Initialize PubSub client with resilient error handling
   */
  private async initialize(): Promise<void> {
    if (this.isInitialized && this.connectionStatus === 'connected') return

    // Skip initialization during build time
    if (this.isBuildTime()) {
      console.log('🔧 Skipping PubSub initialization during build time')
      this.connectionStatus = 'disconnected'
      throw new Error('PubSub not available during build time')
    }

    // Check circuit breaker
    if (this.isCircuitBreakerOpen()) {
      throw new Error(`Circuit breaker is open. Retry after ${new Date(this.circuitBreakerOpenUntil!).toISOString()}`)
    }

    try {
      this.connectionStatus = 'retrying'

      // Check if we're in build time (missing required env vars)
      const projectId = process.env.GOOGLE_CLOUD_PROJECT_ID
      if (!projectId) {
        throw new Error('GOOGLE_CLOUD_PROJECT_ID environment variable is required')
      }

      // Dynamic import to avoid build-time execution
      const { PubSub } = await import('@google-cloud/pubsub')

      // Initialize client following 2025 best practices
      this.client = new PubSub({ projectId })
      this.isInitialized = true
      this.connectionStatus = 'connected'
      this.retryCount = 0
      this.lastError = null

      console.log('🚀 PubSub Service initialized successfully')

    } catch (error) {
      this.retryCount++
      this.lastError = error instanceof Error ? error.message : 'Unknown error'
      this.connectionStatus = 'disconnected'

      console.error(`❌ Failed to initialize PubSub Service (attempt ${this.retryCount}):`, error)

      // Open circuit breaker if too many failures
      if (this.retryCount >= PUBSUB_CONFIG.circuitBreaker.failureThreshold) {
        this.circuitBreakerOpenUntil = Date.now() + PUBSUB_CONFIG.circuitBreaker.resetTimeoutMs
        this.connectionStatus = 'circuit_open'
        console.error(`🚨 Circuit breaker opened due to ${this.retryCount} consecutive failures`)
      }

      throw error
    }
  }

  /**
   * Get or create topic (with caching)
   */
  private async getTopic(topicName: string): Promise<Topic> {
    await this.initialize()
    
    if (!this.client) {
      throw new Error('PubSub client not initialized')
    }

    // Return cached topic if exists
    if (this.topics.has(topicName)) {
      return this.topics.get(topicName)!
    }

    // Create topic reference and cache it
    const topic = this.client.topic(topicName)
    this.topics.set(topicName, topic)
    
    return topic
  }

  /**
   * Publish message to topic with retry logic
   */
  async publishMessage(
    topicName: string,
    data: any,
    options: PublishOptions = {}
  ): Promise<PublishResult> {
    const correlationId = options.correlationId || crypto.randomUUID()
    const publishedAt = new Date()
    const maxAttempts = options.retryAttempts || PUBSUB_CONFIG.retry.maxAttempts

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const topic = await this.getTopic(topicName)

        // Prepare message data
        const messageData = {
          ...data,
          correlation_id: correlationId,
          published_at: publishedAt.toISOString(),
          schema_version: '1.0'
        }

        // Prepare attributes
        const attributes = {
          correlation_id: correlationId,
          event_type: data.event_type || 'unknown',
          schema_version: '1.0',
          retry_attempt: attempt.toString(),
          ...options.attributes
        }

        // Publish message with timeout
        const publishPromise = topic.publishMessage({
          data: Buffer.from(JSON.stringify(messageData)),
          attributes,
          orderingKey: options.orderingKey
        })

        const messageId = await Promise.race([
          publishPromise,
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Publish timeout')), PUBSUB_CONFIG.timeouts.publishTimeoutMs)
          )
        ])

        console.log(`✅ Message published to ${topicName}: ${messageId} (attempt ${attempt})`, { correlationId })

        return {
          success: true,
          messageId,
          correlationId,
          publishedAt
        }

      } catch (error) {
        console.error(`❌ Failed to publish to ${topicName} (attempt ${attempt}/${maxAttempts}):`, error, { correlationId })

        // If this is the last attempt, return failure
        if (attempt === maxAttempts) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            correlationId,
            publishedAt
          }
        }

        // Wait before retry with exponential backoff
        const delay = Math.min(
          PUBSUB_CONFIG.retry.initialDelayMs * Math.pow(PUBSUB_CONFIG.retry.multiplier, attempt - 1),
          PUBSUB_CONFIG.retry.maxDelayMs
        )

        console.log(`⏳ Retrying publish in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    // This should never be reached, but TypeScript requires it
    return {
      success: false,
      error: 'Max retry attempts exceeded',
      correlationId,
      publishedAt
    }
  }

  /**
   * Check topic health
   */
  async checkTopicHealth(topicName: string): Promise<HealthStatus> {
    const timestamp = new Date()

    try {
      const topic = await this.getTopic(topicName)
      const [exists] = await topic.exists()

      return {
        healthy: exists,
        topicExists: exists,
        timestamp,
        ...(exists ? {} : { error: `Topic '${topicName}' does not exist` })
      }

    } catch (error) {
      return {
        healthy: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp
      }
    }
  }

  /**
   * Create topic if it doesn't exist
   */
  async ensureTopicExists(topicName: string): Promise<boolean> {
    try {
      const topic = await this.getTopic(topicName)
      const [exists] = await topic.exists()

      if (!exists) {
        console.log(`📝 Creating topic: ${topicName}`)
        await topic.create()
        console.log(`✅ Topic created: ${topicName}`)
      }

      return true
    } catch (error) {
      console.error(`❌ Failed to ensure topic exists: ${topicName}`, error)
      return false
    }
  }

  /**
   * Get comprehensive service health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    const timestamp = new Date()

    try {
      await this.initialize()
      return {
        healthy: this.isInitialized && this.connectionStatus === 'connected',
        timestamp,
        connectionStatus: this.connectionStatus,
        retryCount: this.retryCount,
        lastError: this.lastError || undefined
      }
    } catch (error) {
      return {
        healthy: false,
        error: error instanceof Error ? error.message : 'Service initialization failed',
        timestamp,
        connectionStatus: this.connectionStatus,
        retryCount: this.retryCount,
        lastError: this.lastError || undefined
      }
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    if (this.client) {
      await this.client.close()
      this.client = null
      this.isInitialized = false
      this.topics.clear()
      console.log('🔄 PubSub Service shutdown complete')
    }
  }
}

// =====================================================
// SINGLETON EXPORT
// =====================================================

export const pubsubService = PubSubService.getInstance()

// =====================================================
// CONVENIENCE FUNCTIONS
// =====================================================

/**
 * Publish consultation creation event
 */
export async function publishConsultationCreate(
  consultationData: any,
  correlationId?: string
): Promise<PublishResult> {
  return pubsubService.publishMessage(PUBSUB_CONFIG.topics.consultation_processing, {
    event_type: 'consultation.create',
    ...consultationData
  }, {
    correlationId,
    orderingKey: consultationData.consultation_id
  })
}

/**
 * Check consultation processing topic health
 */
export async function checkConsultationTopicHealth(): Promise<HealthStatus> {
  return pubsubService.checkTopicHealth(PUBSUB_CONFIG.topics.consultation_processing)
}

/**
 * Get centralized configuration (for debugging/monitoring)
 */
export function getPubSubConfig() {
  return { ...PUBSUB_CONFIG }
}
