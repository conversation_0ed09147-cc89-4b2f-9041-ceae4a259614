import { useState, useRef, useEffect, useCallback } from 'react'

export interface UseRecordingReturn {
  // State
  isRecording: boolean
  isPaused: boolean
  recordingDuration: number
  audioBlob: Blob | null
  audioFile: File | null
  
  // Actions
  startRecording: () => Promise<void>
  pauseRecording: () => void
  stopRecording: () => void
  clearAudio: () => void
  
  // Status
  isSupported: boolean
  error: string | null
}

/**
 * Pure recording hook with no side effects
 * Manages MediaRecorder state and logic only
 * Following CTO's specification: no autosave or external dependencies
 */
export function useRecording(): UseRecordingReturn {
  // Recording state
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Refs for MediaRecorder management
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Check if MediaRecorder is supported
  const isSupported = typeof window !== 'undefined' && 'MediaRecorder' in window

  // Recording timer effect
  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1)
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRecording, isPaused])

  // Get the best supported audio format for this device
  const getSupportedMimeType = useCallback(() => {
    if (!isSupported) return 'audio/webm'
    
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg'
    ]
    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm'
  }, [isSupported])

  // Start recording function
  const startRecording = useCallback(async () => {
    if (!isSupported) {
      setError('MediaRecorder is not supported in this browser')
      return
    }

    try {
      setError(null)
      
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
        }
      })

      streamRef.current = stream

      const mimeType = getSupportedMimeType()
      const mediaRecorder = new MediaRecorder(stream, { mimeType })
      mediaRecorderRef.current = mediaRecorder

      const chunks: Blob[] = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType })

        // Generate appropriate file extension
        const getFileExtension = (mimeType: string) => {
          if (mimeType.includes('webm')) return 'webm'
          if (mimeType.includes('mp4')) return 'mp4'
          if (mimeType.includes('mpeg')) return 'mp3'
          return 'webm'
        }

        const extension = getFileExtension(mimeType)
        const file = new File([blob], `recording_${Date.now()}.${extension}`, {
          type: mimeType
        })

        setAudioBlob(blob)
        setAudioFile(file)
        setIsRecording(false)
        setIsPaused(false)

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event)
        setError('Recording failed')
        setIsRecording(false)
        setIsPaused(false)
      }

      mediaRecorder.start()
      setIsRecording(true)
      setIsPaused(false)
      setRecordingDuration(0)

    } catch (error) {
      console.error('Recording error:', error)
      setError('Failed to start recording. Please check microphone permissions.')
      setIsRecording(false)
      setIsPaused(false)
    }
  }, [isSupported, getSupportedMimeType])

  // Pause/resume recording function
  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume()
        setIsPaused(false)
      } else {
        mediaRecorderRef.current.pause()
        setIsPaused(true)
      }
    }
  }, [isRecording, isPaused])

  // Stop recording function
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
    }
  }, [isRecording])

  // Clear audio function
  const clearAudio = useCallback(() => {
    setAudioBlob(null)
    setAudioFile(null)
    setRecordingDuration(0)
    setError(null)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  return {
    // State
    isRecording,
    isPaused,
    recordingDuration,
    audioBlob,
    audioFile,
    
    // Actions
    startRecording,
    pauseRecording,
    stopRecording,
    clearAudio,
    
    // Status
    isSupported,
    error
  }
}
