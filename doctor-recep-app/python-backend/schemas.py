# Python Pydantic schemas for structured JSON output from Gemini
# These models correspond exactly to the prompts.json structure and Zod schemas

from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from enum import Enum

# Common models used across consultation types
class PatientDetails(BaseModel):
    name: str = Field(description="Full Name or Initials if unknown")
    age: str = Field(description="Age in years")
    gender: str = Field(description="Male / Female / Other")
    date_of_consultation: str = Field(description="DD-MM-YYYY")
    time: Optional[str] = Field(None, description="HH:MM AM/PM")
    patient_id: Optional[str] = Field(None, description="Hospital-specific Patient ID")
    hospital_number: Optional[str] = Field(None, description="Hospital/IP Number")

class Vitals(BaseModel):
    bp: Optional[str] = Field(None, description="Blood pressure")
    pulse: Optional[str] = Field(None, description="Pulse rate")
    temp: Optional[str] = Field(None, description="Temperature")
    spo2: Optional[str] = Field(None, description="Oxygen saturation")
    respiration_rate: Optional[str] = Field(None, description="Respiratory rate")

class PrescriptionItem(BaseModel):
    drug_name: str = Field(description="Name of the medication")
    dose: str = Field(description="Dosage amount")
    frequency: str = Field(description="How often to take")
    duration: str = Field(description="How long to take")

class Diagnosis(BaseModel):
    diagnosis: str = Field(description="Primary diagnosis")
    icd_10_code: str = Field(description="ICD-10 code")
    differential: Optional[str] = Field(None, description="Differential diagnosis if applicable")

# OUTPATIENT CONSULTATION SCHEMA
class ExaminationFindings(BaseModel):
    vitals: Vitals
    general_examination: Optional[str] = Field(None, description="General physical examination findings")
    systemic_exam: Optional[str] = Field(None, description="System-wise examination findings (cardiovascular, respiratory, abdominal, neurological, etc.)")

class OutpatientNote(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: PatientDetails
    chief_complaints: List[str] = Field(description="List of primary complaints with duration")
    history_of_present_illness: str = Field(description="Detailed narrative of symptom progression")
    past_medical_history: List[str] = Field(description="Previous medical conditions and allergies")
    examination_findings: ExaminationFindings
    provisional_diagnosis: Diagnosis
    investigations_ordered: List[str] = Field(description="Tests and investigations ordered")
    prescription: List[PrescriptionItem] = Field(description="Medications prescribed")
    follow_up_plan: str = Field(description="Follow-up instructions")
    notes: Optional[str] = Field(None, description="Additional remarks")
    doctor_id: Optional[str] = Field(None, description="Doctor identification")

# DISCHARGE SUMMARY SCHEMA
class DischargePatientDetails(PatientDetails):
    admission_date: str = Field(description="DD-MM-YYYY")
    discharge_date: str = Field(description="DD-MM-YYYY")
    consultant: str = Field(description="Consultant doctor name")
    department: str = Field(description="Department name")

class AdviceOnDischarge(BaseModel):
    diet: str = Field(description="Dietary advice for the patient")
    activity: str = Field(description="Instructions on physical activity and restrictions")
    red_flags: str = Field(description="Warning signs or symptoms that require immediate medical attention")
    follow_up: str = Field(description="Plan for the next follow-up appointment")

class DischargeSummary(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: DischargePatientDetails
    presenting_complaints: str = Field(description="Primary complaints at admission")
    history_of_present_illness: str = Field(description="Chronological narrative of illness")
    past_medical_surgical_history: str = Field(description="Previous medical and surgical history")
    allergies: str = Field(description="Known allergies or NKDA")
    personal_family_history: str = Field(description="Social habits and family history")
    examination_findings_at_admission: str = Field(description="Physical examination at admission")
    investigations: str = Field(description="Laboratory and imaging results")
    final_diagnosis: str = Field(description="Primary diagnosis with ICD-10 codes")
    hospital_course_treatment: str = Field(description="Inpatient treatment summary")
    surgery_performed: Optional[str] = Field(None, description="Surgical procedures if any")
    condition_at_discharge: str = Field(description="Patient status at discharge")
    medications_on_discharge: List[PrescriptionItem] = Field(description="Discharge medications")
    advice_on_discharge: AdviceOnDischarge = Field(description="Structured discharge instructions including diet, activity, red flags, and follow-up")
    prognosis_outcome: str = Field(description="Overall prognosis")
    doctor_signature: str = Field(description="Doctor name and signature")

# SURGERY/OPERATIVE NOTE SCHEMA
class OperativeProcedure(BaseModel):
    incision: str = Field(description="Site, type, and length of incision")
    exploration: str = Field(description="Initial findings upon entry")
    steps_taken: str = Field(description="Chronological description of the entire procedure")
    hemostasis: str = Field(description="Method(s) used for hemostasis")
    irrigation_suction: str = Field(description="Details if irrigation/suction performed")
    closure: str = Field(description="Detailed layer-wise closure of the surgical site")
    drain_placement: str = Field(description="Drain details if placed, or 'None' if not applicable")

class OperativeNote(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: PatientDetails
    date_and_time_of_surgery: str = Field(description="Surgery date and time")
    indications_for_surgery: str = Field(description="Clinical justification")
    preoperative_diagnosis: str = Field(description="Diagnosis before surgery")
    postoperative_diagnosis: str = Field(description="Final diagnosis with ICD-10 code")
    consent: str = Field(description="Informed consent confirmation")
    type_of_anesthesia: str = Field(description="Anesthesia type administered")
    positioning_and_preparation: str = Field(description="Patient positioning and prep")
    operative_procedure: OperativeProcedure = Field(description="Detailed step-by-step surgical procedure description")
    intraoperative_findings: str = Field(description="Surgical findings")
    intraoperative_complications: str = Field(description="Complications or 'None noted'")
    postoperative_plan: str = Field(description="Post-op care instructions")
    condition_at_end_of_procedure: str = Field(description="Patient status post-surgery")
    specimen_sent_for_hpe: str = Field(description="HPE specimen details")
    signatures: str = Field(description="All personnel signatures (surgeon, anesthetist, etc.)")

# RADIOLOGY REPORT SCHEMA
class ExamDetails(BaseModel):
    type_of_exam: str = Field(description="Type of imaging study")
    date_of_exam: str = Field(description="DD-MM-YYYY")
    time_of_exam: str = Field(description="HH:MM AM/PM")
    reason_for_exam: str = Field(description="Clinical indication")

class RadiologyReport(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: PatientDetails
    exam_details: ExamDetails
    comparison: str = Field(description="Prior studies comparison")
    technique: str = Field(description="Scan technique description")
    findings: Dict[str, str] = Field(description="Detailed organ-by-organ imaging findings")
    impression: List[str] = Field(description="Diagnoses with ICD-10 codes")
    recommendations: str = Field(description="Follow-up recommendations")
    radiologist_id: str = Field(description="Radiologist identification")

# DERMATOLOGY CASE NOTE SCHEMA
class DermatologySubjective(BaseModel):
    chief_complaint: str = Field(description="Primary reason for visit with duration")
    history_of_present_illness: str = Field(description="Detailed chronological narrative")
    review_of_systems: str = Field(description="Brief review of relevant systems")

class LesionDescription(BaseModel):
    location: str = Field(description="Precise anatomical location")
    morphology: str = Field(description="Primary and secondary lesions")
    size: str = Field(description="Measurements in mm or cm")
    color: str = Field(description="Color description")
    shape_border: str = Field(description="Shape and border characteristics")

class DermatologyObjective(BaseModel):
    vital_signs: Optional[Vitals] = Field(None)
    general_appearance: str = Field(description="Overall patient appearance")
    lesion_description: LesionDescription
    procedure_note: Optional[str] = Field(None, description="Any procedures performed")

class DermatologyPlan(BaseModel):
    investigations: Optional[str] = Field(None, description="Tests ordered")
    follow_up: str = Field(description="Follow-up instructions")
    advice: str = Field(description="Patient advice and recommendations")

class DermatologyNote(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: PatientDetails
    subjective: DermatologySubjective
    past_medical_history: List[str] = Field(description="Medical comorbidities and allergies")
    objective_physical_exam: DermatologyObjective
    assessment_diagnosis: List[Diagnosis] = Field(description="Primary and differential diagnoses")
    plan_treatment: DermatologyPlan
    doctor_id: str = Field(description="Doctor identification")

# CARDIOLOGY ECHO SCHEMA
class EchoPatientInfo(PatientDetails):
    referring_physician: str = Field(description="Referring doctor name")
    date_of_study: str = Field(description="DD-MM-YYYY")

class EchoValves(BaseModel):
    aortic_valve: str = Field(description="Aortic valve assessment")
    mitral_valve: str = Field(description="Mitral valve assessment")
    tricuspid_valve: str = Field(description="Tricuspid valve assessment")
    pulmonic_valve: str = Field(description="Pulmonic valve assessment")

class EchoFindings(BaseModel):
    left_ventricle: str = Field(description="LV size, function, wall motion")
    right_ventricle: str = Field(description="RV size and function")
    atria: str = Field(description="Atrial size assessment")
    valves: EchoValves
    pericardium: str = Field(description="Pericardial assessment")
    great_vessels: str = Field(description="Aorta and pulmonary artery")

class EchocardiogramReport(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_information: EchoPatientInfo
    reason_for_study: str = Field(description="Clinical indication for echo")
    measurements: Dict[str, str] = Field(description="Detailed 2D & Doppler measurements and values")
    findings: EchoFindings
    conclusion: List[str] = Field(description="Summary with ICD-10 codes")
    cardiologist_id: str = Field(description="Cardiologist identification")

# IVF CYCLE SUMMARY SCHEMA
class IVFPatientDetails(BaseModel):
    female_partner_name: str = Field(description="Female partner full name")
    male_partner_name: str = Field(description="Male partner full name")
    female_age: str = Field(description="Female age in years")
    male_age: str = Field(description="Male age in years")
    patient_id: str = Field(description="Hospital-specific ID")
    cycle_number: str = Field(description="IVF cycle number")
    primary_diagnosis_female: str = Field(description="Female diagnosis with ICD-10")
    primary_diagnosis_male: str = Field(description="Male diagnosis with ICD-10")

class IVFProcedureDetails(BaseModel):
    procedure: str = Field(description="Type of procedure performed")
    date_of_procedure: str = Field(description="DD-MM-YYYY")

class OocyteData(BaseModel):
    oocytes_retrieved: str = Field(description="Number of oocytes retrieved")
    mature_oocytes: str = Field(description="Number of mature (MII) oocytes")
    oocytes_fertilized: str = Field(description="Number fertilized")

class EmbryoData(BaseModel):
    embryos_day_3: str = Field(description="Number of embryos on Day 3")
    embryos_day_5: str = Field(description="Number of blastocysts on Day 5")
    embryos_cryopreserved: str = Field(description="Number cryopreserved")

class EmbryoTransfer(BaseModel):
    embryos_transferred: str = Field(description="Number transferred")
    embryo_quality: str = Field(description="Quality/grade of embryos")
    procedure_ease: str = Field(description="Transfer procedure notes")

class IVFFollowUp(BaseModel):
    medications: str = Field(description="Medication instructions")
    activity: str = Field(description="Activity restrictions")
    next_appointment: str = Field(description="Follow-up schedule")

class IVFCycleSummary(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: IVFPatientDetails
    procedure_details: IVFProcedureDetails
    oocyte_data: OocyteData
    embryo_development_log: EmbryoData
    embryo_transfer_note: EmbryoTransfer
    follow_up_instructions: IVFFollowUp
    doctor_id: str = Field(description="Doctor identification")

# PATHOLOGY/HISTOPATHOLOGY SCHEMA
class PathologyPatientDetails(PatientDetails):
    specimen_id: str = Field(description="Unique laboratory specimen ID")

class SpecimenDetails(BaseModel):
    specimen_source: str = Field(description="Anatomical site and specimen type")
    date_received: str = Field(description="DD-MM-YYYY")
    clinical_history: str = Field(description="Clinical indication")

class HistopathologyReport(BaseModel):
    schema_version: str = Field(default="1.0")
    patient_details: PathologyPatientDetails
    specimen_details: SpecimenDetails
    gross_description: str = Field(description="Macroscopic specimen description")
    microscopic_description: str = Field(description="Detailed microscopic findings")
    final_diagnosis: str = Field(description="Definitive diagnosis with ICD-10/ICD-O codes")
    comments: str = Field(description="Additional pathologist comments")
    pathologist_id: str = Field(description="Pathologist identification")

def build_gemini_json_schema(model: BaseModel) -> Dict:
    """
    Manually builds a simple, Gemini-compatible JSON schema dictionary
    from a Pydantic model, avoiding complex constructs like $defs.
    """
    schema = model.model_json_schema()

    # Flatten the schema by resolving all $defs references manually.
    # This is the key to making it compatible.
    if "$defs" in schema:
        defs = schema["$defs"]

        def resolve_refs(obj):
            if isinstance(obj, dict):
                if "$ref" in obj:
                    ref_path = obj["$ref"].split('/')
                    # Expects format like '#/$defs/MyModel'
                    def_name = ref_path[-1]
                    # Return a copy of the resolved definition and recursively resolve it
                    resolved = defs.get(def_name, {}).copy()
                    return resolve_refs(resolved)
                else:
                    # Recursively resolve all values in the dictionary
                    resolved_dict = {}
                    for k, v in obj.items():
                        resolved_dict[k] = resolve_refs(v)
                    return resolved_dict
            elif isinstance(obj, list):
                return [resolve_refs(item) for item in obj]
            else:
                return obj

        # Resolve all references in the main schema
        resolved_schema = resolve_refs(schema)

        # Remove the $defs key completely
        if "$defs" in resolved_schema:
            del resolved_schema["$defs"]

        return resolved_schema

    return schema

# Helper function to get the correct schema for a consultation type
def get_schema_for_consultation_type(consultation_type: str):
    """
    Returns the appropriate Pydantic schema class for the given consultation type.
    Maps directly to the consultation types defined in prompts.json.
    """
    schema_map = {
        "outpatient": OutpatientNote,
        "discharge": DischargeSummary,
        "surgery": OperativeNote,
        "radiology": RadiologyReport,
        "dermatology": DermatologyNote,
        "cardiology_echo": EchocardiogramReport,
        "ivf_cycle": IVFCycleSummary,
        "pathology": HistopathologyReport,
    }
    return schema_map.get(consultation_type, OutpatientNote)  # Default to Outpatient

# Export all schema classes for use in main.py
__all__ = [
    "OutpatientNote",
    "DischargeSummary",
    "OperativeNote",
    "RadiologyReport",
    "DermatologyNote",
    "EchocardiogramReport",
    "IVFCycleSummary",
    "HistopathologyReport",
    "get_schema_for_consultation_type"
]
