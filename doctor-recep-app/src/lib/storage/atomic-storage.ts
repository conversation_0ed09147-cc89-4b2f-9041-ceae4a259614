/**
 * CELER AI ENTERPRISE ARCHITECTURE 3.0
 * Atomic Storage Operations with Integrity Checks
 * 
 * This module provides atomic file operations with integrity validation
 * and automatic cleanup capabilities for R2 storage.
 */

import { PutObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3'
import { createR2Client } from '@/lib/storage'
import { STORAGE_CONFIG, validateFile, generateStoragePath } from '@/lib/storage'
import { calculateFileChecksum } from '@/lib/validation/consultation-schemas'

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface AtomicUploadResult {
  success: boolean
  url?: string
  checksum?: string
  error?: string
  metadata?: {
    size: number
    contentType: string
    uploadedAt: string
    integrity: boolean
  }
}

export interface AtomicDeleteResult {
  success: boolean
  error?: string
  existed: boolean
}

export interface FileIntegrityCheck {
  exists: boolean
  sizeMatch: boolean
  checksumMatch: boolean
  contentTypeMatch: boolean
  overall: boolean
}

// =====================================================
// ATOMIC UPLOAD OPERATIONS
// =====================================================

/**
 * Upload file with atomic operations and integrity checks
 */
export async function uploadFile(
  file: File,
  userId: string,
  consultationId: string,
  type: 'audio' | 'image'
): Promise<AtomicUploadResult> {
  const startTime = Date.now()
  
  try {
    console.log(`🔄 Starting atomic upload for ${file.name}`)
    
    // Step 1: Validate file
    const validation = validateFile(file, type)
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      }
    }
    
    // Step 2: Calculate checksum for integrity
    const checksum = await calculateFileChecksum(file)
    
    // Step 3: Generate storage path
    const filePath = generateStoragePath(userId, consultationId, file.name, type)
    
    // Step 4: Convert file to buffer
    const fileBuffer = await file.arrayBuffer()
    
    // Step 5: Upload to R2 with metadata
    const r2Client = createR2Client()
    const uploadCommand = new PutObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath,
      Body: new Uint8Array(fileBuffer),
      ContentType: file.type,
      CacheControl: 'public, max-age=3600',
      Metadata: {
        'original-filename': file.name,
        'file-checksum': checksum,
        'upload-timestamp': new Date().toISOString(),
        'user-id': userId,
        'consultation-id': consultationId,
        'file-type': type,
        'original-size': file.size.toString()
      }
    })
    
    await r2Client.send(uploadCommand)
    
    // Step 6: Verify upload integrity
    const integrityCheck = await verifyFileIntegrity(filePath, file, checksum)
    
    if (!integrityCheck.overall) {
      // Cleanup failed upload
      await deleteFile(`${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`)
      
      return {
        success: false,
        error: 'File integrity check failed after upload'
      }
    }
    
    // Step 7: Generate public URL
    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`
    
    const uploadTime = Date.now() - startTime
    console.log(`✅ Atomic upload completed for ${file.name} in ${uploadTime}ms`)
    
    return {
      success: true,
      url: publicUrl,
      checksum,
      metadata: {
        size: file.size,
        contentType: file.type,
        uploadedAt: new Date().toISOString(),
        integrity: true
      }
    }
    
  } catch (error) {
    console.error(`❌ Atomic upload failed for ${file.name}:`, error)
    
    return {
      success: false,
      error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}

/**
 * Upload multiple files atomically (all or nothing)
 */
export async function uploadFilesAtomic(
  files: File[],
  userId: string,
  consultationId: string,
  type: 'audio' | 'image'
): Promise<{
  success: boolean
  results: AtomicUploadResult[]
  uploadedUrls: string[]
  error?: string
}> {
  const uploadedUrls: string[] = []
  const results: AtomicUploadResult[] = []
  
  try {
    console.log(`🔄 Starting atomic batch upload for ${files.length} files`)
    
    // Upload all files
    for (const file of files) {
      const result = await uploadFile(file, userId, consultationId, type)
      results.push(result)
      
      if (result.success && result.url) {
        uploadedUrls.push(result.url)
      } else {
        // If any upload fails, cleanup all uploaded files
        console.error(`❌ Batch upload failed at ${file.name}, cleaning up...`)
        
        await Promise.all(
          uploadedUrls.map(url => deleteFile(url))
        )
        
        return {
          success: false,
          results,
          uploadedUrls: [],
          error: `Batch upload failed: ${result.error}`
        }
      }
    }
    
    console.log(`✅ Atomic batch upload completed for ${files.length} files`)
    
    return {
      success: true,
      results,
      uploadedUrls
    }
    
  } catch (error) {
    console.error(`❌ Atomic batch upload failed:`, error)
    
    // Cleanup any uploaded files
    await Promise.all(
      uploadedUrls.map(url => deleteFile(url))
    )
    
    return {
      success: false,
      results,
      uploadedUrls: [],
      error: `Batch upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}

// =====================================================
// ATOMIC DELETE OPERATIONS
// =====================================================

/**
 * Delete file with verification
 */
export async function deleteFile(fileUrl: string): Promise<AtomicDeleteResult> {
  try {
    console.log(`🗑️ Starting atomic delete for ${fileUrl}`)
    
    // Extract file path from URL
    const filePath = fileUrl.replace(`${STORAGE_CONFIG.PUBLIC_URL}/`, '')
    
    const r2Client = createR2Client()
    
    // Check if file exists first
    let existed = false
    try {
      await r2Client.send(new HeadObjectCommand({
        Bucket: STORAGE_CONFIG.BUCKET_NAME,
        Key: filePath
      }))
      existed = true
    } catch (error) {
      // File doesn't exist, which is fine for delete operation
      existed = false
    }
    
    // Delete the file
    await r2Client.send(new DeleteObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath
    }))
    
    console.log(`✅ Atomic delete completed for ${fileUrl}`)
    
    return {
      success: true,
      existed
    }
    
  } catch (error) {
    console.error(`❌ Atomic delete failed for ${fileUrl}:`, error)
    
    return {
      success: false,
      existed: false,
      error: `Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}

/**
 * Delete multiple files atomically
 */
export async function deleteFilesAtomic(fileUrls: string[]): Promise<{
  success: boolean
  results: AtomicDeleteResult[]
  deletedCount: number
}> {
  const results: AtomicDeleteResult[] = []
  let deletedCount = 0
  
  try {
    console.log(`🗑️ Starting atomic batch delete for ${fileUrls.length} files`)
    
    // Delete all files (best effort - don't fail if some don't exist)
    const deletePromises = fileUrls.map(async (url) => {
      const result = await deleteFile(url)
      if (result.success && result.existed) {
        deletedCount++
      }
      return result
    })
    
    const deleteResults = await Promise.all(deletePromises)
    results.push(...deleteResults)
    
    console.log(`✅ Atomic batch delete completed: ${deletedCount}/${fileUrls.length} files deleted`)
    
    return {
      success: true,
      results,
      deletedCount
    }
    
  } catch (error) {
    console.error(`❌ Atomic batch delete failed:`, error)
    
    return {
      success: false,
      results,
      deletedCount
    }
  }
}

// =====================================================
// INTEGRITY VERIFICATION
// =====================================================

/**
 * Verify file integrity after upload
 */
export async function verifyFileIntegrity(
  filePath: string,
  originalFile: File,
  expectedChecksum: string
): Promise<FileIntegrityCheck> {
  try {
    const r2Client = createR2Client()
    
    // Get file metadata from R2
    const headResult = await r2Client.send(new HeadObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath
    }))
    
    const exists = true
    const sizeMatch = headResult.ContentLength === originalFile.size
    const contentTypeMatch = headResult.ContentType === originalFile.type
    const checksumMatch = headResult.Metadata?.['file-checksum'] === expectedChecksum
    
    const overall = exists && sizeMatch && contentTypeMatch && checksumMatch
    
    if (!overall) {
      console.warn(`⚠️ Integrity check failed for ${filePath}:`, {
        exists,
        sizeMatch,
        contentTypeMatch,
        checksumMatch
      })
    }
    
    return {
      exists,
      sizeMatch,
      checksumMatch,
      contentTypeMatch,
      overall
    }
    
  } catch (error) {
    console.error(`❌ Integrity check failed for ${filePath}:`, error)
    
    return {
      exists: false,
      sizeMatch: false,
      checksumMatch: false,
      contentTypeMatch: false,
      overall: false
    }
  }
}

/**
 * Check if file exists in storage
 */
export async function fileExists(fileUrl: string): Promise<boolean> {
  try {
    const filePath = fileUrl.replace(`${STORAGE_CONFIG.PUBLIC_URL}/`, '')
    const r2Client = createR2Client()
    
    await r2Client.send(new HeadObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath
    }))
    
    return true
    
  } catch (error) {
    return false
  }
}

/**
 * Get file metadata from storage
 */
export async function getFileMetadata(fileUrl: string): Promise<{
  size?: number
  contentType?: string
  lastModified?: Date
  checksum?: string
  metadata?: Record<string, string>
} | null> {
  try {
    const filePath = fileUrl.replace(`${STORAGE_CONFIG.PUBLIC_URL}/`, '')
    const r2Client = createR2Client()
    
    const result = await r2Client.send(new HeadObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath
    }))
    
    return {
      size: result.ContentLength,
      contentType: result.ContentType,
      lastModified: result.LastModified,
      checksum: result.Metadata?.['file-checksum'],
      metadata: result.Metadata
    }
    
  } catch (error) {
    console.error(`Failed to get metadata for ${fileUrl}:`, error)
    return null
  }
}

// =====================================================
// CLEANUP UTILITIES
// =====================================================

/**
 * Find and clean up orphaned files
 */
export async function cleanupOrphanedFiles(
  consultationIds: string[],
  olderThanHours: number = 24
): Promise<{
  scannedFiles: number
  orphanedFiles: string[]
  cleanedFiles: number
  errors: string[]
}> {
  // This would require listing objects in R2 and checking against database
  // Implementation depends on specific requirements and R2 listing capabilities
  
  console.log('🧹 Orphaned file cleanup not yet implemented')
  
  return {
    scannedFiles: 0,
    orphanedFiles: [],
    cleanedFiles: 0,
    errors: ['Cleanup not yet implemented']
  }
}
