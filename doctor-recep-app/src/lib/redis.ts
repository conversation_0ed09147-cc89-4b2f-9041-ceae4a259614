import { createClient } from 'redis'
import type { RedisClientType } from 'redis'

// Redis client for quota management and optimistic UI
let redis: RedisClientType | null = null

export function getRedisClient(): RedisClientType {
  if (!redis) {
    const redisHost = process.env.REDIS_HOST
    const redisPassword = process.env.REDIS_PASSWORD
    const redisPort = process.env.REDIS_PORT
    const redisUsername = process.env.REDIS_USERNAME || 'default'

    if (!redisHost || !redisPassword || !redisPort) {
      throw new Error('Redis credentials not found. Please set REDIS_HOST, REDIS_PASSWORD, and REDIS_PORT in .env.local')
    }

    redis = createClient({
      username: redisUsername,
      password: redisPassword,
      socket: {
        host: redisHost,
        port: parseInt(redisPort)
      }
    })

    redis.on('error', err => console.log('Redis Client Error', err))
    
    // Connect immediately
    redis.connect().catch(console.error)
  }

  return redis
}

// Quota management functions
export async function checkQuotaRedis(userId: string): Promise<{ allowed: boolean; currentUsage: number }> {
  try {
    const redis = getRedisClient()
    const key = `quota:${userId}`
    
    // Get current usage from Redis
    const currentUsageStr = await redis.get(key)
    const currentUsage = currentUsageStr ? parseInt(currentUsageStr) : 0
    
    // For now, we'll use a default quota of 50 (this should come from the database)
    // In production, you might want to cache the user's quota in Redis as well
    const quota = 50
    
    return {
      allowed: currentUsage < quota,
      currentUsage
    }
  } catch (error) {
    console.error('Redis quota check error:', error)
    // Fallback: allow the request if Redis is down
    return { allowed: true, currentUsage: 0 }
  }
}

export async function incrementQuotaRedis(userId: string): Promise<number> {
  try {
    const redis = getRedisClient()
    const key = `quota:${userId}`
    
    // Increment the quota usage
    const newUsage = await redis.incr(key)
    
    // Set expiration to end of month if this is the first increment
    if (newUsage === 1) {
      const now = new Date()
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
      const secondsUntilEndOfMonth = Math.floor((endOfMonth.getTime() - now.getTime()) / 1000)
      await redis.expire(key, secondsUntilEndOfMonth)
    }
    
    return newUsage
  } catch (error) {
    console.error('Redis quota increment error:', error)
    throw error
  }
}

export async function resetQuotaRedis(userId: string): Promise<void> {
  try {
    const redis = getRedisClient()
    const key = `quota:${userId}`
    await redis.del(key)
  } catch (error) {
    console.error('Redis quota reset error:', error)
    throw error
  }
}

// Sync Redis quota with database (called periodically or after DB updates)
export async function syncQuotaFromDatabase(userId: string, quotaUsed: number): Promise<void> {
  try {
    const redis = getRedisClient()
    const key = `quota:${userId}`
    await redis.set(key, quotaUsed)
    
    // Set expiration to end of month
    const now = new Date()
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
    const secondsUntilEndOfMonth = Math.floor((endOfMonth.getTime() - now.getTime()) / 1000)
    await redis.expire(key, secondsUntilEndOfMonth)
  } catch (error) {
    console.error('Redis quota sync error:', error)
    throw error
  }
}
