'use server'

import { revalidatePath } from 'next/cache'
import { cache } from 'react'
import { createClient } from '@/lib/supabase/server'
import { createAdminClient } from '@/lib/supabase/admin'
import { verifyAdminSession } from '@/lib/auth/supabase-helpers'
import { ApiResponse, DoctorWithStats, AdminDashboardStats, AdminActionRequest, AdminDoctorWithStats } from '@/lib/types'

export const getAdminDashboardStats = cache(async (): Promise<ApiResponse<AdminDashboardStats>> => {
  try {
    const session = await verifyAdminSession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    // OPTIMIZED: Use pre-aggregated database view (eliminates 6 queries -> 1 query)
    const { data: stats, error } = await supabase
      .from('admin_dashboard_summary')
      .select('*')
      .single()

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to fetch dashboard stats' }
    }

    // Transform view data to match expected interface
    const dashboardStats: AdminDashboardStats = {
      total_doctors: stats.total_doctors || 0,
      pending_approvals: stats.pending_approvals || 0,
      approved_doctors: stats.approved_doctors || 0,
      total_consultations: stats.total_consultations || 0,
      total_ai_generations: stats.total_ai_generations || 0,
      quota_usage_percentage: stats.quota_usage_percentage || 0,
    }

    return { success: true, data: dashboardStats }
  } catch (error) {
    console.error('Get admin dashboard stats error:', error)
    return { success: false, error: 'Failed to fetch dashboard stats' }
  }
})

export const getAllDoctorsWithStats = cache(async (): Promise<ApiResponse<AdminDoctorWithStats[]>> => {
  try {
    const session = await verifyAdminSession()
    if (!session) {
      return { success: false, error: 'Not authenticated. Please log in again.' }
    }

    const supabase = await createClient()

    // OPTIMIZED: Use pre-aggregated database view (eliminates N+1 queries)
    // This single query replaces 1 + (N * 2) queries where N = number of doctors
    const { data: doctorsWithStats, error } = await supabase
      .from('admin_doctors_with_stats')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return { success: false, error: 'Failed to fetch doctors' }
    }

    // Transform view data to match expected AdminDoctorWithStats interface
    const transformedDoctors: AdminDoctorWithStats[] = doctorsWithStats.map(doctor => ({
      ...doctor,
      phone: doctor.phone ?? null,
      clinic_name: doctor.clinic_name ?? null,
      approved_by: doctor.approved_by ?? null,
      approved_at: doctor.approved_at ?? null,
      referral_code: doctor.referral_code ?? null,
      referred_by: doctor.referred_by ?? null,
      billing_status: doctor.billing_status ?? null,
      trial_ends_at: doctor.trial_ends_at ?? null,
      last_activity: doctor.last_activity ?? null,
      // View already provides these computed fields:
      // - total_consultations
      // - this_month_generations
      // - quota_percentage
    }))

    return { success: true, data: transformedDoctors }
  } catch (error) {
    console.error('Get doctors with stats error:', error)
    return { success: false, error: 'Failed to fetch doctors with stats' }
  }
})

export async function performAdminAction(request: AdminActionRequest): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifyAdminSession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    switch (request.action) {
      case 'approve':
        const { error: approveError } = await supabase
          .from('profiles')
          .update({
            approved: true,
            approved_by: session.adminId,
            approved_at: new Date().toISOString(),
          })
          .eq('id', request.doctor_id)

        if (approveError) {
          return { success: false, error: 'Failed to approve doctor' }
        }

        // Update app_metadata to reflect approval status change
        const supabaseAdmin = createAdminClient()
        await supabaseAdmin.auth.admin.updateUserById(request.doctor_id, {
          app_metadata: { approved: true }
        })
        break

      case 'reject':
        // Delete from profiles table (will cascade to auth.users due to foreign key)
        const { error: rejectError } = await supabase
          .from('profiles')
          .delete()
          .eq('id', request.doctor_id)

        if (rejectError) {
          return { success: false, error: 'Failed to reject doctor' }
        }
        break

      case 'update_quota':
        if (!request.data?.quota) {
          return { success: false, error: 'Quota value is required' }
        }

        const { error: quotaError } = await supabase
          .from('profiles')
          .update({
            monthly_quota: request.data.quota,
          })
          .eq('id', request.doctor_id)

        if (quotaError) {
          return { success: false, error: 'Failed to update quota' }
        }

        await supabase
          .from('usage_logs')
          .insert({
            doctor_id: request.doctor_id,
            action_type: 'quota_update',
            quota_after: request.data.quota,
            metadata: {
              admin_id: session.adminId,
              reason: request.data.reason || 'Admin update',
            },
          })
        break

      case 'disable':
        const { error: disableError } = await supabase
          .from('profiles')
          .update({
            approved: false,
          })
          .eq('id', request.doctor_id)

        if (disableError) {
          return { success: false, error: 'Failed to disable doctor' }
        }

        // Update app_metadata to reflect approval status change
        const supabaseAdminDisable = createAdminClient()
        await supabaseAdminDisable.auth.admin.updateUserById(request.doctor_id, {
          app_metadata: { approved: false }
        })
        break

      case 'enable':
        const { error: enableError } = await supabase
          .from('profiles')
          .update({
            approved: true,
            approved_by: session.adminId,
            approved_at: new Date().toISOString(),
          })
          .eq('id', request.doctor_id)

        if (enableError) {
          return { success: false, error: 'Failed to enable doctor' }
        }

        // Update app_metadata to reflect approval status change
        const supabaseAdminEnable = createAdminClient()
        await supabaseAdminEnable.auth.admin.updateUserById(request.doctor_id, {
          app_metadata: { approved: true }
        })
        break

      default:
        return { success: false, error: 'Invalid action' }
    }

    revalidatePath('/admin/dashboard')
    revalidatePath('/admin/doctors')
    
    return { success: true, data: true }
  } catch (error) {
    console.error('Perform admin action error:', error)
    return { success: false, error: 'Failed to perform action' }
  }
}

export async function resetDoctorQuota(doctorId: string): Promise<ApiResponse<boolean>> {
  try {
    const session = await verifyAdminSession()
    if (!session) {
      return { success: false, error: 'Unauthorized' }
    }

    const supabase = await createClient()

    const { data: doctor } = await supabase
      .from('profiles')
      .select('quota_used')
      .eq('id', doctorId)
      .single()

    if (!doctor) {
      return { success: false, error: 'Doctor not found' }
    }

    const { error } = await supabase
      .from('profiles')
      .update({
        quota_used: 0,
        quota_reset_at: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString(),
      })
      .eq('id', doctorId)

    if (error) {
      return { success: false, error: 'Failed to reset quota' }
    }

    await supabase
      .from('usage_logs')
      .insert({
        doctor_id: doctorId,
        action_type: 'quota_reset',
        quota_before: doctor.quota_used,
        quota_after: 0,
        metadata: {
          admin_id: session.adminId,
          reason: 'Manual admin reset',
        },
      })

    revalidatePath('/admin/dashboard')
    revalidatePath('/admin/doctors')
    
    return { success: true, data: true }
  } catch (error) {
    console.error('Reset doctor quota error:', error)
    return { success: false, error: 'Failed to reset quota' }
  }
}

