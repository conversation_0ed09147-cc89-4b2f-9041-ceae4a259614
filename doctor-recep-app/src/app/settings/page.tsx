import { Metadata } from 'next'
import { verifySession } from '@/lib/auth/dal'
import { SettingsForm } from '@/components/settings/settings-form'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

import Link from 'next/link'
import Image from 'next/image'
import { Sparkles, Settings, Home, Info, LogOut } from 'lucide-react'
import { logout } from '@/lib/actions/auth'

export const metadata: Metadata = {
  title: 'Settings - Celer AI',
  description: 'User settings and preferences',
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
}

export default async function SettingsPage() {
  const session = await verifySession()
  
  if (!session) {
    redirect('/login')
  }

  const supabase = await createClient()
  
  // Get doctor's basic info
  const { data: doctor, error } = await supabase
    .from('profiles')
    .select('name, email')
    .eq('id', session.userId)
    .single()

  if (error || !doctor) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-4 sm:space-x-8">
          {/* Logo only - no text */}
          <div className="flex items-center">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celer AI - Account Settings & Preferences"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            {/* Hide "Celer AI" text on mobile, show on desktop */}
            <span className="hidden sm:block ml-3 text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-3">
            {/* Desktop: Text links */}
            <div className="hidden sm:flex items-center space-x-3">
              <Link
                href="/dashboard"
                className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
              >
                Dashboard
              </Link>
              <Link
                href="/info"
                className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
              >
                Analytics
              </Link>
            </div>

            {/* Mobile: Icon links */}
            <div className="flex sm:hidden items-center space-x-2">
              <Link
                href="/dashboard"
                className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
                title="Dashboard"
              >
                <Home className="w-4 h-4" />
              </Link>
              <Link
                href="/info"
                className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"
                title="Analytics"
              >
                <Info className="w-4 h-4" />
              </Link>
              <form action={logout} className="inline">
                <button
                  type="submit"
                  className="p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-red-50 hover:to-rose-50 text-slate-700 hover:scale-105"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </form>
            </div>
          </div>
        </div>
      </nav>

      <div className="relative max-w-4xl mx-auto py-32 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
              Settings
            </span>
          </h1>

          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-12">
            <Settings className="w-4 h-4 text-indigo-600 animate-pulse" />
            <span className="text-indigo-700 text-sm font-medium">Customize Your Experience</span>
            <Sparkles className="w-4 h-4 text-purple-600" />
          </div>
        </div>

        {/* Settings Card */}
        <div className="relative">
          <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
          <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20">
            <SettingsForm
              doctorId={session.userId}
              doctorName={doctor.name}
              doctorEmail={doctor.email}
            />
          </div>
        </div>
      </div>
    </div>
  )
}