"""
Celer AI System - Python Backend
High-performance audio processing with thread pools and Files API
Converts WebM to WAV for optimal Gemini processing
"""

import os
import logging
import mimetypes
import asyncio
import base64
import json
import httpx
import tempfile
from typing import List, Optional, Tuple, Any, Dict
from datetime import datetime
from urllib.parse import urlparse
import io
from PIL import Image
from google import genai
from google.genai import types
from pydantic import BaseModel, ValidationError
from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException
import uvicorn
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import functools
import subprocess
import shutil

# Import our schemas
from schemas import get_schema_for_consultation_type, build_gemini_json_schema

# Import new enterprise architecture modules
from event_bus import get_event_bus_client, ConsultationEvent
from message_schemas import validate_consultation_event, ConsultationCreateEvent
from monitoring import logger, metrics, monitor_performance, performance_context, health_checker

# Import Phase 2 modules
from batch_processor import get_batch_processor, DynamicBatchProcessor
from sagas.saga_coordinator import get_saga_coordinator
from sagas.consultation_saga import get_consultation_saga
from circuit_breaker import get_circuit_breaker_manager, get_circuit_breaker
from fallback_handlers import (
    get_gemini_fallback_handler,
    get_database_fallback_handler,
    get_file_processing_fallback_handler
)

# Import Phase 3 modules
from performance_tuning import get_performance_manager
from auto_scaling import get_autoscaling_manager
from caching_optimization import get_cache_manager, cached

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Pydantic models for validating the prompts.json configuration
class PromptTemplate(BaseModel):
    format: str
    style: str
    ai_instruction: str
    template_structure: str
    instructions: str
    sections: List[str]

class PromptConfig(BaseModel):
    outpatient: PromptTemplate
    discharge: PromptTemplate
    surgery: PromptTemplate
    radiology: PromptTemplate
    dermatology: PromptTemplate
    cardiology_echo: PromptTemplate
    ivf_cycle: PromptTemplate
    pathology: PromptTemplate

# Function to load and validate the configuration file
def load_prompt_config(path: str) -> Optional[PromptConfig]:
    """Loads and validates the prompt configuration from a JSON file."""
    try:
        with open(path, 'r') as f:
            data = json.load(f)
        config = PromptConfig.model_validate(data)
        logger.info(f"✅ Successfully loaded and validated prompt configuration from {path}")
        return config
    except FileNotFoundError:
        logger.error(f"❌ CONFIGURATION ERROR: The prompt file '{path}' was not found.")
        return None
    except ValidationError as e:
        logger.error(f"❌ CONFIGURATION ERROR: The prompt file '{path}' has invalid data. Details: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ CONFIGURATION ERROR: An unexpected error occurred while loading '{path}'. Details: {e}")
        return None

# Initialize Supabase client
def get_supabase_client():
    """Initialize Supabase client for database operations"""
    try:
        from supabase import create_client, Client
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        if not url or not key:
            raise ValueError("Missing Supabase credentials")
        return create_client(url, key)
    except Exception as e:
        logger.error(f"❌ Failed to initialize Supabase client: {e}")
        raise

# Global variables for worker
prompt_config: Optional[PromptConfig] = None
supabase_client = None
gemini_client = None

# Thread pools for parallel processing (5 threads for 1 vCPU)
audio_thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="audio-worker")
# Process pool for CPU-intensive FFmpeg conversions (bypasses GIL)
conversion_thread_pool = ProcessPoolExecutor(max_workers=5)

# Initialize worker on startup
def initialize_worker():
    """Initialize the worker with required clients and configuration"""
    global prompt_config, supabase_client, gemini_client
    
    logger.info("🚀 Initializing Pub/Sub worker...")
    
    # Load prompt configuration
    prompt_config = load_prompt_config("prompts.json")
    if not prompt_config:
        raise RuntimeError("Failed to load prompt configuration. Worker cannot start.")
    
    # Initialize Supabase client
    supabase_client = get_supabase_client()
    logger.info("✅ Supabase client initialized")
    
    # Initialize Gemini client
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise RuntimeError("GEMINI_API_KEY environment variable is required")
    
    gemini_client = genai.Client(api_key=api_key)
    logger.info("✅ Gemini client initialized")
    
    logger.info("✅ Worker initialization complete")

# Utility functions for file processing
async def download_file(url: str) -> bytes:
    """Download file from URL and return bytes"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url)
            response.raise_for_status()
            return response.content
    except Exception as e:
        logger.error(f"❌ Failed to download file from {url}: {e}")
        raise

def get_mime_type(url: str) -> str:
    """Get MIME type from URL"""
    mime_type, _ = mimetypes.guess_type(url)
    return mime_type or "application/octet-stream"

def is_supported_audio_format(mime_type: str) -> bool:
    """Check if the MIME type is supported by Gemini API"""
    supported_formats = {
        "audio/wav",
        "audio/mp3",
        "audio/mpeg",  # Also MP3
        "audio/aiff",
        "audio/aac",
        "audio/ogg",
        "audio/flac"
    }
    return mime_type in supported_formats

def convert_audio_to_wav_sync(input_data: bytes, input_mime_type: str) -> bytes:
    """Convert audio data to WAV format using ffmpeg (synchronous)"""
    # Determine input format from MIME type
    input_format_map = {
        "video/webm": "webm",
        "audio/webm": "webm",
        "video/mp4": "mp4",
        "audio/mp4": "mp4",
        "audio/wav": "wav",
        "audio/ogg": "ogg"
    }

    input_format = input_format_map.get(input_mime_type, "webm")

    # Create temporary files
    with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{input_format}') as input_file:
        input_file.write(input_data)
        input_file_path = input_file.name

    output_file_path = tempfile.mktemp(suffix='.wav')

    try:
        # Find ffmpeg executable
        ffmpeg_path = shutil.which('ffmpeg')
        if not ffmpeg_path:
            # Try common locations
            for path in ['/opt/homebrew/bin/ffmpeg', '/usr/local/bin/ffmpeg', '/usr/bin/ffmpeg']:
                if os.path.exists(path):
                    ffmpeg_path = path
                    break

        if not ffmpeg_path:
            raise RuntimeError("FFmpeg executable not found")

        # Convert using subprocess directly for better control
        cmd = [
            ffmpeg_path,
            '-i', input_file_path,
            '-acodec', 'pcm_s16le',  # 16-bit PCM
            '-ac', '1',              # Mono
            '-ar', '16000',          # 16kHz sample rate (optimal for speech)
            '-y',                    # Overwrite output
            output_file_path
        ]

        process = subprocess.run(cmd, capture_output=True, text=True)
        if process.returncode != 0:
            raise RuntimeError(f"FFmpeg conversion failed: {process.stderr}")

        # Read converted file
        with open(output_file_path, 'rb') as f:
            converted_data = f.read()

        return converted_data

    finally:
        # Clean up temporary files
        try:
            os.unlink(input_file_path)
            os.unlink(output_file_path)
        except:
            pass

async def convert_audio_to_wav(input_data: bytes, input_mime_type: str) -> bytes:
    """Convert audio data to WAV format using ffmpeg (async wrapper)"""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        conversion_thread_pool,
        convert_audio_to_wav_sync,
        input_data,
        input_mime_type
    )

def get_file_extension(mime_type: str) -> str:
    """Get appropriate file extension for MIME type"""
    mime_to_ext = {
        "audio/wav": ".wav",
        "audio/mp3": ".mp3",
        "audio/mpeg": ".mp3",
        "audio/aiff": ".aiff",
        "audio/aac": ".aac",
        "audio/ogg": ".ogg",
        "audio/flac": ".flac",
        "image/jpeg": ".jpg",
        "image/png": ".png",
        "image/gif": ".gif",
        "image/webp": ".webp",
        "video/webm": ".webm",
        "video/mp4": ".mp4"
    }
    return mime_to_ext.get(mime_type, ".bin")

async def process_audio_file(audio_url: str, gemini_client) -> str:
    """
    Process audio file using Files API and return file reference.
    Converts unsupported formats (like WebM) to MP3 for Gemini compatibility.
    """
    try:
        # Download audio file
        audio_data = await download_file(audio_url)
        original_mime_type = get_mime_type(audio_url)

        logger.info(f"📁 Processing audio: {len(audio_data)} bytes, Original MIME: {original_mime_type}")

        # Check if format is supported by Gemini
        if is_supported_audio_format(original_mime_type):
            # Use original format
            final_mime_type = original_mime_type
            final_audio_data = audio_data
            logger.info(f"✅ Audio format {original_mime_type} is supported by Gemini")
        else:
            # Convert to MP3
            logger.info(f"� Converting {original_mime_type} to MP3 (unsupported format)")
            try:
                final_audio_data = await convert_audio_to_wav(audio_data, original_mime_type)
                final_mime_type = "audio/wav"
                logger.info(f"✅ Successfully converted to WAV: {len(final_audio_data)} bytes")
            except Exception as conv_error:
                logger.error(f"❌ Failed to convert audio format: {conv_error}")
                raise Exception(f"Unsupported audio format {original_mime_type} and conversion failed: {conv_error}")

        file_extension = get_file_extension(final_mime_type)

        # Create a temporary file for upload with proper extension
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(final_audio_data)
            temp_file_path = temp_file.name

        try:
            # Upload to Files API (without mime_type parameter - SDK doesn't support it)
            logger.info(f"📤 Uploading audio file to Gemini Files API: {final_mime_type}")
            uploaded_file = gemini_client.files.upload(file=temp_file_path)
            logger.info(f"✅ Audio uploaded successfully. File ID: {uploaded_file.name}")

            return uploaded_file

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        logger.error(f"❌ Failed to process audio file {audio_url}: {e}")
        raise

async def process_image_file(image_url: str, gemini_client):
    """
    Process image file using Files API and return file reference.
    Uses Files API for better efficiency and handling of larger image files.
    """
    try:
        image_data = await download_file(image_url)
        mime_type = get_mime_type(image_url)

        # Optimize image if needed
        if len(image_data) > 4 * 1024 * 1024:  # 4MB limit
            image = Image.open(io.BytesIO(image_data))
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')

            # Resize if too large
            max_size = (1024, 1024)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

            # Save optimized image
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=85, optimize=True)
            image_data = output.getvalue()
            mime_type = "image/jpeg"

        file_extension = get_file_extension(mime_type)
        logger.info(f"🖼️ Processing image: {len(image_data)} bytes, MIME: {mime_type}, Extension: {file_extension}")

        # Create a temporary file for upload with proper extension
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(image_data)
            temp_file_path = temp_file.name

        try:
            # Upload to Files API with explicit MIME type
            logger.info(f"📤 Uploading image file to Gemini Files API with MIME type: {mime_type}")
            uploaded_file = gemini_client.files.upload(
                file=temp_file_path,
                mime_type=mime_type
            )
            logger.info(f"✅ Image uploaded successfully. File ID: {uploaded_file.name}")

            return uploaded_file

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        logger.error(f"❌ Failed to process image file {image_url}: {e}")
        raise

def generate_prompt(
    prompt_templates: Dict[str, PromptTemplate],
    submitted_by: str,
    consultation_type: str = "outpatient",
    doctor_notes: Optional[str] = None,
    additional_notes: Optional[str] = None,
    patient_name: Optional[str] = None,
    doctor_name: Optional[str] = None,
    created_at: Optional[str] = None
) -> str:
    """Generate AI prompt based on template configuration and consultation type"""
    context_note = (
        "This consultation was recorded by the doctor during patient visit."
        if submitted_by == "doctor"
        else "This consultation is being reviewed by the receptionist for final summary."
    )

    # Get template from the configuration
    consultation_template = prompt_templates.get(consultation_type, prompt_templates["outpatient"])

    sections_text = ", ".join(consultation_template["sections"])

    text_sources = []
    if doctor_notes:
        text_sources.append(f"Doctor's Notes: {doctor_notes}")
    if additional_notes:
        text_sources.append(f"Additional Notes: {additional_notes}")

    text_context = ""
    if text_sources:
        text_context = f"""

**TEXT SOURCES PROVIDED:**
{chr(10).join(text_sources)}

IMPORTANT: Give equal priority to the text sources above and the audio recordings. Both contain valuable medical information that should be integrated into the final summary.
"""

    ai_instruction = consultation_template["ai_instruction"]

    # Add patient name, doctor name, and date context if provided
    patient_context = ""
    if patient_name:
        patient_context = f"\nPatient Name: {patient_name}"

    doctor_context = ""
    if doctor_name:
        doctor_context = f"\nDoctor Name: {doctor_name}"

    date_context = ""
    if created_at:
        try:
            from datetime import datetime, timezone, timedelta
            parsed_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            ist_timezone = timezone(timedelta(hours=5, minutes=30))
            ist_date = parsed_date.astimezone(ist_timezone)
            formatted_date = ist_date.strftime("%B %d, %Y at %I:%M %p IST")
            date_context = f"\nConsultation Date: {formatted_date}"
        except:
            date_context = f"\nConsultation Date: {created_at}"

    return f"""
{ai_instruction}

Context: {context_note}{patient_context}{doctor_context}{date_context}
Consultation Type: {consultation_type.upper()}

{consultation_template["instructions"]}

CRITICAL: Generate structured JSON output only. Do not include any explanatory text.

Requirements:
- Language: English
- Tone: Professional
- Format: {consultation_template["format"]}
- Include sections: {sections_text}

Processing Instructions:
1. Process audio recording(s) and any provided text notes with equal priority
2. Extract key medical information from all sources (audio + text)
3. If images are provided, analyze them for visual findings
4. Integrate information from ALL sources for complete context
5. Keep summary factual and based only on provided information
6. Use appropriate medical terminology for Indian healthcare context
7. For {consultation_type} consultations: {consultation_template["instructions"]}{text_context}
"""

async def cleanup_uploaded_files(uploaded_files: List, gemini_client):
    """
    Clean up uploaded files from Gemini Files API after processing.
    This prevents accumulation of files and manages storage efficiently.
    """
    for uploaded_file in uploaded_files:
        try:
            gemini_client.files.delete(name=uploaded_file.name)
            logger.info(f"🗑️ Cleaned up uploaded file: {uploaded_file.name}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to cleanup file {uploaded_file.name}: {e}")

async def send_webhook_notification(consultation_id: str, status: str):
    """
    Send secured webhook notification to Vercel API about job completion.
    This is the final step as mandated by phase1.md - notify the cache layer.
    """
    try:
        webhook_url = os.getenv("VERCEL_WEBHOOK_URL", "https://your-vercel-app.vercel.app") + "/api/notify-completion"
        webhook_secret = os.getenv("WEBHOOK_SECRET_TOKEN")

        if not webhook_secret:
            logger.error("❌ WEBHOOK_SECRET_TOKEN not configured - cannot send secure webhook")
            return

        payload = {
            "consultationId": consultation_id,
            "status": status
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {webhook_secret}"
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(webhook_url, json=payload, headers=headers)
            response.raise_for_status()
            logger.info(f"✅ Secured webhook notification sent successfully for consultation {consultation_id}")

    except Exception as e:
        logger.error(f"❌ Failed to send webhook notification for consultation {consultation_id}: {e}")
        # Don't raise - webhook failure shouldn't fail the job, but log it prominently
        logger.error("⚠️ WARNING: Job completed but notification failed - status may not update in UI immediately")

async def process_summary_job(event, context):
    """
    DEPRECATED: Legacy V2 architecture - DO NOT USE

    This function represents the old monolithic architecture and should not be used.
    Use process_consultation_batch_v3() with saga pattern instead.

    This function is kept only for backward compatibility and will be removed in future versions.
    """
    logger.warning("⚠️ DEPRECATED: process_summary_job called - redirecting to V3 architecture")

    try:
        # 1. Decode Pub/Sub message to get consultation_id and consultation_type
        if 'data' not in event:
            logger.error("❌ No data in Pub/Sub event")
            return

        message_data = json.loads(base64.b64decode(event['data']).decode('utf-8'))
        consultation_id = message_data.get('consultation_id')
        consultation_type = message_data.get('consultation_type', 'outpatient')

        if not consultation_id:
            logger.error("❌ No consultation_id in Pub/Sub message")
            return

        logger.info(f"🚀 Redirecting to V3 architecture: consultation {consultation_id} (type: {consultation_type})")

        # Redirect to V3 enterprise architecture
        from message_schemas import ConsultationCreateEvent

        event_obj = ConsultationCreateEvent(
            consultation_id=consultation_id,
            consultation_type=consultation_type,
            timestamp=datetime.utcnow(),
            source='pubsub_production'
        )

        # Use V3 enterprise architecture with saga pattern
        await process_consultation_batch_v3([event_obj])
        return

    except Exception as e:
        logger.error(f"❌ Error in process_summary_job: {e}")
        return

        # LEGACY CODE BELOW - COMMENTED OUT
        # This is the old monolithic implementation that should not be used
        """
        # 2. Update status to "pending_generation" in Supabase (processing state)
        try:
            supabase_client.table("consultations").update({
                "status": "pending_generation"
            }).eq("id", consultation_id).execute()
            logger.info(f"✅ Updated consultation {consultation_id} status to pending_generation")
        except Exception as e:
            logger.error(f"❌ Failed to update consultation status: {e}")
            return

        # 3. Fetch consultation details from Supabase
        try:
            consultation_response = supabase_client.table("consultations").select("*").eq("id", consultation_id).single().execute()
            consultation = consultation_response.data

            if not consultation:
                logger.error(f"❌ Consultation {consultation_id} not found")
                return

        except Exception as e:
            logger.error(f"❌ Failed to fetch consultation {consultation_id}: {e}")
            return

        # 4. Process files (audio and images) using parallel thread pools
        uploaded_files = []

        # Collect all audio processing tasks
        audio_tasks = []

        # Primary audio
        if consultation.get('primary_audio_url'):
            audio_tasks.append(
                process_audio_file(consultation['primary_audio_url'], gemini_client)
            )
            logger.info(f"📋 Queued primary audio for parallel processing")

        # Additional audio files
        additional_audio_urls = consultation.get('additional_audio_urls', [])
        if additional_audio_urls and isinstance(additional_audio_urls, list):
            for i, audio_url in enumerate(additional_audio_urls):
                audio_tasks.append(
                    process_audio_file(audio_url, gemini_client)
                )
                logger.info(f"📋 Queued additional audio {i+1} for parallel processing")

        # Process all audio files in parallel using thread pools
        if audio_tasks:
            logger.info(f"🚀 Processing {len(audio_tasks)} audio files in parallel...")
            try:
                audio_results = await asyncio.gather(*audio_tasks, return_exceptions=True)
                for i, result in enumerate(audio_results):
                    if isinstance(result, Exception):
                        logger.error(f"❌ Failed to process audio {i+1}: {result}")
                    else:
                        uploaded_files.append(result)
                        logger.info(f"✅ Audio {i+1} uploaded to Files API")
            except Exception as e:
                logger.error(f"❌ Failed to process audio files in parallel: {e}")

        # Process image files (can also be parallelized if needed)
        image_urls = consultation.get('image_urls', [])
        if image_urls and isinstance(image_urls, list):
            image_tasks = []
            for image_url in image_urls:
                image_tasks.append(process_image_file(image_url, gemini_client))

            if image_tasks:
                logger.info(f"🖼️ Processing {len(image_tasks)} images in parallel...")
                try:
                    image_results = await asyncio.gather(*image_tasks, return_exceptions=True)
                    for i, result in enumerate(image_results):
                        if isinstance(result, Exception):
                            logger.error(f"❌ Failed to process image {i+1}: {result}")
                        else:
                            uploaded_files.append(result)
                            logger.info(f"✅ Image {i+1} uploaded to Files API")
                except Exception as e:
                    logger.error(f"❌ Failed to process images in parallel: {e}")

        # 5. Generate the prompt using the logic from prompts.json
        prompt_templates = prompt_config.model_dump()
        prompt = generate_prompt(
            prompt_templates=prompt_templates,
            submitted_by=consultation.get('submitted_by', 'doctor'),
            consultation_type=consultation_type,
            doctor_notes=consultation.get('doctor_notes'),
            additional_notes=consultation.get('additional_notes'),
            patient_name=consultation.get('patient_name'),
            doctor_name=consultation.get('doctor_name'),  # You may need to fetch this from profiles
            created_at=consultation.get('created_at')
        )

        logger.info(f"📝 Generated prompt for consultation {consultation_id}")

        # 6. Call Gemini with JSON Mode (non-streaming as per documentation)
        try:
            # Get the Pydantic model class for the consultation type
            pydantic_model = get_schema_for_consultation_type(consultation_type)

            # Build the simple, Gemini-compatible schema dictionary from the model
            gemini_compatible_schema = build_gemini_json_schema(pydantic_model)

            # Get model name from environment variable
            model_name = os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')

            # 🔍 DEBUG: Log the exact request details
            logger.info(f"🔍 DEBUG - Gemini API Request Details:")
            logger.info(f"   Model: {model_name}")
            logger.info(f"   Contents count: {len([prompt] + uploaded_files)}")
            logger.info(f"   Prompt length: {len(prompt)} characters")
            logger.info(f"   Uploaded files: {len(uploaded_files)} files")
            logger.info(f"   Schema type: {type(gemini_compatible_schema)}")

            # Log uploaded file details
            for i, uploaded_file in enumerate(uploaded_files):
                logger.info(f"   File {i+1}: {uploaded_file.name} ({uploaded_file.mime_type if hasattr(uploaded_file, 'mime_type') else 'unknown type'})")

            # Log schema structure (truncated for readability)
            if isinstance(gemini_compatible_schema, dict):
                schema_keys = list(gemini_compatible_schema.get('properties', {}).keys()) if 'properties' in gemini_compatible_schema else []
                logger.info(f"   Schema properties: {schema_keys}")
                logger.info(f"   Schema size: {len(str(gemini_compatible_schema))} characters")

                # 🔍 DEBUG: Log the full schema for debugging (be careful with size)
                try:
                    schema_json = json.dumps(gemini_compatible_schema, indent=2)
                    if len(schema_json) < 3000:  # Only log if not too large
                        logger.info(f"   Full schema: {schema_json}")
                    else:
                        logger.info(f"   Schema too large to log ({len(schema_json)} chars)")
                        # Log just the top-level structure
                        top_level = {k: f"<{type(v).__name__}>" for k, v in gemini_compatible_schema.items()}
                        logger.info(f"   Schema structure: {json.dumps(top_level, indent=2)}")

                        # Check for $defs to see if our flattening worked
                        if '$defs' in gemini_compatible_schema:
                            logger.warning(f"   ⚠️  Schema still contains $defs - flattening may have failed")
                        else:
                            logger.info(f"   ✅ Schema successfully flattened (no $defs)")

                except Exception as schema_log_error:
                    logger.error(f"   Failed to log schema: {schema_log_error}")

            # Log first 500 chars of prompt for debugging
            logger.info(f"   Prompt preview: {prompt[:500]}...")

            # Combine prompt with uploaded files (Files API references)
            contents = [prompt] + uploaded_files

            # Use non-streaming generate_content as per Gemini documentation
            logger.info(f"🚀 Sending request to Gemini API...")
            response = gemini_client.models.generate_content(
                model=model_name,
                contents=contents,
                config=types.GenerateContentConfig(
                    response_mime_type="application/json",
                    response_schema=gemini_compatible_schema,  # Use the flattened schema
                    temperature=0.7,  # Lower temperature for more consistent medical output
                    thinking_config=types.ThinkingConfig(thinking_budget=-1)  # Disable thinking for faster response
                )
            )

            # 🔍 DEBUG: Log response details
            logger.info(f"✅ Gemini API Response received:")
            logger.info(f"   Response type: {type(response)}")
            logger.info(f"   Response text length: {len(response.text) if hasattr(response, 'text') else 'N/A'}")
            if hasattr(response, 'text') and response.text:
                logger.info(f"   Response preview: {response.text[:200]}...")
            if hasattr(response, 'usage_metadata'):
                logger.info(f"   Usage metadata: {response.usage_metadata}")
            if hasattr(response, 'candidates'):
                logger.info(f"   Candidates count: {len(response.candidates) if response.candidates else 0}")

            logger.info(f"✅ Gemini response received for consultation {consultation_id}")

        except Exception as e:
            # 🔍 DEBUG: Enhanced error logging
            logger.error(f"❌ Gemini API call failed for consultation {consultation_id}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Error message: {str(e)}")

            # Log additional error details if available
            if hasattr(e, 'response'):
                logger.error(f"   HTTP Response: {e.response}")
            if hasattr(e, 'status_code'):
                logger.error(f"   Status Code: {e.status_code}")
            if hasattr(e, 'details'):
                logger.error(f"   Error Details: {e.details}")

            # Try to extract more info from the exception
            try:
                import traceback
                logger.error(f"   Full traceback: {traceback.format_exc()}")
            except:
                pass

            # Clean up uploaded files
            if uploaded_files:
                await cleanup_uploaded_files(uploaded_files, gemini_client)

            # Update status and send webhook notification
            try:
                supabase_client.table("consultations").update({
                    "status": "pending_generation"  # Keep as pending since no "failed" status exists
                }).eq("id", consultation_id).execute()
                await send_webhook_notification(consultation_id, "failed")
            except:
                pass
            return

        # 7. Save structured JSON to Supabase
        try:
            summary_json = json.loads(response.text)

            # Update consultation with the generated JSON and status
            # Clear edited_note_json to ensure new AI content is displayed
            update_payload = {
                "ai_generated_note_json": summary_json,
                "edited_note_json": None,  # Explicitly clear the edited note on regeneration
                "status": "generated"
            }

            supabase_client.table("consultations").update(update_payload).eq("id", consultation_id).execute()
            logger.info(f"✅ Consultation {consultation_id} updated with generated JSON")

            # 8. Send webhook notification to Vercel
            await send_webhook_notification(consultation_id, "generated")

            # 9. Clean up uploaded files from Files API
            if uploaded_files:
                await cleanup_uploaded_files(uploaded_files, gemini_client)

            logger.info(f"🎉 Successfully completed processing consultation {consultation_id}")

        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse Gemini JSON response for consultation {consultation_id}: {e}")
            # Clean up uploaded files
            if uploaded_files:
                await cleanup_uploaded_files(uploaded_files, gemini_client)
            # Update status back to pending_generation (no "failed" status in schema)
            try:
                supabase_client.table("consultations").update({
                    "status": "pending_generation"
                }).eq("id", consultation_id).execute()
                await send_webhook_notification(consultation_id, "failed")
            except:
                pass
            return

        except Exception as e:
            logger.error(f"❌ Failed to save consultation {consultation_id}: {e}")
            # Clean up uploaded files
            if uploaded_files:
                await cleanup_uploaded_files(uploaded_files, gemini_client)
            # Update status back to pending_generation (no "failed" status in schema)
            try:
                supabase_client.table("consultations").update({
                    "status": "pending_generation"
                }).eq("id", consultation_id).execute()
                await send_webhook_notification(consultation_id, "failed")
            except:
                pass
            return

    except Exception as e:
        logger.error(f"❌ Unexpected error processing consultation {consultation_id if 'consultation_id' in locals() else 'unknown'}: {e}")
        # Clean up uploaded files if they exist
        if 'uploaded_files' in locals() and uploaded_files:
            try:
                await cleanup_uploaded_files(uploaded_files, gemini_client)
            except:
                pass
        # Try to update status to failed if we have consultation_id
        if 'consultation_id' in locals() and consultation_id:
            try:
                supabase_client.table("consultations").update({
                    "status": "pending_generation"
                }).eq("id", consultation_id).execute()
                await send_webhook_notification(consultation_id, "failed")
            except:
                pass
        """

# Entry point for Google Cloud Functions
def main(event, context):
    """
    Google Cloud Function entry point for Pub/Sub trigger.
    This function will be called by Google Cloud when a message is published to the topic.
    """
    try:
        # Initialize worker if not already done
        if not prompt_config:
            initialize_worker()

        # Run the async worker function
        asyncio.run(process_summary_job(event, context))

    except Exception as e:
        logger.error(f"❌ Fatal error in main function: {e}")
        raise

# ============================================================================
# LOCAL DEVELOPMENT FASTAPI SERVER
# ============================================================================

# Create FastAPI app for local development
app = FastAPI(title="Celer AI Python Worker", version="1.0.0")

# Local development endpoint - only enabled when ENVIRONMENT=local
if os.environ.get("ENVIRONMENT") == "local":
    @app.post("/local/trigger-job")
    async def local_trigger_job(request: Request):
        """
        Local development endpoint that receives direct HTTP calls from the frontend
        and triggers the same worker function that Pub/Sub would trigger in production.
        """
        try:
            body = await request.json()
            consultation_id = body.get("consultation_id")
            consultation_type = body.get("consultation_type", "outpatient")

            logger.info(f"🔧 Local trigger received: consultation_id={consultation_id}, type={consultation_type}")

            # Simulate the Pub/Sub message payload
            simulated_event = {
                'data': base64.b64encode(json.dumps({
                    'consultation_id': consultation_id,
                    'consultation_type': consultation_type
                }).encode()).decode()
            }

            # Call the V3 enterprise architecture (same code path as production)
            # Convert to proper event format for V3 saga processing
            from message_schemas import ConsultationCreateEvent

            event = ConsultationCreateEvent(
                consultation_id=consultation_id,
                consultation_type=consultation_type,
                timestamp=datetime.utcnow(),
                source='local_development'
            )

            # Use V3 enterprise architecture with saga pattern
            await process_consultation_batch_v3([event])

            return {"status": "success", "message": "Local job triggered successfully"}

        except Exception as e:
            logger.error(f"❌ Local trigger failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/health")
    async def health_check():
        """Health check endpoint for local development"""
        return {"status": "healthy", "environment": "local", "service": "celer-ai-worker"}

# For local testing
if __name__ == "__main__":
    # Initialize worker
    initialize_worker()

    # Check if we should run as FastAPI server for local development
    if os.environ.get("ENVIRONMENT") == "local":
        logger.info("🚀 Starting FastAPI server for local development on port 3005...")
        uvicorn.run(app, host="0.0.0.0", port=3005, log_level="info")
    else:
        # Example test event (for local development without FastAPI)
        test_event = {
            'data': base64.b64encode(json.dumps({
                'consultation_id': 'test-consultation-id',
                'consultation_type': 'outpatient'
            }).encode()).decode()
        }

        test_context = {}

        print("🧪 Running local test...")
        asyncio.run(process_summary_job(test_event, test_context))

# ============================================================================
# ENTERPRISE ARCHITECTURE 3.0 - PHASE 2: INTELLIGENT PROCESSING
# ============================================================================

@monitor_performance("optimized_batch_consultation_processing")
async def process_consultation_batch_v3(events: List[ConsultationCreateEvent]):
    """
    ENTERPRISE ARCHITECTURE 3.0 - PHASE 3
    Fully optimized batch processing with caching, performance tuning, and auto-scaling
    """
    batch_id = f"optimized_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(events)}"

    with performance_context("optimized_consultation_batch_processing"):
        logger.info("🚀 Starting optimized batch processing with Phase 3 features",
                   batch_id=batch_id,
                   batch_size=len(events),
                   consultation_ids=[e.consultation_id for e in events])

        try:
            # Get performance manager for optimized execution
            performance_manager = get_performance_manager()

            # Use optimized saga pattern with caching
            consultation_saga = get_consultation_saga()
            saga_id = await consultation_saga.process_batch(events)

            # Record performance metrics for auto-scaling
            metrics.increment_counter('optimized_batch_processing_total',
                                    {'status': 'success', 'batch_size': str(len(events))})

            logger.info("✅ Optimized batch processing initiated via saga",
                       batch_id=batch_id,
                       saga_id=saga_id)

            return saga_id

        except Exception as e:
            logger.error("❌ Optimized batch processing failed",
                        batch_id=batch_id,
                        error=str(e))

            # Record failure metrics
            metrics.increment_counter('optimized_batch_processing_total',
                                    {'status': 'failed', 'batch_size': str(len(events))})
            raise

@monitor_performance("enhanced_batch_consultation_processing")
async def process_consultation_batch_v2(events: List[ConsultationCreateEvent]):
    """
    DEPRECATED: Legacy V2 architecture - DO NOT USE

    Use process_consultation_batch_v3() instead.
    This function is kept only for backward compatibility.
    """
    batch_id = f"enhanced_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(events)}"

    with performance_context("enhanced_consultation_batch_processing"):
        logger.info("🚀 Starting enhanced batch processing with Phase 2 features",
                   batch_id=batch_id,
                   batch_size=len(events),
                   consultation_ids=[e.consultation_id for e in events])

        try:
            # Use saga pattern for distributed transaction management
            consultation_saga = get_consultation_saga()
            saga_id = await consultation_saga.process_batch(events)

            logger.info("✅ Enhanced batch processing initiated via saga",
                       batch_id=batch_id,
                       saga_id=saga_id)

            return saga_id

        except Exception as e:
            logger.error("❌ Enhanced batch processing failed",
                        batch_id=batch_id,
                        error=str(e))

            # Record failure metrics
            metrics.increment_counter('enhanced_batch_processing_total',
                                    {'status': 'failed', 'batch_size': str(len(events))})
            raise

@monitor_performance("batch_consultation_processing")
async def process_consultation_batch(events: List[ConsultationCreateEvent]):
    """
    DEPRECATED: Legacy V1 architecture - DO NOT USE

    Use process_consultation_batch_v3() instead.
    This function is kept only for backward compatibility.
    """
    batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(events)}"

    with performance_context("consultation_batch_processing"):
        logger.info("🚀 Starting batch processing",
                   batch_id=batch_id,
                   batch_size=len(events),
                   consultation_ids=[e.consultation_id for e in events])

        try:
            # Step 1: Batch INSERT to database
            await batch_insert_consultations(events)

            # Step 2: Process consultations in parallel
            processing_results = await process_consultations_parallel(events)

            # Step 3: Batch UPDATE to database
            await batch_update_consultations(processing_results)

            # Step 4: Send notifications
            await send_batch_notifications(processing_results)

            logger.info("✅ Batch processing completed successfully",
                       batch_id=batch_id,
                       successful_count=len([r for r in processing_results if r.get('success')]),
                       failed_count=len([r for r in processing_results if not r.get('success')]))

        except Exception as e:
            logger.error("❌ Batch processing failed",
                        batch_id=batch_id,
                        error=str(e))
            # Trigger compensation logic here
            raise

async def batch_insert_consultations(events: List[ConsultationCreateEvent]):
    """Batch INSERT consultations using stored procedure"""
    try:
        # Prepare consultation data for bulk insert
        consultation_data = []
        for event in events:
            consultation_data.append({
                'id': event.consultation_id,
                'correlation_id': event.correlation_id,
                'user_id': event.user_id,
                'status': 'processing',
                'consultation_type': event.consultation_type,
                'submitted_by': event.submitted_by,
                'patient_name': event.patient_name,
                'doctor_notes': event.doctor_notes,
                'additional_notes': event.additional_notes,
                'primary_audio_url': event.primary_audio_url,
                'additional_audio_urls': event.additional_audio_urls,
                'image_urls': event.image_urls,
                'total_file_size_bytes': event.total_file_size_bytes,
                'metadata': event.metadata,
                'created_by': event.user_id
            })

        # Execute bulk insert
        result = supabase_client.rpc('bulk_insert_consultations', {
            'consultations_json': json.dumps(consultation_data)
        }).execute()

        logger.info("✅ Batch INSERT completed",
                   inserted_count=len(consultation_data))

        # Record metrics
        metrics.increment_counter('consultations_batch_inserted_total',
                                {'batch_size': str(len(consultation_data))})

    except Exception as e:
        logger.error("❌ Batch INSERT failed", error=str(e))
        raise

async def process_consultations_parallel(events: List[ConsultationCreateEvent]) -> List[Dict[str, Any]]:
    """Process consultations in parallel with resource limits"""
    semaphore = asyncio.Semaphore(5)  # Limit concurrent processing

    async def process_single_consultation(event: ConsultationCreateEvent) -> Dict[str, Any]:
        async with semaphore:
            try:
                # Use existing process_summary_job logic but adapted for new event format
                return await process_single_consultation_v3(event)
            except Exception as e:
                logger.error("❌ Single consultation processing failed",
                           consultation_id=event.consultation_id,
                           error=str(e))
                return {
                    'consultation_id': event.consultation_id,
                    'success': False,
                    'error': str(e)
                }

    # Process all consultations in parallel
    results = await asyncio.gather(
        *[process_single_consultation(event) for event in events],
        return_exceptions=True
    )

    # Handle exceptions
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append({
                'consultation_id': events[i].consultation_id,
                'success': False,
                'error': str(result)
            })
        else:
            processed_results.append(result)

    return processed_results

async def process_single_consultation_v3(event: ConsultationCreateEvent) -> Dict[str, Any]:
    """
    ENTERPRISE ARCHITECTURE 3.0 - PHASE 2
    Process a single consultation with circuit breakers and fallback mechanisms
    """
    consultation_id = event.consultation_id

    # Get fallback handlers
    file_fallback = get_file_processing_fallback_handler()
    gemini_fallback = get_gemini_fallback_handler()

    try:
        logger.info("🔄 Processing single consultation with Phase 2 enhancements",
                   consultation_id=consultation_id,
                   consultation_type=event.consultation_type)

        # Step 1: Process files with fallback mechanisms
        file_urls = [event.primary_audio_url] + event.additional_audio_urls + event.image_urls

        file_processing_result = await file_fallback.process_files_with_fallback(
            files=file_urls,
            processing_func=lambda files: process_files_parallel(files, event)
        )

        if not file_processing_result.success:
            logger.error("❌ File processing failed completely",
                        consultation_id=consultation_id)
            return {
                'consultation_id': consultation_id,
                'success': False,
                'error': file_processing_result.error,
                'status': 'failed'
            }

        uploaded_files = file_processing_result.data.get('processed_files', [])

        # Step 2: Generate AI summary with fallback mechanisms
        prompt_templates = prompt_config.model_dump()
        prompt = generate_prompt(
            prompt_templates=prompt_templates,
            submitted_by=event.submitted_by,
            consultation_type=event.consultation_type,
            patient_name=event.patient_name,
            doctor_notes=event.doctor_notes,
            additional_notes=event.additional_notes
        )

        # Use Gemini with fallback
        gemini_result = await gemini_fallback.generate_content_with_fallback(
            prompt=prompt,
            uploaded_files=uploaded_files,
            consultation_type=event.consultation_type,
            primary_func=lambda p, f, t: generate_gemini_content(p, f, t)
        )

        if not gemini_result.success:
            logger.error("❌ Gemini processing failed completely",
                        consultation_id=consultation_id)
            return {
                'consultation_id': consultation_id,
                'success': False,
                'error': gemini_result.error,
                'status': 'failed'
            }

        ai_generated_note_json = gemini_result.data

        # Add fallback indicators to the result
        result = {
            'consultation_id': consultation_id,
            'success': True,
            'ai_generated_note_json': ai_generated_note_json,
            'status': 'generated',
            'processing_metadata': {
                'file_processing_fallback_used': file_processing_result.fallback_used,
                'file_processing_fallback_type': file_processing_result.fallback_type,
                'gemini_fallback_used': gemini_result.fallback_used,
                'gemini_fallback_type': gemini_result.fallback_type,
                'total_execution_time_seconds': file_processing_result.execution_time_seconds + gemini_result.execution_time_seconds
            }
        }

        # Log fallback usage
        if file_processing_result.fallback_used or gemini_result.fallback_used:
            logger.warning("⚠️ Consultation processed with fallback mechanisms",
                          consultation_id=consultation_id,
                          file_fallback=file_processing_result.fallback_type,
                          gemini_fallback=gemini_result.fallback_type)
        else:
            logger.info("✅ Single consultation processed successfully without fallbacks",
                       consultation_id=consultation_id)

        return result

    except Exception as e:
        logger.error("❌ Single consultation processing failed",
                   consultation_id=consultation_id, error=str(e))
        return {
            'consultation_id': consultation_id,
            'success': False,
            'error': str(e),
            'status': 'failed'
        }

async def process_files_parallel(file_urls: List[str], event: ConsultationCreateEvent) -> Dict[str, Any]:
    """Process files in parallel with circuit breaker protection"""
    uploaded_files = []

    # Process primary audio
    if event.primary_audio_url:
        try:
            audio_file = await process_audio_file(event.primary_audio_url, gemini_client)
            uploaded_files.append(audio_file)
        except Exception as e:
            logger.error("❌ Failed to process primary audio",
                       consultation_id=event.consultation_id, error=str(e))

    # Process additional audio files in parallel
    if event.additional_audio_urls:
        audio_tasks = [
            process_audio_file(url, gemini_client)
            for url in event.additional_audio_urls
        ]
        audio_results = await asyncio.gather(*audio_tasks, return_exceptions=True)

        for result in audio_results:
            if not isinstance(result, Exception):
                uploaded_files.append(result)

    # Process images in parallel
    if event.image_urls:
        image_tasks = [
            process_image_file(url, gemini_client)
            for url in event.image_urls
        ]
        image_results = await asyncio.gather(*image_tasks, return_exceptions=True)

        for result in image_results:
            if not isinstance(result, Exception):
                uploaded_files.append(result)

    return {
        'processed_files': uploaded_files,
        'total_files': len(file_urls),
        'successful_files': len(uploaded_files)
    }

async def batch_update_consultations(results: List[Dict[str, Any]]):
    """Batch UPDATE consultations using stored procedure"""
    try:
        # Prepare update data
        update_data = []
        for result in results:
            if result.get('success'):
                update_data.append({
                    'id': result['consultation_id'],
                    'ai_generated_note_json': json.dumps(result['ai_generated_note_json']),
                    'status': result.get('status', 'generated'),
                    'updated_by': None  # System update
                })
            else:
                update_data.append({
                    'id': result['consultation_id'],
                    'status': 'failed',
                    'updated_by': None  # System update
                })

        # Execute bulk update
        if update_data:
            result = supabase_client.rpc('bulk_update_consultations', {
                'updates_json': json.dumps(update_data)
            }).execute()

            logger.info("✅ Batch UPDATE completed",
                       updated_count=len(update_data))

            # Record metrics
            metrics.increment_counter('consultations_batch_updated_total',
                                    {'batch_size': str(len(update_data))})

    except Exception as e:
        logger.error("❌ Batch UPDATE failed", error=str(e))
        raise

async def send_batch_notifications(results: List[Dict[str, Any]]):
    """Send notifications for batch processing results"""
    try:
        for result in results:
            if result.get('success'):
                # Send success notification (webhook or status update)
                await send_webhook_notification(
                    result['consultation_id'],
                    'generated',
                    result.get('ai_generated_note_json')
                )
            else:
                # Send failure notification
                await send_webhook_notification(
                    result['consultation_id'],
                    'failed',
                    None
                )

        logger.info("✅ Batch notifications sent",
                   notification_count=len(results))

    except Exception as e:
        logger.error("❌ Batch notifications failed", error=str(e))
        # Don't raise - notifications are not critical

# ============================================================================
# ENTERPRISE ARCHITECTURE 3.0 - EVENT BUS INTEGRATION
# ============================================================================

async def start_event_driven_worker_v2():
    """
    ENTERPRISE ARCHITECTURE 3.0 - PHASE 2
    Start the enhanced event-driven worker with all Phase 2 features
    """
    logger.info("🚀 Starting Enterprise Architecture 3.0 Phase 2 Worker")

    # Initialize worker
    initialize_worker()

    # Initialize Phase 2 components
    await initialize_phase2_components()

    # Set up dynamic batch processor
    batch_processor = get_batch_processor(process_consultation_batch_v2)

    # Set up event bus client with dynamic batch processor
    event_bus = get_event_bus_client()
    event_bus.set_message_handler(lambda events: batch_processor.add_message(events[0], None) if events else None)

    # Start consuming messages
    await event_bus.start_consuming()

async def start_event_driven_worker_v3():
    """
    ENTERPRISE ARCHITECTURE 3.0 - PHASE 3
    Start the fully optimized event-driven worker with all Phase 3 features
    """
    logger.info("🚀 Starting Enterprise Architecture 3.0 Phase 3 Worker (Full Optimization)")

    # Initialize worker
    initialize_worker()

    # Initialize all phases
    await initialize_phase2_components()
    await initialize_phase3_components()

    # Set up optimized batch processor with caching
    batch_processor = get_batch_processor(process_consultation_batch_v3)

    # Set up event bus client with dynamic batch processor
    event_bus = get_event_bus_client()
    event_bus.set_message_handler(lambda events: batch_processor.add_message(events[0], None) if events else None)

    # Start consuming messages
    await event_bus.start_consuming()

async def initialize_phase3_components():
    """Initialize all Phase 3 optimization components"""
    logger.info("🔧 Initializing Phase 3 optimization components")

    # Initialize performance manager
    performance_manager = get_performance_manager()
    await performance_manager.start_all_monitoring()

    # Initialize auto-scaling manager
    autoscaling_manager = get_autoscaling_manager()
    await autoscaling_manager.start()

    # Initialize cache manager
    cache_manager = get_cache_manager()
    await cache_manager.start_cleanup()

    # Warm critical caches
    await warm_critical_caches()

    logger.info("✅ Phase 3 optimization components initialized successfully")

async def warm_critical_caches():
    """Warm critical caches for optimal performance"""
    cache_manager = get_cache_manager()

    # Define cache warming strategies
    warming_keys = [
        # Cache prompt templates
        ("prompt_templates", lambda: prompt_config.model_dump()),

        # Cache schema definitions
        ("consultation_schemas", lambda: {
            ct: get_schema_for_consultation_type(ct)
            for ct in ['outpatient', 'discharge', 'surgery']
        }),

        # Cache common configurations
        ("gemini_config", lambda: {
            'model': GEMINI_MODEL,
            'generation_config': {
                'temperature': 0.1,
                'top_p': 0.8,
                'top_k': 40,
                'max_output_tokens': 8192
            }
        })
    ]

    await cache_manager.warm_cache(warming_keys)
    logger.info("✅ Critical caches warmed")

async def initialize_phase2_components():
    """Initialize all Phase 2 components"""
    logger.info("🔧 Initializing Phase 2 components")

    # Initialize saga coordinator
    saga_coordinator = get_saga_coordinator()
    await saga_coordinator.start_cleanup_task()

    # Initialize circuit breaker manager
    circuit_breaker_manager = get_circuit_breaker_manager()
    await circuit_breaker_manager.start_monitoring()

    # Initialize fallback handlers (they initialize themselves)
    get_gemini_fallback_handler()
    get_database_fallback_handler()
    get_file_processing_fallback_handler()

    logger.info("✅ Phase 2 components initialized successfully")

async def start_event_driven_worker():
    """
    ENTERPRISE ARCHITECTURE 3.0 - PHASE 1 (Legacy support)
    Start the event-driven worker with intelligent batching
    """
    logger.info("🚀 Starting Enterprise Architecture 3.0 Worker (Phase 1)")

    # Initialize worker
    initialize_worker()

    # Set up event bus client
    event_bus = get_event_bus_client()
    event_bus.set_message_handler(process_consultation_batch)

    # Start consuming messages
    await event_bus.start_consuming()

# Health check endpoint for enterprise architecture Phase 2
@app.get("/health/v3")
async def health_check_v3():
    """Enhanced health check for Enterprise Architecture 3.0 Phase 2"""
    health_status = await health_checker.check_health()

    # Add Phase 2 component health checks
    try:
        # Check saga coordinator
        saga_coordinator = get_saga_coordinator()
        health_status['checks']['saga_coordinator'] = {
            'healthy': True,
            'active_sagas': len(saga_coordinator.active_sagas),
            'message': 'Saga coordinator operational'
        }

        # Check circuit breakers
        circuit_breaker_manager = get_circuit_breaker_manager()
        circuit_breaker_status = circuit_breaker_manager.get_all_status()
        health_status['checks']['circuit_breakers'] = {
            'healthy': all(cb['state'] != 'open' for cb in circuit_breaker_status.values()),
            'circuit_breakers': circuit_breaker_status,
            'message': 'Circuit breakers status checked'
        }

        # Check batch processor
        batch_processor = get_batch_processor()
        if batch_processor:
            batch_status = batch_processor.get_status()
            health_status['checks']['batch_processor'] = {
                'healthy': not batch_status['is_processing'] or batch_status['current_batch_size'] < 50,
                'status': batch_status,
                'message': 'Batch processor operational'
            }

    except Exception as e:
        logger.error("Failed to check Phase 2 component health", error=str(e))
        health_status['checks']['phase2_components'] = {
            'healthy': False,
            'error': str(e),
            'message': 'Phase 2 component health check failed'
        }

    # Update overall health
    health_status['healthy'] = all(
        check.get('healthy', False)
        for check in health_status['checks'].values()
    )

    return health_status

# Metrics endpoint
@app.get("/metrics")
async def get_metrics():
    """Get comprehensive system metrics including Phase 2 components"""
    try:
        base_metrics = {
            'basic_metrics': metrics.get_metrics_summary(),
            'timestamp': datetime.now().isoformat()
        }

        # Add Phase 2 metrics
        try:
            # Saga metrics
            saga_coordinator = get_saga_coordinator()
            base_metrics['saga_metrics'] = {
                'active_sagas': len(saga_coordinator.active_sagas),
                'saga_types': list(saga_coordinator.saga_definitions.keys())
            }

            # Circuit breaker metrics
            circuit_breaker_manager = get_circuit_breaker_manager()
            base_metrics['circuit_breaker_metrics'] = circuit_breaker_manager.get_all_status()

            # Batch processor metrics
            batch_processor = get_batch_processor()
            if batch_processor:
                base_metrics['batch_processor_metrics'] = batch_processor.get_status()

        except Exception as e:
            logger.error("Failed to collect Phase 2 metrics", error=str(e))
            base_metrics['phase2_metrics_error'] = str(e)

        return base_metrics

    except Exception as e:
        logger.error("Failed to collect metrics", error=str(e))
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Circuit breaker status endpoint
@app.get("/circuit-breakers")
async def get_circuit_breaker_status():
    """Get detailed circuit breaker status"""
    try:
        circuit_breaker_manager = get_circuit_breaker_manager()
        return {
            'circuit_breakers': circuit_breaker_manager.get_all_status(),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get circuit breaker status", error=str(e))
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Saga status endpoint
@app.get("/sagas")
async def get_saga_status():
    """Get active saga status"""
    try:
        saga_coordinator = get_saga_coordinator()
        return {
            'active_sagas': len(saga_coordinator.active_sagas),
            'saga_definitions': list(saga_coordinator.saga_definitions.keys()),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get saga status", error=str(e))
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Saga details endpoint
@app.get("/sagas/{saga_id}")
async def get_saga_details(saga_id: str):
    """Get detailed saga status by ID"""
    try:
        saga_coordinator = get_saga_coordinator()
        saga_status = await saga_coordinator.get_saga_status(saga_id)

        if saga_status:
            return saga_status
        else:
            return {
                'error': 'Saga not found',
                'saga_id': saga_id,
                'timestamp': datetime.now().isoformat()
            }
    except Exception as e:
        logger.error("Failed to get saga details", saga_id=saga_id, error=str(e))
        return {
            'error': str(e),
            'saga_id': saga_id,
            'timestamp': datetime.now().isoformat()
        }

# Phase 3 optimization endpoints
@app.get("/performance")
async def get_performance_stats():
    """Get comprehensive performance statistics"""
    try:
        performance_manager = get_performance_manager()
        return {
            'performance_stats': performance_manager.get_comprehensive_stats(),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get performance stats", error=str(e))
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

@app.get("/autoscaling")
async def get_autoscaling_status():
    """Get auto-scaling system status"""
    try:
        autoscaling_manager = get_autoscaling_manager()
        return {
            'autoscaling_status': autoscaling_manager.get_status(),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get auto-scaling status", error=str(e))
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

@app.get("/cache")
async def get_cache_stats():
    """Get cache statistics"""
    try:
        cache_manager = get_cache_manager()
        return {
            'cache_stats': cache_manager.get_comprehensive_stats(),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get cache stats", error=str(e))
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# ============================================================================
# GEMINI CONTENT GENERATION WITH OPTIMIZATION
# ============================================================================

@cached(
    key_func=lambda prompt, uploaded_files, consultation_type: f"gemini:{consultation_type}:{hashlib.md5(prompt.encode()).hexdigest()}:{len(uploaded_files)}",
    ttl_seconds=3600  # Cache for 1 hour
)
async def generate_gemini_content_cached(prompt: str, uploaded_files: List[Any], consultation_type: str) -> Dict[str, Any]:
    """Generate content using Gemini API with intelligent caching"""
    return await generate_gemini_content(prompt, uploaded_files, consultation_type)

async def generate_gemini_content(prompt: str, uploaded_files: List[Any], consultation_type: str) -> Dict[str, Any]:
    """Generate content using Gemini API with uploaded files"""
    try:
        logger.info("🤖 Generating content with Gemini",
                   consultation_type=consultation_type,
                   files_count=len(uploaded_files))

        # Get performance manager for optimized execution
        performance_manager = get_performance_manager()

        # Execute in optimized thread pool
        result = await performance_manager.cpu_optimizer.execute_in_thread_pool(
            'io_bound',
            _generate_gemini_content_sync,
            prompt, uploaded_files, consultation_type
        )

        return result

    except Exception as e:
        logger.error("❌ Gemini content generation failed",
                   consultation_type=consultation_type,
                   error=str(e))

        # Return error structure
        return {
            "error": str(e),
            "consultation_type": consultation_type,
            "status": "generation_failed"
        }

def _generate_gemini_content_sync(prompt: str, uploaded_files: List[Any], consultation_type: str) -> Dict[str, Any]:
    """Synchronous Gemini content generation for thread pool execution"""
    try:
        # Prepare content parts
        content_parts = [prompt]
        content_parts.extend(uploaded_files)

        # Generate content
        response = gemini_client.generate_content(
            content_parts,
            generation_config=types.GenerationConfig(
                temperature=0.1,
                top_p=0.8,
                top_k=40,
                max_output_tokens=8192,
                response_mime_type="application/json"
            )
        )

        # Parse JSON response
        response_text = response.text.strip()

        # Clean up response text
        if response_text.startswith('```json'):
            response_text = response_text[7:]
        if response_text.endswith('```'):
            response_text = response_text[:-3]

        response_text = response_text.strip()

        try:
            ai_generated_note_json = json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.error("❌ Failed to parse Gemini JSON response", error=str(e), response=response_text[:500])
            # Return a structured error response
            ai_generated_note_json = {
                "error": "Failed to parse AI response",
                "raw_response": response_text[:1000],
                "consultation_type": consultation_type
            }

        logger.info("✅ Gemini content generated successfully",
                   consultation_type=consultation_type)

        return ai_generated_note_json

    except Exception as e:
        logger.error("❌ Gemini content generation failed",
                   consultation_type=consultation_type,
                   error=str(e))

        # Return error structure
        return {
            "error": str(e),
            "consultation_type": consultation_type,
            "status": "generation_failed"
        }

# ============================================================================
# MAIN APPLICATION STARTUP
# ============================================================================

if __name__ == "__main__":
    import uvicorn

    # Initialize worker components
    initialize_worker()

    # Start the FastAPI server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8080)),
        log_level="info"
    )
