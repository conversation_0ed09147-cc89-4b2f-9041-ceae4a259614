/**
 * CELER AI ENTERPRISE ARCHITECTURE 3.0
 * File Upload Saga - Atomic File Operations with Compensation
 * 
 * This saga ensures atomic file upload operations with automatic cleanup
 * on failure, preventing orphaned files in R2 storage.
 */

import { uploadFileDirectToR2, validateFileForUpload } from '@/lib/storage/direct-r2-upload'
import { deleteFile } from '@/lib/storage/atomic-storage'
import { calculateFileChecksum } from '@/lib/validation/consultation-schemas'
import { getRedisClient } from '@/lib/redis'

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface FileUploadResult {
  url: string
  checksum: string
  size: number
  filename: string
}

export interface FileUploadBatch {
  primary_audio: FileUploadResult
  additional_audio: FileUploadResult[]
  images: FileUploadResult[]
}

export interface SagaState {
  saga_id: string
  correlation_id: string
  consultation_id: string
  current_step: string
  completed_steps: string[]
  uploaded_files: string[]
  failed_files: { filename: string; error: string }[]
  started_at: Date
  updated_at: Date
}

// =====================================================
// FILE UPLOAD SAGA CLASS
// =====================================================

export class FileUploadSaga {
  private sagaId: string
  private correlationId: string
  private consultationId: string
  private userId: string
  private uploadedFiles: string[] = []
  private failedFiles: { filename: string; error: string }[] = []
  private redis = getRedisClient()

  constructor(correlationId: string, consultationId: string, userId: string) {
    this.sagaId = crypto.randomUUID()
    this.correlationId = correlationId
    this.consultationId = consultationId
    this.userId = userId
  }

  /**
   * Execute the file upload saga
   */
  async execute(files: {
    primary_audio: File
    additional_audio: File[]
    images: File[]
  }): Promise<FileUploadBatch> {
    console.log(`🚀 Starting file upload saga ${this.sagaId}`)
    
    try {
      // Initialize saga state
      await this.initializeSagaState()
      
      // Step 1: Upload primary audio (critical path)
      await this.updateSagaStep('uploading_primary_audio')
      const primaryAudio = await this.uploadSingleFile(files.primary_audio, 'audio')
      await this.markStepCompleted('uploading_primary_audio')
      
      // Step 2: Upload additional audio files (parallel)
      await this.updateSagaStep('uploading_additional_audio')
      const additionalAudio = await this.uploadFilesInParallel(files.additional_audio, 'audio')
      await this.markStepCompleted('uploading_additional_audio')
      
      // Step 3: Upload image files (parallel)
      await this.updateSagaStep('uploading_images')
      const images = await this.uploadFilesInParallel(files.images, 'image')
      await this.markStepCompleted('uploading_images')
      
      // Step 4: Finalize saga
      await this.updateSagaStep('completed')
      await this.finalizeSaga()
      
      console.log(`✅ File upload saga ${this.sagaId} completed successfully`)
      
      return {
        primary_audio: primaryAudio,
        additional_audio: additionalAudio,
        images: images
      }
      
    } catch (error) {
      console.error(`❌ File upload saga ${this.sagaId} failed:`, error)
      await this.updateSagaStep('failed')
      
      // Trigger compensation
      await this.compensate()
      
      throw new Error(`File upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Upload a single file using direct-to-R2 pattern with pre-signed URLs
   */
  private async uploadSingleFile(file: File, type: 'audio' | 'image'): Promise<FileUploadResult> {
    try {
      console.log(`📁 Starting direct-to-R2 upload for ${type} file: ${file.name}`)

      // Step 1: Validate file before upload
      const validation = validateFileForUpload(file)
      if (!validation.valid) {
        throw new Error(`File validation failed: ${validation.error}`)
      }

      // Step 2: Use our direct-to-R2 upload utility
      const uploadResult = await uploadFileDirectToR2(file, this.consultationId)

      if (!uploadResult.success || !uploadResult.publicUrl) {
        throw new Error(uploadResult.error || 'Direct R2 upload failed')
      }

      // Step 3: Calculate checksum for integrity tracking
      const checksum = await calculateFileChecksum(file)

      // Track uploaded file for potential cleanup
      this.uploadedFiles.push(uploadResult.publicUrl)

      console.log(`✅ Direct-to-R2 upload completed in ${uploadResult.uploadTime}ms: ${uploadResult.publicUrl}`)

      const result: FileUploadResult = {
        url: uploadResult.publicUrl,
        checksum,
        size: file.size,
        filename: uploadResult.fileName || file.name
      }
      
      console.log(`✅ Successfully uploaded ${file.name}`)
      return result
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.failedFiles.push({ filename: file.name, error: errorMessage })
      throw error
    }
  }

  /**
   * Upload multiple files in parallel with error handling
   */
  private async uploadFilesInParallel(files: File[], type: 'audio' | 'image'): Promise<FileUploadResult[]> {
    if (files.length === 0) {
      return []
    }
    
    console.log(`📁 Uploading ${files.length} ${type} files in parallel`)
    
    const uploadPromises = files.map(file => 
      this.uploadSingleFile(file, type).catch(error => {
        console.error(`Failed to upload ${file.name}:`, error)
        return null // Return null for failed uploads
      })
    )
    
    const results = await Promise.all(uploadPromises)
    
    // Filter out failed uploads (null values)
    const successfulUploads = results.filter((result): result is FileUploadResult => result !== null)
    
    console.log(`✅ Successfully uploaded ${successfulUploads.length}/${files.length} ${type} files`)
    
    return successfulUploads
  }

  /**
   * Compensate for failed operations by cleaning up uploaded files
   */
  async compensate(): Promise<void> {
    console.log(`🔄 Starting compensation for saga ${this.sagaId}`)
    
    try {
      await this.updateSagaStep('compensating')
      
      // Clean up all uploaded files
      const cleanupPromises = this.uploadedFiles.map(async (fileUrl) => {
        try {
          await deleteFile(fileUrl)
          console.log(`🗑️ Cleaned up file: ${fileUrl}`)
        } catch (error) {
          console.error(`Failed to cleanup file ${fileUrl}:`, error)
          // Don't throw - best effort cleanup
        }
      })
      
      await Promise.all(cleanupPromises)
      
      await this.updateSagaStep('compensated')
      console.log(`✅ Compensation completed for saga ${this.sagaId}`)
      
    } catch (error) {
      console.error(`❌ Compensation failed for saga ${this.sagaId}:`, error)
      await this.updateSagaStep('compensation_failed')
    }
  }

  /**
   * Initialize saga state in Redis
   */
  private async initializeSagaState(): Promise<void> {
    const sagaState: SagaState = {
      saga_id: this.sagaId,
      correlation_id: this.correlationId,
      consultation_id: this.consultationId,
      current_step: 'initialized',
      completed_steps: [],
      uploaded_files: [],
      failed_files: [],
      started_at: new Date(),
      updated_at: new Date()
    }
    
    const sagaKey = `saga:file_upload:${this.sagaId}`
    await this.redis.hSet(sagaKey, {
      state: JSON.stringify(sagaState)
    })
    await this.redis.expire(sagaKey, 3600) // 1 hour TTL
  }

  /**
   * Update saga step
   */
  private async updateSagaStep(step: string): Promise<void> {
    const sagaKey = `saga:file_upload:${this.sagaId}`
    
    try {
      const existingState = await this.redis.hGet(sagaKey, 'state')
      if (existingState) {
        const sagaState: SagaState = JSON.parse(existingState)
        sagaState.current_step = step
        sagaState.updated_at = new Date()
        sagaState.uploaded_files = this.uploadedFiles
        sagaState.failed_files = this.failedFiles
        
        await this.redis.hSet(sagaKey, {
          state: JSON.stringify(sagaState)
        })
      }
    } catch (error) {
      console.error(`Failed to update saga step to ${step}:`, error)
      // Don't throw - saga state is for monitoring only
    }
  }

  /**
   * Mark step as completed
   */
  private async markStepCompleted(step: string): Promise<void> {
    const sagaKey = `saga:file_upload:${this.sagaId}`
    
    try {
      const existingState = await this.redis.hGet(sagaKey, 'state')
      if (existingState) {
        const sagaState: SagaState = JSON.parse(existingState)
        sagaState.completed_steps.push(step)
        sagaState.updated_at = new Date()
        
        await this.redis.hSet(sagaKey, {
          state: JSON.stringify(sagaState)
        })
      }
    } catch (error) {
      console.error(`Failed to mark step ${step} as completed:`, error)
      // Don't throw - saga state is for monitoring only
    }
  }

  /**
   * Finalize saga state
   */
  private async finalizeSaga(): Promise<void> {
    const sagaKey = `saga:file_upload:${this.sagaId}`
    
    try {
      const existingState = await this.redis.hGet(sagaKey, 'state')
      if (existingState) {
        const sagaState: SagaState = JSON.parse(existingState)
        sagaState.current_step = 'completed'
        sagaState.completed_steps.push('completed')
        sagaState.updated_at = new Date()
        
        await this.redis.hSet(sagaKey, {
          state: JSON.stringify(sagaState)
        })
        
        // Extend TTL for completed sagas (for audit purposes)
        await this.redis.expire(sagaKey, 86400) // 24 hours
      }
    } catch (error) {
      console.error(`Failed to finalize saga:`, error)
      // Don't throw - saga state is for monitoring only
    }
  }

  /**
   * Get saga state (for monitoring/debugging)
   */
  async getSagaState(): Promise<SagaState | null> {
    try {
      const sagaKey = `saga:file_upload:${this.sagaId}`
      const state = await this.redis.hGet(sagaKey, 'state')
      return state ? JSON.parse(state) : null
    } catch (error) {
      console.error(`Failed to get saga state:`, error)
      return null
    }
  }

  /**
   * Get saga ID for external tracking
   */
  getSagaId(): string {
    return this.sagaId
  }

  /**
   * Get uploaded files list
   */
  getUploadedFiles(): string[] {
    return [...this.uploadedFiles]
  }

  /**
   * Get failed files list
   */
  getFailedFiles(): { filename: string; error: string }[] {
    return [...this.failedFiles]
  }
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Clean up orphaned sagas (for background cleanup job)
 */
export async function cleanupOrphanedSagas(olderThanHours: number = 24): Promise<number> {
  const redis = getRedisClient()
  let cleanedCount = 0
  
  try {
    const pattern = 'saga:file_upload:*'
    const keys = await redis.keys(pattern)
    
    for (const key of keys) {
      try {
        const state = await redis.hGet(key, 'state')
        if (state) {
          const sagaState: SagaState = JSON.parse(state)
          const ageHours = (Date.now() - new Date(sagaState.started_at).getTime()) / (1000 * 60 * 60)
          
          if (ageHours > olderThanHours && sagaState.current_step !== 'completed') {
            // Trigger compensation for old incomplete sagas
            const saga = new FileUploadSaga(
              sagaState.correlation_id,
              sagaState.consultation_id,
              'system' // System cleanup
            )
            
            // Set the uploaded files for cleanup
            saga['uploadedFiles'] = sagaState.uploaded_files
            await saga.compensate()
            
            // Remove saga state
            await redis.del(key)
            cleanedCount++
          }
        }
      } catch (error) {
        console.error(`Failed to cleanup saga ${key}:`, error)
      }
    }
    
    console.log(`🧹 Cleaned up ${cleanedCount} orphaned sagas`)
    return cleanedCount
    
  } catch (error) {
    console.error('Failed to cleanup orphaned sagas:', error)
    return 0
  }
}
