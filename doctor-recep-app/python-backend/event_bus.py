"""
CELER AI ENTERPRISE ARCHITECTURE 3.0
Event Bus - Python Backend Pub/Sub Integration

This module handles Pub/Sub message consumption with intelligent batching,
error handling, and saga coordination for the Python worker.
"""

import os
import json
import base64
import asyncio
import logging
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from google.cloud import pubsub_v1
from google.cloud.pubsub_v1.types import FlowControl, RetryPolicy
from concurrent.futures import ThreadPoolExecutor
import redis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =====================================================
# CONFIGURATION
# =====================================================

PUBSUB_CONFIG = {
    'project_id': os.getenv('GOOGLE_CLOUD_PROJECT_ID'),
    'subscription_name': 'consultation-processing-subscription',
    'dead_letter_subscription': 'consultation-processing-dlq-subscription',
    
    # Flow control for intelligent batching
    'flow_control': {
        'max_messages': 10,  # Batch size
        'max_bytes': 100 * 1024 * 1024,  # 100MB
        'max_duration_seconds': 1.0  # Max wait time for batch
    },
    
    # Retry configuration
    'retry_policy': {
        'minimum_backoff_seconds': 10,
        'maximum_backoff_seconds': 600,
        'backoff_multiplier': 2.0,
        'max_retry_delay_seconds': 600
    },
    
    # Acknowledgment settings
    'ack_deadline_seconds': 600,  # 10 minutes for processing
    'max_extension_seconds': 3600  # 1 hour maximum
}

# =====================================================
# MESSAGE SCHEMAS
# =====================================================

@dataclass
class ConsultationEvent:
    """Consultation event from Pub/Sub"""
    schema_version: str
    correlation_id: str
    consultation_id: str
    event_type: str
    timestamp: str
    user_id: str
    primary_audio_url: str
    additional_audio_urls: List[str]
    image_urls: List[str]
    consultation_type: str
    submitted_by: str
    patient_name: Optional[str] = None
    doctor_notes: Optional[str] = None
    additional_notes: Optional[str] = None
    total_file_size_bytes: int = 0
    retry_count: int = 0
    priority: str = 'normal'
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class BatchProcessingContext:
    """Context for batch processing"""
    batch_id: str
    batch_size: int
    events: List[ConsultationEvent]
    messages: List[pubsub_v1.types.ReceivedMessage]
    started_at: datetime
    correlation_ids: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'batch_id': self.batch_id,
            'batch_size': self.batch_size,
            'correlation_ids': self.correlation_ids,
            'started_at': self.started_at.isoformat(),
            'event_types': [event.event_type for event in self.events],
            'consultation_ids': [event.consultation_id for event in self.events]
        }

# =====================================================
# EVENT BUS CLIENT
# =====================================================

class EventBusClient:
    """Enhanced Pub/Sub client with intelligent batching"""
    
    def __init__(self):
        self.project_id = PUBSUB_CONFIG['project_id']
        self.subscriber = pubsub_v1.SubscriberClient()
        self.publisher = pubsub_v1.PublisherClient()
        self.redis_client = self._init_redis()
        self.message_handler: Optional[Callable] = None
        self.is_running = False
        self.batch_processor = None
        
        # Configure flow control
        self.flow_control = FlowControl(
            max_messages=PUBSUB_CONFIG['flow_control']['max_messages'],
            max_bytes=PUBSUB_CONFIG['flow_control']['max_bytes']
        )
        
        logger.info("🚀 Event Bus Client initialized")
    
    def _init_redis(self) -> redis.Redis:
        """Initialize Redis client for state management"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
        except Exception as e:
            logger.error(f"❌ Failed to initialize Redis: {e}")
            raise
    
    def set_message_handler(self, handler: Callable[[List[ConsultationEvent]], None]):
        """Set the message handler for processing batches"""
        self.message_handler = handler
        logger.info("✅ Message handler registered")
    
    async def start_consuming(self):
        """Start consuming messages with intelligent batching"""
        if not self.message_handler:
            raise ValueError("Message handler must be set before starting consumption")
        
        self.is_running = True
        subscription_path = self.subscriber.subscription_path(
            self.project_id, 
            PUBSUB_CONFIG['subscription_name']
        )
        
        logger.info(f"🔄 Starting message consumption from {subscription_path}")
        
        # Configure subscription settings
        self.subscriber.modify_ack_deadline(
            request={
                "subscription": subscription_path,
                "ack_deadline_seconds": PUBSUB_CONFIG['ack_deadline_seconds']
            }
        )
        
        # Start batch processor
        self.batch_processor = BatchProcessor(
            self.message_handler,
            self.redis_client,
            PUBSUB_CONFIG['flow_control']['max_duration_seconds']
        )
        
        # Start consuming with flow control
        try:
            streaming_pull_future = self.subscriber.subscribe(
                subscription_path,
                callback=self._message_callback,
                flow_control=self.flow_control
            )
            
            logger.info("✅ Message consumption started")
            
            # Keep the main thread running
            try:
                streaming_pull_future.result()
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal")
                streaming_pull_future.cancel()
                
        except Exception as e:
            logger.error(f"❌ Error in message consumption: {e}")
            raise
        finally:
            self.is_running = False
            if self.batch_processor:
                await self.batch_processor.shutdown()
    
    def _message_callback(self, message: pubsub_v1.types.ReceivedMessage):
        """Callback for individual messages - adds to batch processor"""
        try:
            # Parse message
            event = self._parse_message(message)
            if not event:
                message.nack()
                return
            
            # Add to batch processor
            if self.batch_processor:
                self.batch_processor.add_message(event, message)
            else:
                logger.error("❌ Batch processor not initialized")
                message.nack()
                
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            message.nack()
    
    def _parse_message(self, message: pubsub_v1.types.ReceivedMessage) -> Optional[ConsultationEvent]:
        """Parse Pub/Sub message into ConsultationEvent"""
        try:
            # Decode message data
            message_data = json.loads(message.data.decode('utf-8'))
            
            # Validate required fields
            required_fields = [
                'correlation_id', 'consultation_id', 'event_type',
                'user_id', 'primary_audio_url', 'consultation_type', 'submitted_by'
            ]
            
            for field in required_fields:
                if field not in message_data:
                    logger.error(f"❌ Missing required field: {field}")
                    return None
            
            # Create event object
            event = ConsultationEvent(
                schema_version=message_data.get('schema_version', '1.0'),
                correlation_id=message_data['correlation_id'],
                consultation_id=message_data['consultation_id'],
                event_type=message_data['event_type'],
                timestamp=message_data.get('timestamp', datetime.utcnow().isoformat()),
                user_id=message_data['user_id'],
                primary_audio_url=message_data['primary_audio_url'],
                additional_audio_urls=message_data.get('additional_audio_urls', []),
                image_urls=message_data.get('image_urls', []),
                consultation_type=message_data['consultation_type'],
                submitted_by=message_data['submitted_by'],
                patient_name=message_data.get('patient_name'),
                doctor_notes=message_data.get('doctor_notes'),
                additional_notes=message_data.get('additional_notes'),
                total_file_size_bytes=message_data.get('total_file_size_bytes', 0),
                retry_count=message_data.get('retry_count', 0),
                priority=message_data.get('priority', 'normal'),
                metadata=message_data.get('metadata', {})
            )
            
            logger.info(f"✅ Parsed message for consultation {event.consultation_id}")
            return event
            
        except Exception as e:
            logger.error(f"❌ Failed to parse message: {e}")
            return None
    
    async def publish_status_update(self, consultation_id: str, status: str, metadata: Dict[str, Any] = None):
        """Publish status update event"""
        try:
            topic_path = self.publisher.topic_path(self.project_id, 'consultation-status-updates')
            
            event_data = {
                'consultation_id': consultation_id,
                'status': status,
                'timestamp': datetime.utcnow().isoformat(),
                'metadata': metadata or {}
            }
            
            message_data = json.dumps(event_data).encode('utf-8')
            future = self.publisher.publish(topic_path, message_data)
            
            message_id = future.result()
            logger.info(f"✅ Status update published: {message_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to publish status update: {e}")

# =====================================================
# BATCH PROCESSOR
# =====================================================

class BatchProcessor:
    """Intelligent batch processor for consultation events"""
    
    def __init__(self, message_handler: Callable, redis_client: redis.Redis, max_wait_seconds: float):
        self.message_handler = message_handler
        self.redis_client = redis_client
        self.max_wait_seconds = max_wait_seconds
        self.current_batch: List[tuple] = []  # (event, message) tuples
        self.batch_timer: Optional[asyncio.Task] = None
        self.is_processing = False
        self.executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="batch-processor")
        
        logger.info("🚀 Batch processor initialized")
    
    def add_message(self, event: ConsultationEvent, message: pubsub_v1.types.ReceivedMessage):
        """Add message to current batch"""
        self.current_batch.append((event, message))
        
        # Check if batch is full
        if len(self.current_batch) >= PUBSUB_CONFIG['flow_control']['max_messages']:
            asyncio.create_task(self._process_batch())
        elif len(self.current_batch) == 1:
            # Start timer for first message in batch
            self.batch_timer = asyncio.create_task(self._batch_timer())
    
    async def _batch_timer(self):
        """Timer to process batch after max wait time"""
        try:
            await asyncio.sleep(self.max_wait_seconds)
            if self.current_batch and not self.is_processing:
                await self._process_batch()
        except asyncio.CancelledError:
            pass  # Timer was cancelled, which is normal
    
    async def _process_batch(self):
        """Process current batch of messages"""
        if self.is_processing or not self.current_batch:
            return
        
        self.is_processing = True
        
        # Cancel timer if running
        if self.batch_timer and not self.batch_timer.done():
            self.batch_timer.cancel()
        
        # Extract batch
        batch_items = self.current_batch.copy()
        self.current_batch.clear()
        
        batch_id = f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{len(batch_items)}"
        
        try:
            # Create batch context
            events = [item[0] for item in batch_items]
            messages = [item[1] for item in batch_items]
            
            context = BatchProcessingContext(
                batch_id=batch_id,
                batch_size=len(events),
                events=events,
                messages=messages,
                started_at=datetime.utcnow(),
                correlation_ids=[event.correlation_id for event in events]
            )
            
            logger.info(f"🚀 Processing batch {batch_id} with {len(events)} events")
            
            # Store batch context in Redis
            await self._store_batch_context(context)
            
            # Process batch in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, self.message_handler, events)
            
            # Acknowledge all messages on success
            for message in messages:
                message.ack()
            
            logger.info(f"✅ Batch {batch_id} processed successfully")
            
        except Exception as e:
            logger.error(f"❌ Batch {batch_id} processing failed: {e}")
            
            # Nack all messages for retry
            for message in messages:
                message.nack()
        
        finally:
            self.is_processing = False
    
    async def _store_batch_context(self, context: BatchProcessingContext):
        """Store batch context in Redis for monitoring"""
        try:
            key = f"batch:processing:{context.batch_id}"
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.redis_client.hset,
                key,
                mapping={
                    'context': json.dumps(context.to_dict()),
                    'status': 'processing',
                    'created_at': datetime.utcnow().isoformat()
                }
            )
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.redis_client.expire,
                key,
                3600  # 1 hour TTL
            )
        except Exception as e:
            logger.error(f"❌ Failed to store batch context: {e}")
    
    async def shutdown(self):
        """Shutdown batch processor"""
        logger.info("🛑 Shutting down batch processor")
        
        # Process any remaining messages
        if self.current_batch:
            await self._process_batch()
        
        # Cancel timer
        if self.batch_timer and not self.batch_timer.done():
            self.batch_timer.cancel()
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        logger.info("✅ Batch processor shutdown complete")

# =====================================================
# SINGLETON INSTANCE
# =====================================================

_event_bus_client: Optional[EventBusClient] = None

def get_event_bus_client() -> EventBusClient:
    """Get singleton event bus client"""
    global _event_bus_client
    if _event_bus_client is None:
        _event_bus_client = EventBusClient()
    return _event_bus_client

# =====================================================
# UTILITY FUNCTIONS
# =====================================================

async def health_check() -> Dict[str, Any]:
    """Health check for event bus"""
    try:
        client = get_event_bus_client()
        
        # Test Redis connection
        redis_healthy = False
        try:
            client.redis_client.ping()
            redis_healthy = True
        except Exception:
            pass
        
        return {
            'healthy': redis_healthy,
            'redis_connected': redis_healthy,
            'is_consuming': client.is_running,
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        return {
            'healthy': False,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }
