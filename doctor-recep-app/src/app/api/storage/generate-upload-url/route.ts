/**
 * LEAN ENTERPRISE ARCHITECTURE 3.0
 * Secure R2 Pre-Signed URL Generation
 * 
 * This endpoint generates temporary, secure upload URLs for direct client-to-R2 uploads.
 * Zero additional infrastructure cost - runs on existing Vercel serverless functions.
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { z } from 'zod'

// =====================================================
// CONFIGURATION
// =====================================================

// Configuration will be initialized inside request handlers to avoid build-time execution

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

const GenerateUploadUrlSchema = z.object({
  consultationId: z.string().uuid('Invalid consultation ID format'),
  fileName: z.string().min(1, 'File name is required').max(255, 'File name too long'),
  fileType: z.string().min(1, 'File type is required'),
  fileSize: z.number().positive('File size must be positive').max(100 * 1024 * 1024, 'File too large (max 100MB)')
})

// =====================================================
// SECURITY & VALIDATION
// =====================================================

async function validateUser(request: NextRequest, supabase: any) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return { error: 'Missing or invalid authorization header', user: null }
    }

    const token = authHeader.substring(7)
    const { data: { user }, error } = await supabase.auth.getUser(token)

    if (error || !user) {
      return { error: 'Invalid or expired token', user: null }
    }

    // Check if user is approved
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('approved')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.approved) {
      return { error: 'User not approved for uploads', user: null }
    }

    return { user, error: null }
  } catch (error) {
    return { error: 'Authentication failed', user: null }
  }
}

function validateFileType(fileType: string, fileName: string): boolean {
  const allowedTypes = {
    // Audio types
    'audio/webm': ['.webm'],
    'audio/mp4': ['.mp4', '.m4a'],
    'audio/mpeg': ['.mp3'],
    'audio/wav': ['.wav'],
    'audio/ogg': ['.ogg'],
    
    // Image types
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/webp': ['.webp'],
    'image/heic': ['.heic'],
    'image/heif': ['.heif']
  }

  const extensions = allowedTypes[fileType as keyof typeof allowedTypes]
  if (!extensions) {
    return false
  }

  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return extensions.includes(fileExtension)
}

function generateSecureFileName(consultationId: string, originalFileName: string, userId: string): string {
  // Create a secure, deterministic file path
  const timestamp = Date.now()
  const fileExtension = originalFileName.substring(originalFileName.lastIndexOf('.'))
  const sanitizedName = originalFileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .substring(0, 50) // Limit length
  
  return `consultations/${consultationId}/${timestamp}_${sanitizedName}`
}

// =====================================================
// MAIN HANDLER
// =====================================================

export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    console.log('🔐 Generating secure R2 upload URL')

    // Initialize clients inside handler to avoid build-time execution
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // R2 client configuration (S3-compatible)
    const r2Client = new S3Client({
      region: 'auto',
      endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    })

    // 1. Validate user authentication
    const { user, error: authError } = await validateUser(request, supabase)
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError)
      return NextResponse.json(
        { error: authError || 'Authentication required' },
        { status: 401 }
      )
    }

    // 2. Parse and validate request body
    const body = await request.json()
    const validationResult = GenerateUploadUrlSchema.safeParse(body)
    
    if (!validationResult.success) {
      console.error('❌ Validation failed:', validationResult.error.errors)
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { consultationId, fileName, fileType, fileSize } = validationResult.data

    // 3. Additional file type validation
    if (!validateFileType(fileType, fileName)) {
      console.error('❌ Invalid file type:', fileType, 'for file:', fileName)
      return NextResponse.json(
        { error: 'File type not allowed or doesn\'t match extension' },
        { status: 400 }
      )
    }

    // 4. Generate secure file path
    const secureFileName = generateSecureFileName(consultationId, fileName, user.id)
    
    // 5. Create pre-signed URL for upload
    const putObjectCommand = new PutObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: secureFileName,
      ContentType: fileType,
      ContentLength: fileSize,
      Metadata: {
        'consultation-id': consultationId,
        'user-id': user.id,
        'original-filename': fileName,
        'upload-timestamp': Date.now().toString()
      }
    })

    // Generate pre-signed URL (valid for 5 minutes)
    const preSignedUrl = await getSignedUrl(r2Client, putObjectCommand, {
      expiresIn: 300 // 5 minutes
    })

    // 6. Generate the final public URL (for later use)
    const publicUrl = `https://${process.env.R2_PUBLIC_DOMAIN}/${secureFileName}`

    // 7. Log the upload request for monitoring
    console.log('✅ Pre-signed URL generated', {
      consultationId,
      fileName: secureFileName,
      fileType,
      fileSize,
      userId: user.id,
      duration: Date.now() - startTime
    })

    // 8. Return the pre-signed URL and metadata
    return NextResponse.json({
      success: true,
      uploadUrl: preSignedUrl,
      publicUrl: publicUrl,
      fileName: secureFileName,
      expiresIn: 300,
      metadata: {
        consultationId,
        originalFileName: fileName,
        fileType,
        fileSize
      }
    })

  } catch (error) {
    console.error('❌ Failed to generate upload URL:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to generate upload URL',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      },
      { status: 500 }
    )
  }
}

// =====================================================
// HEALTH CHECK
// =====================================================

export async function GET() {
  try {
    // Quick health check - verify R2 credentials are configured
    const requiredEnvVars = [
      'R2_ACCESS_KEY_ID',
      'R2_SECRET_ACCESS_KEY',
      'R2_BUCKET_NAME',
      'R2_PUBLIC_DOMAIN',
      'CLOUDFLARE_ACCOUNT_ID'
    ]

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
    
    if (missingVars.length > 0) {
      return NextResponse.json(
        { 
          healthy: false, 
          error: 'Missing required environment variables',
          missing: missingVars
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      healthy: true,
      service: 'R2 Upload URL Generator',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return NextResponse.json(
      { 
        healthy: false, 
        error: 'Health check failed',
        details: String(error)
      },
      { status: 500 }
    )
  }
}
