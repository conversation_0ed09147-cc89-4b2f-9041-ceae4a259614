"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 2
Dynamic Batch Processing Engine with Adaptive Sizing

This module implements intelligent batch processing with adaptive sizing
based on system load, resource availability, and processing patterns.
"""

import os
import asyncio
import time
import psutil
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import redis
import json

from monitoring import logger, metrics, performance_context
from message_schemas import ConsultationCreateEvent

# =====================================================
# CONFIGURATION
# =====================================================

BATCH_CONFIG = {
    'min_batch_size': 1,
    'max_batch_size': 20,
    'default_batch_size': 10,
    'max_wait_time_seconds': 2.0,
    'min_wait_time_seconds': 0.1,
    
    # Resource thresholds for adaptive sizing
    'cpu_threshold_high': 80.0,
    'cpu_threshold_low': 40.0,
    'memory_threshold_high': 85.0,
    'memory_threshold_low': 60.0,
    
    # Performance targets
    'target_processing_time_seconds': 30.0,
    'max_processing_time_seconds': 120.0,
    
    # Adaptive parameters
    'size_adjustment_factor': 0.2,
    'performance_window_size': 10,
    'load_check_interval': 5.0
}

# =====================================================
# DATA STRUCTURES
# =====================================================

@dataclass
class BatchMetrics:
    """Metrics for batch processing performance"""
    batch_id: str
    batch_size: int
    processing_time_seconds: float
    cpu_usage_percent: float
    memory_usage_mb: float
    success_count: int
    failure_count: int
    timestamp: datetime
    
    def success_rate(self) -> float:
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0

@dataclass
class SystemLoad:
    """Current system load metrics"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_percent: float
    load_average: float
    timestamp: datetime

@dataclass
class BatchConfiguration:
    """Dynamic batch configuration"""
    current_batch_size: int
    current_wait_time: float
    last_adjustment: datetime
    performance_history: List[BatchMetrics]
    
    def add_metrics(self, metrics: BatchMetrics):
        """Add metrics and maintain window size"""
        self.performance_history.append(metrics)
        if len(self.performance_history) > BATCH_CONFIG['performance_window_size']:
            self.performance_history.pop(0)

# =====================================================
# ADAPTIVE BATCH SIZER
# =====================================================

class AdaptiveBatchSizer:
    """Intelligent batch sizing based on system performance"""
    
    def __init__(self):
        self.config = BatchConfiguration(
            current_batch_size=BATCH_CONFIG['default_batch_size'],
            current_wait_time=BATCH_CONFIG['max_wait_time_seconds'],
            last_adjustment=datetime.utcnow(),
            performance_history=[]
        )
        self.redis_client = self._init_redis()
        
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client for state persistence"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
        except Exception as e:
            logger.error("Failed to initialize Redis for batch sizer", error=str(e))
            return None
    
    def get_current_system_load(self) -> SystemLoad:
        """Get current system resource utilization"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            load_avg = os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0.0
            
            return SystemLoad(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                disk_percent=disk.percent,
                load_average=load_avg,
                timestamp=datetime.utcnow()
            )
        except Exception as e:
            logger.error("Failed to get system load", error=str(e))
            return SystemLoad(0, 0, 0, 0, 0, datetime.utcnow())
    
    def calculate_optimal_batch_size(self, pending_messages: int) -> int:
        """Calculate optimal batch size based on current conditions"""
        system_load = self.get_current_system_load()
        
        # Start with current batch size
        optimal_size = self.config.current_batch_size
        
        # Adjust based on system load
        if system_load.cpu_percent > BATCH_CONFIG['cpu_threshold_high']:
            # High CPU - reduce batch size
            optimal_size = max(
                BATCH_CONFIG['min_batch_size'],
                int(optimal_size * (1 - BATCH_CONFIG['size_adjustment_factor']))
            )
            logger.info("Reducing batch size due to high CPU", 
                       cpu_percent=system_load.cpu_percent,
                       new_size=optimal_size)
        
        elif system_load.cpu_percent < BATCH_CONFIG['cpu_threshold_low']:
            # Low CPU - can increase batch size
            optimal_size = min(
                BATCH_CONFIG['max_batch_size'],
                int(optimal_size * (1 + BATCH_CONFIG['size_adjustment_factor']))
            )
            logger.info("Increasing batch size due to low CPU", 
                       cpu_percent=system_load.cpu_percent,
                       new_size=optimal_size)
        
        # Adjust based on memory usage
        if system_load.memory_percent > BATCH_CONFIG['memory_threshold_high']:
            optimal_size = max(
                BATCH_CONFIG['min_batch_size'],
                int(optimal_size * 0.8)  # More aggressive reduction for memory
            )
            logger.warning("Reducing batch size due to high memory usage", 
                          memory_percent=system_load.memory_percent,
                          new_size=optimal_size)
        
        # Adjust based on recent performance
        if len(self.config.performance_history) >= 3:
            avg_processing_time = sum(
                m.processing_time_seconds for m in self.config.performance_history[-3:]
            ) / 3
            
            if avg_processing_time > BATCH_CONFIG['target_processing_time_seconds']:
                # Processing is slow - reduce batch size
                optimal_size = max(
                    BATCH_CONFIG['min_batch_size'],
                    int(optimal_size * 0.9)
                )
                logger.info("Reducing batch size due to slow processing", 
                           avg_processing_time=avg_processing_time,
                           new_size=optimal_size)
        
        # Don't exceed available messages
        optimal_size = min(optimal_size, pending_messages)
        
        # Update configuration
        self.config.current_batch_size = optimal_size
        self.config.last_adjustment = datetime.utcnow()
        
        # Persist configuration
        self._persist_configuration()
        
        return optimal_size
    
    def calculate_optimal_wait_time(self, current_batch_size: int) -> float:
        """Calculate optimal wait time based on batch size and load"""
        system_load = self.get_current_system_load()
        
        # Base wait time inversely proportional to system load
        if system_load.cpu_percent > BATCH_CONFIG['cpu_threshold_high']:
            # High load - wait longer to accumulate more messages
            wait_time = BATCH_CONFIG['max_wait_time_seconds']
        else:
            # Low load - can process more frequently
            wait_time = BATCH_CONFIG['min_wait_time_seconds']
        
        # Adjust based on current batch size
        if current_batch_size < BATCH_CONFIG['default_batch_size']:
            # Small batch - wait longer to accumulate more
            wait_time = min(
                BATCH_CONFIG['max_wait_time_seconds'],
                wait_time * 1.5
            )
        
        self.config.current_wait_time = wait_time
        return wait_time
    
    def record_batch_performance(self, metrics: BatchMetrics):
        """Record batch performance for future optimization"""
        self.config.add_metrics(metrics)
        
        # Store metrics in Redis for monitoring
        if self.redis_client:
            try:
                metrics_key = f"batch_metrics:{metrics.batch_id}"
                self.redis_client.hset(metrics_key, mapping=asdict(metrics))
                self.redis_client.expire(metrics_key, 86400)  # 24 hours
                
                # Update aggregate metrics
                self._update_aggregate_metrics(metrics)
                
            except Exception as e:
                logger.error("Failed to store batch metrics", error=str(e))
    
    def _update_aggregate_metrics(self, metrics: BatchMetrics):
        """Update aggregate performance metrics"""
        try:
            # Update counters
            metrics.increment_counter('batch_processing_total', 
                                    {'status': 'completed'})
            metrics.record_histogram('batch_processing_duration_seconds', 
                                   metrics.processing_time_seconds)
            metrics.record_histogram('batch_size_distribution', 
                                   metrics.batch_size)
            metrics.set_gauge('batch_success_rate', 
                            metrics.success_rate())
            
        except Exception as e:
            logger.error("Failed to update aggregate metrics", error=str(e))
    
    def _persist_configuration(self):
        """Persist current configuration to Redis"""
        if self.redis_client:
            try:
                config_key = "batch_processor:configuration"
                config_data = {
                    'current_batch_size': self.config.current_batch_size,
                    'current_wait_time': self.config.current_wait_time,
                    'last_adjustment': self.config.last_adjustment.isoformat(),
                    'performance_history_count': len(self.config.performance_history)
                }
                
                self.redis_client.hset(config_key, mapping=config_data)
                self.redis_client.expire(config_key, 3600)  # 1 hour
                
            except Exception as e:
                logger.error("Failed to persist batch configuration", error=str(e))
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for monitoring"""
        if not self.config.performance_history:
            return {'status': 'no_data'}
        
        recent_metrics = self.config.performance_history[-5:]  # Last 5 batches
        
        avg_processing_time = sum(m.processing_time_seconds for m in recent_metrics) / len(recent_metrics)
        avg_success_rate = sum(m.success_rate() for m in recent_metrics) / len(recent_metrics)
        avg_batch_size = sum(m.batch_size for m in recent_metrics) / len(recent_metrics)
        
        return {
            'status': 'active',
            'current_batch_size': self.config.current_batch_size,
            'current_wait_time': self.config.current_wait_time,
            'avg_processing_time_seconds': avg_processing_time,
            'avg_success_rate': avg_success_rate,
            'avg_batch_size': avg_batch_size,
            'total_batches_processed': len(self.config.performance_history),
            'last_adjustment': self.config.last_adjustment.isoformat()
        }

# =====================================================
# DYNAMIC BATCH PROCESSOR
# =====================================================

class DynamicBatchProcessor:
    """Main batch processor with adaptive sizing"""
    
    def __init__(self, message_handler: Callable):
        self.message_handler = message_handler
        self.batch_sizer = AdaptiveBatchSizer()
        self.current_batch: List[tuple] = []  # (event, message) tuples
        self.batch_timer: Optional[asyncio.Task] = None
        self.is_processing = False
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="dynamic-batch")
        
        logger.info("🚀 Dynamic batch processor initialized")
    
    async def add_message(self, event: ConsultationCreateEvent, message: Any):
        """Add message to current batch with dynamic sizing"""
        self.current_batch.append((event, message))
        
        # Calculate optimal batch size
        optimal_size = self.batch_sizer.calculate_optimal_batch_size(len(self.current_batch))
        
        # Check if we should process now
        if len(self.current_batch) >= optimal_size:
            await self._process_batch()
        elif len(self.current_batch) == 1:
            # Start adaptive timer for first message
            wait_time = self.batch_sizer.calculate_optimal_wait_time(optimal_size)
            self.batch_timer = asyncio.create_task(self._adaptive_timer(wait_time))
    
    async def _adaptive_timer(self, wait_time: float):
        """Adaptive timer that adjusts based on system load"""
        try:
            await asyncio.sleep(wait_time)
            if self.current_batch and not self.is_processing:
                await self._process_batch()
        except asyncio.CancelledError:
            pass
    
    async def _process_batch(self):
        """Process current batch with performance tracking"""
        if self.is_processing or not self.current_batch:
            return
        
        self.is_processing = True
        
        # Cancel timer if running
        if self.batch_timer and not self.batch_timer.done():
            self.batch_timer.cancel()
        
        # Extract batch
        batch_items = self.current_batch.copy()
        self.current_batch.clear()
        
        batch_id = f"dynamic_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{len(batch_items)}"
        start_time = time.time()
        
        try:
            with performance_context("dynamic_batch_processing"):
                logger.info("🚀 Processing dynamic batch", 
                           batch_id=batch_id, 
                           batch_size=len(batch_items))
                
                # Get initial system metrics
                initial_load = self.batch_sizer.get_current_system_load()
                
                # Process batch
                events = [item[0] for item in batch_items]
                messages = [item[1] for item in batch_items]
                
                # Execute processing in thread pool
                loop = asyncio.get_event_loop()
                results = await loop.run_in_executor(
                    self.executor, 
                    self.message_handler, 
                    events
                )
                
                # Calculate performance metrics
                processing_time = time.time() - start_time
                final_load = self.batch_sizer.get_current_system_load()
                
                # Count successes and failures
                success_count = len([r for r in results if r.get('success', False)])
                failure_count = len(results) - success_count
                
                # Create batch metrics
                batch_metrics = BatchMetrics(
                    batch_id=batch_id,
                    batch_size=len(batch_items),
                    processing_time_seconds=processing_time,
                    cpu_usage_percent=final_load.cpu_percent,
                    memory_usage_mb=final_load.memory_used_mb,
                    success_count=success_count,
                    failure_count=failure_count,
                    timestamp=datetime.utcnow()
                )
                
                # Record performance for adaptive sizing
                self.batch_sizer.record_batch_performance(batch_metrics)
                
                # Acknowledge messages on success
                for message in messages:
                    if hasattr(message, 'ack'):
                        message.ack()
                
                logger.info("✅ Dynamic batch processed successfully", 
                           batch_id=batch_id,
                           processing_time=processing_time,
                           success_rate=batch_metrics.success_rate())
                
        except Exception as e:
            logger.error("❌ Dynamic batch processing failed", 
                        batch_id=batch_id, 
                        error=str(e))
            
            # Nack messages for retry
            for message in messages:
                if hasattr(message, 'nack'):
                    message.nack()
        
        finally:
            self.is_processing = False
    
    async def shutdown(self):
        """Shutdown batch processor"""
        logger.info("🛑 Shutting down dynamic batch processor")
        
        # Process any remaining messages
        if self.current_batch:
            await self._process_batch()
        
        # Cancel timer
        if self.batch_timer and not self.batch_timer.done():
            self.batch_timer.cancel()
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        logger.info("✅ Dynamic batch processor shutdown complete")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current processor status"""
        return {
            'is_processing': self.is_processing,
            'current_batch_size': len(self.current_batch),
            'performance_summary': self.batch_sizer.get_performance_summary(),
            'timestamp': datetime.utcnow().isoformat()
        }

# =====================================================
# GLOBAL INSTANCE
# =====================================================

_batch_processor: Optional[DynamicBatchProcessor] = None

def get_batch_processor(message_handler: Callable = None) -> DynamicBatchProcessor:
    """Get singleton batch processor"""
    global _batch_processor
    if _batch_processor is None and message_handler:
        _batch_processor = DynamicBatchProcessor(message_handler)
    return _batch_processor
