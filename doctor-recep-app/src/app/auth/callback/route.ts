import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { trackAuth } from '@/lib/analytics'

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const error = searchParams.get('error')
  const error_description = searchParams.get('error_description')
  
  // Handle OAuth errors
  if (error) {
    console.error('OAuth error:', error, error_description)
    return NextResponse.redirect(`${origin}/auth/error?message=${encodeURIComponent(error_description || error)}`)
  }

  // Handle missing code
  if (!code) {
    console.error('No authorization code received')
    return NextResponse.redirect(`${origin}/auth/error?message=No authorization code received`)
  }

  try {
    const supabase = await createClient()
    
    // Exchange code for session
    const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)
    
    if (exchangeError) {
      console.error('Code exchange error:', exchangeError)
      return NextResponse.redirect(`${origin}/auth/error?message=${encodeURIComponent(exchangeError.message)}`)
    }

    if (!data.user) {
      console.error('No user data after code exchange')
      return NextResponse.redirect(`${origin}/auth/error?message=Authentication failed`)
    }

    console.log('Google OAuth successful for user:', data.user.email)

    // Create/update profile for Google OAuth user
    await createGoogleUserProfile(supabase, data.user)

    // Check user profile and approval status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('approved, role')
      .eq('id', data.user.id)
      .single()

    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return NextResponse.redirect(`${origin}/auth/error?message=Failed to fetch user profile`)
    }

    // Check admin role restriction
    if (profile.role === 'admin' || profile.role === 'super_admin') {
      await supabase.auth.signOut()
      return NextResponse.redirect(`${origin}/auth/error?message=Admin users must use the admin login portal`)
    }

    // Track successful authentication
    trackAuth('login_successful')

    // Google users are now auto-approved, so they should always be approved at this point
    // But we'll keep a safety check just in case
    if (!profile.approved) {
      console.error('Google user not approved - this should not happen with auto-approval')
      return NextResponse.redirect(`${origin}/auth/error?message=Account approval failed`)
    }

    // Success - redirect to dashboard
    return NextResponse.redirect(`${origin}/dashboard`)

  } catch (error) {
    console.error('Unexpected error in OAuth callback:', error)
    return NextResponse.redirect(`${origin}/auth/error?message=An unexpected error occurred`)
  }
}

// Helper function to create/update Google user profile
async function createGoogleUserProfile(supabase: any, user: any) {
  try {
    // Extract user data from Google
    const fullName = user.user_metadata?.full_name || user.user_metadata?.name || ''
    const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture || null

    // Check if profile already exists
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single()

    if (existingProfile) {
      // Profile exists, just update avatar if needed
      if (avatarUrl) {
        await supabase
          .from('profiles')
          .update({ avatar_url: avatarUrl })
          .eq('id', user.id)
      }
      return
    }

    // Create new profile for Google OAuth user
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        email: user.email!,
        name: fullName,
        avatar_url: avatarUrl,
        role: 'doctor', // Default role
        approved: true, // Auto-approve Google users (email already verified by Google)
        approved_at: new Date().toISOString(),
        approved_by: null, // Auto-approved via Google OAuth
        clinic_name: null, // To be filled later
        phone: null, // To be filled later
      })

    if (profileError) {
      console.error('Error creating Google OAuth user profile:', profileError)
      throw new Error('Failed to create user profile')
    }

    console.log('Google OAuth user profile created successfully')
  } catch (error) {
    console.error('Error in createGoogleUserProfile:', error)
    throw error
  }
}
