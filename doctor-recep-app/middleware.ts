import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl

  // Define protected routes
  const protectedRoutes = ['/dashboard', '/info', '/settings', '/templates']
  const adminRoutes = ['/admin']
  const authRoutes = ['/login', '/signup', '/forgot-password', '/reset-password', '/auth/confirm', '/auth/error']

  // Check if current path is protected
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route))
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route))

  // Handle protected routes
  if (isProtectedRoute && !user) {
    // User is not authenticated, redirect to login
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/login'
    redirectUrl.searchParams.set('redirectedFrom', pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // Handle admin routes
  if (isAdminRoute && pathname !== '/admin/login') {
    if (!user) {
      // User is not authenticated, redirect to admin login
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/admin/login'
      return NextResponse.redirect(redirectUrl)
    }

    // THIS IS NOW INSTANTANEOUS - NO DATABASE CALL
    const userRole = user.app_metadata.role;

    if (userRole !== 'admin' && userRole !== 'super_admin') {
      // User is not an admin, redirect to admin login
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/admin/login'
      return NextResponse.redirect(redirectUrl)
    }
  }

  // Handle auth routes (login, signup)
  if (isAuthRoute && user) {
    // User is already authenticated, check if they're approved
    const userApproved = user.app_metadata.approved;

    if (userApproved) {
      // User is approved, redirect to appropriate dashboard based on role
      const redirectUrl = request.nextUrl.clone()
      const userRole = user.app_metadata.role;
      if (userRole === 'admin' || userRole === 'super_admin') {
        redirectUrl.pathname = '/admin/dashboard'
      } else {
        redirectUrl.pathname = '/dashboard'
      }
      return NextResponse.redirect(redirectUrl)
    }
    // If not approved, let them stay on auth pages (they might need phone verification)
  }

  // Handle root path
  if (pathname === '/') {
    if (user) {
      // User is authenticated, check their role and approval status
      const userApproved = user.app_metadata.approved;

      if (userApproved) {
        const redirectUrl = request.nextUrl.clone()
        const userRole = user.app_metadata.role;
        if (userRole === 'admin' || userRole === 'super_admin') {
          redirectUrl.pathname = '/admin/dashboard'
        } else {
          redirectUrl.pathname = '/dashboard'
        }
        return NextResponse.redirect(redirectUrl)
      }
      // If not approved, let them see the landing page
    }
    // If not authenticated, let them see the landing page
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes (handled separately)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$|api/).*)',
  ],
}
