'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

export type AutoSaveStatus = 'idle' | 'saving' | 'saved' | 'error'
export type AutoSaveType = 'audio' | 'images' | 'text' | 'json' | 'manual_retry'

interface AutoSaveOptions {
  onSuccess?: (type: AutoSaveType) => void
  onError?: (type: AutoSaveType, error: Error) => void
  textDebounceMs?: number
  audioDelayMs?: number
}

export const useAutosave = (options: AutoSaveOptions = {}) => {
  const [autoSaveStatus, setAutoSaveStatus] = useState<AutoSaveStatus>('idle')
  const [lastSavedValues, setLastSavedValues] = useState<Record<string, unknown>>({})

  // Use refs for timers to avoid dependency issues
  const textTimerRef = useRef<NodeJS.Timeout | null>(null)
  const audioTimerRef = useRef<NodeJS.Timeout | null>(null)

  const {
    onSuccess,
    onError,
    textDebounceMs = 2000,
    audioDelayMs = 1000
  } = options

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      if (textTimerRef.current) clearTimeout(textTimerRef.current)
      if (audioTimerRef.current) clearTimeout(audioTimerRef.current)
    }
  }, [])

  const executeAutoSave = useCallback(async (
    type: AutoSaveType,
    saveFunction: () => Promise<unknown>,
    data?: { field: string; value: unknown }
  ) => {
    // Show saving status for all types - cute animations are back!
    setAutoSaveStatus('saving')

    try {
      const result = await saveFunction()

      // Show cute 'saved' status briefly
      setAutoSaveStatus('saved')

      // Store last saved value for comparison
      if (data && data.field) {
        setLastSavedValues(prev => ({
          ...prev,
          [data.field]: data.value
        }))
      }

      onSuccess?.(type)

      // Auto-hide saved status after 2 seconds
      setTimeout(() => {
        setAutoSaveStatus(prev => prev === 'saved' ? 'idle' : prev)
      }, 2000)

      return result
    } catch (error) {
      console.error(`Autosave failed for ${type}:`, error)
      setAutoSaveStatus('error')
      const errorObj = error instanceof Error ? error : new Error(String(error))
      onError?.(type, errorObj)
      throw errorObj
    }
  }, [onSuccess, onError])

  // Image autosave (immediate)
  const autoSaveImages = useCallback(async (
    images: File[],
    saveFunction: (images: File[]) => Promise<unknown>
  ) => {
    if (images.length === 0) return
    
    return executeAutoSave('images', () => saveFunction(images))
  }, [executeAutoSave])

  // Audio autosave (delayed) - EVENT-DRIVEN, not reactive
  const autoSaveAudio = useCallback((
    audioBlob: Blob,
    saveFunction: (audio: Blob) => Promise<unknown>,
    immediate = false
  ) => {
    // Clear existing timer
    if (audioTimerRef.current) {
      clearTimeout(audioTimerRef.current)
      audioTimerRef.current = null
    }

    if (immediate) {
      return executeAutoSave('audio', () => saveFunction(audioBlob))
    }

    // Set delay timer
    const timer = setTimeout(async () => {
      try {
        await executeAutoSave('audio', () => saveFunction(audioBlob))
      } catch (_error) {
        // Error already handled in executeAutoSave
      }
      audioTimerRef.current = null
    }, audioDelayMs)

    audioTimerRef.current = timer
  }, [executeAutoSave, audioDelayMs])

  // Text autosave (debounced) - EVENT-DRIVEN, not reactive
  const autoSaveText = useCallback((
    field: string,
    value: string,
    saveFunction: (field: string, value: string) => Promise<unknown>
  ) => {
    // Clear existing timer
    if (textTimerRef.current) {
      clearTimeout(textTimerRef.current)
      textTimerRef.current = null
    }

    // Check if value actually changed
    const lastValue = lastSavedValues[field]
    if (lastValue === value) {
      return // No change, don't save
    }

    // Set debounce timer
    const timer = setTimeout(async () => {
      try {
        await executeAutoSave('text', () => saveFunction(field, value), { field, value })
      } catch (_error) {
        // Error already handled in executeAutoSave
      }
      textTimerRef.current = null
    }, textDebounceMs)

    textTimerRef.current = timer
  }, [executeAutoSave, textDebounceMs, lastSavedValues])

  // JSON autosave (debounced) - for structured consultation data
  const autoSaveJson = useCallback((
    jsonData: any,
    saveFunction: (data: any) => Promise<unknown>
  ) => {
    // Clear existing timer
    if (textTimerRef.current) {
      clearTimeout(textTimerRef.current)
      textTimerRef.current = null
    }

    // Check if JSON actually changed (deep comparison)
    const lastValue = lastSavedValues['json_data']
    if (JSON.stringify(lastValue) === JSON.stringify(jsonData)) {
      return // No change, don't save
    }

    // Set debounce timer
    const timer = setTimeout(async () => {
      try {
        await executeAutoSave('json', () => saveFunction(jsonData), { field: 'json_data', value: jsonData })
      } catch (_error) {
        // Error already handled in executeAutoSave
      }
      textTimerRef.current = null
    }, textDebounceMs)

    textTimerRef.current = timer
  }, [executeAutoSave, textDebounceMs, lastSavedValues])

  // Manual retry for failed saves
  const retryAutoSave = useCallback(async (
    saveFunction: () => Promise<unknown>
  ) => {
    return executeAutoSave('manual_retry', saveFunction)
  }, [executeAutoSave])

  // Force immediate save (bypass timers)
  const forceSave = useCallback(async (
    saveFunction: () => Promise<unknown>
  ) => {
    // Clear all timers
    if (textTimerRef.current) {
      clearTimeout(textTimerRef.current)
      textTimerRef.current = null
    }
    if (audioTimerRef.current) {
      clearTimeout(audioTimerRef.current)
      audioTimerRef.current = null
    }

    return executeAutoSave('manual_retry', saveFunction)
  }, [executeAutoSave])

  // Check if there are pending saves
  const hasPendingSaves = textTimerRef.current !== null || audioTimerRef.current !== null

  return {
    autoSaveStatus,
    setAutoSaveStatus,
    autoSaveImages,
    autoSaveAudio,
    autoSaveText,
    autoSaveJson,
    retryAutoSave,
    forceSave,
    hasPendingSaves,
    lastSavedValues
  }
}
