"""
CELER AI ENTERPRISE ARCHITECTURE 3.0
Monitoring and Observability - Basic Implementation

This module provides structured logging, metrics collection, and health checks
for the Python backend worker using existing infrastructure.
"""

import os
import json
import time
import psutil
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import redis
from functools import wraps

# =====================================================
# CONFIGURATION
# =====================================================

MONITORING_CONFIG = {
    'log_level': os.getenv('LOG_LEVEL', 'INFO'),
    'structured_logging': True,
    'metrics_enabled': True,
    'health_check_interval': 30,  # seconds
    'performance_tracking': True
}

# =====================================================
# STRUCTURED LOGGING
# =====================================================

class StructuredLogger:
    """Structured logger for consistent log formatting"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, MONITORING_CONFIG['log_level']))
        
        # Configure structured logging
        if MONITORING_CONFIG['structured_logging']:
            handler = logging.StreamHandler()
            formatter = StructuredFormatter()
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def info(self, message: str, **kwargs):
        """Log info message with structured data"""
        self._log('INFO', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with structured data"""
        self._log('ERROR', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with structured data"""
        self._log('WARNING', message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with structured data"""
        self._log('DEBUG', message, **kwargs)
    
    def _log(self, level: str, message: str, **kwargs):
        """Internal logging method"""
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level,
            'message': message,
            'service': 'celer-ai-worker',
            'version': '3.0',
            **kwargs
        }
        
        if level == 'ERROR':
            self.logger.error(json.dumps(log_data))
        elif level == 'WARNING':
            self.logger.warning(json.dumps(log_data))
        elif level == 'DEBUG':
            self.logger.debug(json.dumps(log_data))
        else:
            self.logger.info(json.dumps(log_data))

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logs"""
    
    def format(self, record):
        # If the message is already JSON, return as-is
        try:
            json.loads(record.getMessage())
            return record.getMessage()
        except (json.JSONDecodeError, ValueError):
            # Convert regular log to structured format
            log_data = {
                'timestamp': datetime.utcnow().isoformat(),
                'level': record.levelname,
                'message': record.getMessage(),
                'service': 'celer-ai-worker',
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno
            }
            return json.dumps(log_data)

# =====================================================
# METRICS COLLECTION
# =====================================================

@dataclass
class MetricData:
    """Metric data structure"""
    name: str
    value: float
    timestamp: datetime
    labels: Dict[str, str]
    metric_type: str  # counter, gauge, histogram

class MetricsCollector:
    """Simple metrics collector using Redis"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.logger = StructuredLogger(__name__)
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client for metrics storage"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
        except Exception as e:
            print(f"❌ Failed to initialize Redis for metrics: {e}")
            return None
    
    def increment_counter(self, name: str, labels: Dict[str, str] = None, value: float = 1.0):
        """Increment a counter metric"""
        if not MONITORING_CONFIG['metrics_enabled'] or not self.redis_client:
            return
        
        try:
            metric_key = self._build_metric_key(name, labels or {})
            self.redis_client.incrbyfloat(metric_key, value)
            self.redis_client.expire(metric_key, 86400)  # 24 hours TTL
            
        except Exception as e:
            self.logger.error("Failed to increment counter", 
                            metric_name=name, error=str(e))
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """Set a gauge metric value"""
        if not MONITORING_CONFIG['metrics_enabled'] or not self.redis_client:
            return
        
        try:
            metric_key = self._build_metric_key(name, labels or {})
            self.redis_client.set(metric_key, value, ex=86400)  # 24 hours TTL
            
        except Exception as e:
            self.logger.error("Failed to set gauge", 
                            metric_name=name, error=str(e))
    
    def record_histogram(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record a histogram value (simplified as list in Redis)"""
        if not MONITORING_CONFIG['metrics_enabled'] or not self.redis_client:
            return
        
        try:
            metric_key = f"histogram:{self._build_metric_key(name, labels or {})}"
            timestamp = int(time.time())
            
            # Store as sorted set with timestamp as score
            self.redis_client.zadd(metric_key, {str(value): timestamp})
            self.redis_client.expire(metric_key, 86400)  # 24 hours TTL
            
            # Keep only recent values (last 1000)
            self.redis_client.zremrangebyrank(metric_key, 0, -1001)
            
        except Exception as e:
            self.logger.error("Failed to record histogram", 
                            metric_name=name, error=str(e))
    
    def _build_metric_key(self, name: str, labels: Dict[str, str]) -> str:
        """Build Redis key for metric"""
        label_str = ",".join([f"{k}={v}" for k, v in sorted(labels.items())])
        return f"metric:{name}:{label_str}" if label_str else f"metric:{name}"
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics"""
        if not self.redis_client:
            return {}
        
        try:
            keys = self.redis_client.keys("metric:*")
            summary = {}
            
            for key in keys:
                metric_name = key.replace("metric:", "").split(":")[0]
                if metric_name not in summary:
                    summary[metric_name] = []
                
                value = self.redis_client.get(key)
                if value:
                    summary[metric_name].append(float(value))
            
            return summary
            
        except Exception as e:
            self.logger.error("Failed to get metrics summary", error=str(e))
            return {}

# =====================================================
# PERFORMANCE MONITORING
# =====================================================

class PerformanceMonitor:
    """Monitor system performance and resource usage"""
    
    def __init__(self):
        self.metrics = MetricsCollector()
        self.logger = StructuredLogger(__name__)
    
    def get_system_metrics(self) -> Dict[str, float]:
        """Get current system metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            metrics = {
                'cpu_usage_percent': cpu_percent,
                'memory_usage_percent': memory_percent,
                'memory_used_mb': memory_used_mb,
                'disk_usage_percent': disk_percent
            }
            
            # Record metrics
            for name, value in metrics.items():
                self.metrics.set_gauge(name, value)
            
            return metrics
            
        except Exception as e:
            self.logger.error("Failed to get system metrics", error=str(e))
            return {}
    
    def get_process_metrics(self) -> Dict[str, float]:
        """Get current process metrics"""
        try:
            process = psutil.Process()
            
            # Process CPU and memory
            cpu_percent = process.cpu_percent()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            # File descriptors
            num_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
            
            metrics = {
                'process_cpu_percent': cpu_percent,
                'process_memory_mb': memory_mb,
                'process_file_descriptors': num_fds
            }
            
            # Record metrics
            for name, value in metrics.items():
                self.metrics.set_gauge(name, value)
            
            return metrics
            
        except Exception as e:
            self.logger.error("Failed to get process metrics", error=str(e))
            return {}

# =====================================================
# HEALTH CHECKS
# =====================================================

class HealthChecker:
    """Health check implementation"""
    
    def __init__(self):
        self.logger = StructuredLogger(__name__)
        self.performance_monitor = PerformanceMonitor()
    
    async def check_health(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        health_status = {
            'healthy': True,
            'timestamp': datetime.utcnow().isoformat(),
            'service': 'celer-ai-worker',
            'version': '3.0',
            'checks': {}
        }
        
        # Check Redis connection
        redis_health = await self._check_redis()
        health_status['checks']['redis'] = redis_health
        
        # Check system resources
        system_health = await self._check_system_resources()
        health_status['checks']['system'] = system_health
        
        # Check Supabase connection
        supabase_health = await self._check_supabase()
        health_status['checks']['supabase'] = supabase_health
        
        # Overall health
        health_status['healthy'] = all(
            check.get('healthy', False) 
            for check in health_status['checks'].values()
        )
        
        return health_status
    
    async def _check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity"""
        try:
            redis_client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
            
            # Test connection
            redis_client.ping()
            
            return {
                'healthy': True,
                'response_time_ms': 0,  # Could measure actual response time
                'message': 'Redis connection successful'
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Redis connection failed'
            }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource availability"""
        try:
            metrics = self.performance_monitor.get_system_metrics()
            
            # Define thresholds
            cpu_threshold = 90.0
            memory_threshold = 90.0
            disk_threshold = 90.0
            
            healthy = (
                metrics.get('cpu_usage_percent', 0) < cpu_threshold and
                metrics.get('memory_usage_percent', 0) < memory_threshold and
                metrics.get('disk_usage_percent', 0) < disk_threshold
            )
            
            return {
                'healthy': healthy,
                'metrics': metrics,
                'thresholds': {
                    'cpu_percent': cpu_threshold,
                    'memory_percent': memory_threshold,
                    'disk_percent': disk_threshold
                },
                'message': 'System resources within limits' if healthy else 'System resources under pressure'
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Failed to check system resources'
            }
    
    async def _check_supabase(self) -> Dict[str, Any]:
        """Check Supabase connectivity"""
        try:
            # This would require importing and testing Supabase client
            # For now, return a placeholder
            return {
                'healthy': True,
                'message': 'Supabase connection check not implemented'
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Supabase connection failed'
            }

# =====================================================
# DECORATORS AND UTILITIES
# =====================================================

def monitor_performance(metric_name: str = None):
    """Decorator to monitor function performance"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            metrics = MetricsCollector()
            logger = StructuredLogger(func.__module__)
            
            name = metric_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record metrics
                metrics.increment_counter(f"{name}_calls_total", {"status": "success"})
                metrics.record_histogram(f"{name}_duration_seconds", duration)
                
                logger.info(f"Function completed successfully",
                          function=name, duration_seconds=duration)
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Record error metrics
                metrics.increment_counter(f"{name}_calls_total", {"status": "error"})
                metrics.record_histogram(f"{name}_duration_seconds", duration)
                
                logger.error(f"Function failed",
                           function=name, duration_seconds=duration, error=str(e))
                
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            metrics = MetricsCollector()
            logger = StructuredLogger(func.__module__)
            
            name = metric_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record metrics
                metrics.increment_counter(f"{name}_calls_total", {"status": "success"})
                metrics.record_histogram(f"{name}_duration_seconds", duration)
                
                logger.info(f"Function completed successfully",
                          function=name, duration_seconds=duration)
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Record error metrics
                metrics.increment_counter(f"{name}_calls_total", {"status": "error"})
                metrics.record_histogram(f"{name}_duration_seconds", duration)
                
                logger.error(f"Function failed",
                           function=name, duration_seconds=duration, error=str(e))
                
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

@contextmanager
def performance_context(operation_name: str):
    """Context manager for monitoring operation performance"""
    start_time = time.time()
    metrics = MetricsCollector()
    logger = StructuredLogger(__name__)
    
    try:
        yield
        duration = time.time() - start_time
        
        metrics.increment_counter(f"{operation_name}_operations_total", {"status": "success"})
        metrics.record_histogram(f"{operation_name}_duration_seconds", duration)
        
        logger.info(f"Operation completed successfully",
                  operation=operation_name, duration_seconds=duration)
        
    except Exception as e:
        duration = time.time() - start_time
        
        metrics.increment_counter(f"{operation_name}_operations_total", {"status": "error"})
        metrics.record_histogram(f"{operation_name}_duration_seconds", duration)
        
        logger.error(f"Operation failed",
                   operation=operation_name, duration_seconds=duration, error=str(e))
        
        raise

# =====================================================
# GLOBAL INSTANCES
# =====================================================

# Global instances for easy access
logger = StructuredLogger(__name__)
metrics = MetricsCollector()
performance_monitor = PerformanceMonitor()
health_checker = HealthChecker()

# Import asyncio for decorator
import asyncio
