'use server'

import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { createAdminClient } from '@/lib/supabase/admin'
import { SignupFormSchema, LoginFormSchema } from '@/lib/validations'
import { FormState } from '@/lib/types'
import { processReferralSignup } from '@/lib/actions/referrals'
import { createUserProfile } from '@/lib/auth/phone-verification'

export async function signup(state: FormState, formData: FormData): Promise<FormState> {
  // Validate form fields
  const validatedFields = SignupFormSchema.safeParse({
    name: formData.get('name'),
    email: formData.get('email'),
    password: formData.get('password'),
    clinic_name: formData.get('clinic_name'),
    phone: formData.get('phone'),
  })

  // Get referral code and captcha token from form
  const referralCode = formData.get('referral_code') as string
  const captchaToken = formData.get('captcha_token') as string

  // If any form fields are invalid, return early
  if (!validatedFields.success) {
    return {
      // Provide required 'success' and 'message' properties
      success: false,
      message: 'Invalid form fields.', // A generic message for validation failure
      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors
      fieldErrors: validatedFields.error.flatten().fieldErrors,
    }
  }

  const { name, email, password, clinic_name, phone } = validatedFields.data

  try {
    const supabase = await createClient()

    // Create user in Supabase Auth (unconfirmed)
    const signUpOptions: any = {
      email,
      password,
      options: {
        data: {
          name,
          clinic_name,
          phone
        }
      }
    }

    // Add CAPTCHA token if provided
    if (captchaToken) {
      signUpOptions.options.captchaToken = captchaToken
    }

    const { data: authData, error: authError } = await supabase.auth.signUp(signUpOptions)

    if (authError) {
      console.error('Supabase auth signup error:', authError)

      // Handle specific error cases
      if (authError.message.includes('already registered')) {
        return {
          success: false,
          message: 'An account with this email already exists.',
        }
      }

      return {
        success: false,
        message: 'Failed to create account. Please try again.',
      }
    }

    if (!authData.user) {
      return {
        success: false,
        message: 'Failed to create account. Please try again.',
      }
    }

    // Create user profile in our profiles table
    const profileResult = await createUserProfile(
      authData.user.id,
      email,
      name,
      clinic_name,
      phone,
      referralCode
    )

    if (!profileResult.success) {
      // If profile creation fails, we should clean up the auth user
      // But for now, we'll let the user try again
      return profileResult
    }

    // Process referral if referral code was provided
    if (referralCode) {
      await processReferralSignup(referralCode, authData.user.id)
    }

    // Return success with phone verification required
    return {
      success: true,
      message: 'PHONE_VERIFICATION_REQUIRED',
      data: { userId: authData.user.id, phone: phone }
    }

  } catch (error) {
    console.error('Signup error:', error)
    return {
      success: false,
      message: 'An unexpected error occurred.',
    }
  }
}



export async function login(state: FormState, formData: FormData): Promise<FormState> {
  // Validate form fields
  const validatedFields = LoginFormSchema.safeParse({
    email: formData.get('email'),
    password: formData.get('password'),
  })

  if (!validatedFields.success) {
    return {
      success: false,
      message: 'Invalid form fields.',
      fieldErrors: validatedFields.error.flatten().fieldErrors,
    }
  }

  const { email, password } = validatedFields.data

  try {
    const supabase = await createClient()

    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (authError || !authData.user) {
      console.error('Supabase auth login error:', authError)
      return {
        success: false,
        message: 'Invalid email or password.',
      }
    }

    // Check if user profile exists, is approved, and has correct role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('approved, role')
      .eq('id', authData.user.id)
      .single()

    if (profileError || !profile) {
      console.error('Profile fetch error:', profileError)
      return {
        success: false,
        message: 'Account not found. Please contact support.',
      }
    }

    // ENTERPRISE SECURITY: Prevent admin users from logging into doctor portal
    if (profile.role === 'admin' || profile.role === 'super_admin') {
      await supabase.auth.signOut()
      return {
        success: false,
        message: 'Admin users must use the admin login portal.',
      }
    }

    // Check if user is approved
    if (!profile.approved) {
      // Sign out the user since they're not approved
      await supabase.auth.signOut()
      return {
        success: false,
        message: 'Your account is pending approval. Please complete phone verification or contact support.',
      }
    }

    // --- START OF NEW LOGIC ---
    // Use the SERVICE_ROLE client to update the user's metadata
    // This securely embeds the role and approval status into their JWT.
    const supabaseAdmin = createAdminClient()

    await supabaseAdmin.auth.admin.updateUserById(
      authData.user.id,
      { app_metadata: { role: profile.role, approved: profile.approved } }
    )
    // --- END OF NEW LOGIC ---

    // User is authenticated and approved - Supabase handles the session
    // No need to create custom session, Supabase manages it automatically

  } catch (error) {
    console.error('Login error:', error)
    return {
      success: false,
      message: 'An unexpected error occurred.',
    }
  }

  // If execution reaches here, login was successful
  // Let middleware handle the redirect to avoid conflicts
  redirect('/dashboard')
}

export async function logout() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  redirect('/login')
}

export async function completeGoogleAuth(): Promise<void> {
  try {
    const supabase = await createClient()
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.user) {
      console.error('No valid session found:', sessionError)
      redirect('/auth/error?message=Authentication session not found')
    }

    // Check user profile and approval status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('approved, role')
      .eq('id', session.user.id)
      .single()

    if (profileError || !profile) {
      console.error('Error fetching user profile:', profileError)
      redirect('/auth/error?message=Failed to fetch user profile')
    }

    // Check admin role restriction
    if (profile.role === 'admin' || profile.role === 'super_admin') {
      await supabase.auth.signOut()
      redirect('/auth/error?message=Admin users must use the admin login portal')
    }

    // Google users are now auto-approved, so they should always be approved at this point
    // But we'll keep a safety check just in case
    if (!profile.approved) {
      console.error('Google user not approved - this should not happen with auto-approval')
      redirect('/auth/error?message=Account approval failed')
    }

    // --- START OF NEW LOGIC ---
    // Use the SERVICE_ROLE client to update the user's metadata
    // This securely embeds the role and approval status into their JWT.
    const supabaseAdmin = createAdminClient()

    await supabaseAdmin.auth.admin.updateUserById(
      session.user.id,
      { app_metadata: { role: profile.role, approved: profile.approved } }
    )
    // --- END OF NEW LOGIC ---

    // Success - redirect to dashboard
    redirect('/dashboard')

  } catch (error) {
    console.error('Error in completeGoogleAuth:', error)
    redirect('/auth/error?message=An unexpected error occurred')
  }
}

// Password changes are now handled client-side with Supabase Auth
// No server action needed - users can call supabase.auth.updateUser({ password: newPassword })

/**
 * DEPRECATED: Phone verification is now handled by approveUserInDatabase
 * in @/lib/actions/admin-auth-actions.ts for better security
 *
 * @deprecated Use approveUserInDatabase from @/lib/actions/admin-auth-actions instead
 */