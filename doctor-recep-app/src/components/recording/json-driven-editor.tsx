'use client'

import React, { useEffect, useCallback, useMemo, forwardRef } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Document from '@tiptap/extension-document'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'
import { useConsultationStore } from '@/lib/stores/consultation-store'
import { getSchemaForConsultationType } from '@/lib/schemas/consultation-note'
import { EnhancedEditableField } from '@/components/ai-assist/enhanced-editable-field'

// Simple debounce implementation
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }) as T
}

interface JsonDrivenEditorProps {
  consultationData: any
  consultationType: string
  onUpdate?: (updatedData: any) => void
  readOnly?: boolean
}

export const JsonDrivenEditor = forwardRef<HTMLDivElement, JsonDrivenEditorProps>(({
  consultationData,
  consultationType,
  onUpdate,
  readOnly = false
}, ref) => {
  const {
    setUnsavedChanges,
    saveEditedNote,
    isSaving,
    hasUnsavedChanges,
    lastSaved
  } = useConsultationStore()

  // Get the schema for this consultation type
  const schema = getSchemaForConsultationType(consultationType)

  // Debounced save function - using useMemo to avoid hook dependency issues
  const debouncedSave = useMemo(
    () => debounce(async (data: any) => {
      try {
        await saveEditedNote(data)
      } catch (error) {
        console.error('Auto-save failed:', error)
      }
    }, 2000),
    [saveEditedNote]
  )

  // Handle field updates
  const handleFieldUpdate = useCallback((fieldPath: string, value: any) => {
    if (readOnly) return

    // Deep clone the consultation data to avoid readonly issues
    const updatedData = JSON.parse(JSON.stringify(consultationData || {}))

    // Navigate to nested field and update
    const pathParts = fieldPath.split('.')
    let current = updatedData

    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {}
      }
      current = current[pathParts[i]]
    }

    current[pathParts[pathParts.length - 1]] = value

    // Mark as having unsaved changes
    setUnsavedChanges(true)

    // Trigger callback
    onUpdate?.(updatedData)

    // Auto-save
    debouncedSave(updatedData)
  }, [consultationData, onUpdate, setUnsavedChanges, debouncedSave, readOnly])

  // Render different field types based on schema and actual data
  const renderField = (key: string, value: any, fieldSchema: any, path: string = '') => {
    const fullPath = path ? `${path}.${key}` : key
    const fieldType = fieldSchema?.type || 'string'
    const description = fieldSchema?.description || ''
    const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

    // Skip schema_version and other technical fields
    if (key === 'schema_version') return null

    // --- RENDER FOR OBJECTS ---
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return (
        <div
          key={fullPath}
          className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-900/50"
          data-field-type="object"
          data-field-path={fullPath}
          data-field-label={label}
        >
          <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200 capitalize">
            {label}
          </h3>
          <div className="space-y-4 pl-4 border-l-2 border-indigo-200 dark:border-indigo-800">
            {Object.entries(value).map(([subKey, subValue]) =>
              // Recursive call to render nested properties
              renderField(subKey, subValue, fieldSchema?.properties?.[subKey], fullPath)
            )}
          </div>
        </div>
      )
    }

    // --- RENDER FOR ARRAYS (like Prescriptions) ---
    if (Array.isArray(value)) {
      return (
        <div
          key={fullPath}
          className="mb-4"
          data-field-type="array"
          data-field-path={fullPath}
          data-field-label={label}
        >
          <label className="block text-base font-medium text-gray-800 dark:text-gray-200 mb-2 capitalize">
            {label}
          </label>
          {value.map((item, index) => (
            <div
              key={`${fullPath}.${index}`}
              className="mb-2 p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              data-array-item-index={index}
            >
              {typeof item === 'object' ? (
                // If array contains objects (e.g., prescriptions), render them as editable key-value pairs
                <div className="space-y-2">
                  {Object.entries(item).map(([itemKey, itemValue]) => (
                    <div key={itemKey} className="flex flex-col">
                      <label className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 capitalize">
                        {itemKey.replace(/_/g, ' ')}
                      </label>
                      <EnhancedEditableField
                        value={String(itemValue || '')}
                        onChange={(newValue) => {
                          const newArray = [...value]
                          newArray[index] = { ...newArray[index], [itemKey]: newValue }
                          handleFieldUpdate(fullPath, newArray)
                        }}
                        readOnly={readOnly}
                        multiline={false}
                        fieldName={`${fullPath}[${index}].${itemKey}`}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                // Simple array items (e.g., chief complaints) - editable
                <EnhancedEditableField
                  value={String(item || '')}
                  onChange={(newValue) => {
                    const newArray = [...value]
                    newArray[index] = newValue
                    handleFieldUpdate(fullPath, newArray)
                  }}
                  readOnly={readOnly}
                  multiline={false}
                  fieldName={`${fullPath}[${index}]`}
                />
              )}
            </div>
          ))}
        </div>
      )
    }

    // --- RENDER FOR SIMPLE STRINGS ---
    // Only render simple key-value pairs.
    // The 'object' case above handles rendering of nested structures.
    if (path) { // Render only if it's a sub-property of an object.
      return (
        <div
          key={fullPath}
          className="mb-2"
          data-field-type="string"
          data-field-path={fullPath}
          data-field-label={label}
        >
          <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1 capitalize">
            {label}
          </label>
          <EnhancedEditableField
            value={String(value || '')}
            onChange={(newValue) => handleFieldUpdate(fullPath, newValue)}
            readOnly={readOnly}
            multiline={false}
            fieldName={fullPath}
          />
        </div>
      )
    }

    // For top-level strings that are not part of an object.
    return (
      <div
        key={fullPath}
        className="mb-4"
        data-field-type="string"
        data-field-path={fullPath}
        data-field-label={label}
      >
        <label className="block text-base font-medium text-gray-800 dark:text-gray-200 mb-2 capitalize">
          {label}
        </label>
        <EnhancedEditableField
          value={String(value || '')}
          onChange={(newValue) => handleFieldUpdate(fullPath, newValue)}
          readOnly={readOnly}
          multiline={true}
          fieldName={fullPath}
        />
      </div>
    )
  }

  // Get schema properties
  const schemaProperties = schema?.shape || {}

  return (
    <div ref={ref} className="json-driven-editor">
      {/* Save status indicator */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isSaving && (
            <div className="flex items-center text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-sm">Saving...</span>
            </div>
          )}
          {hasUnsavedChanges && !isSaving && (
            <span className="text-sm text-orange-600">Unsaved changes</span>
          )}
          {lastSaved && !hasUnsavedChanges && !isSaving && (
            <span className="text-sm text-green-600">
              Saved {lastSaved.toLocaleTimeString()}
            </span>
          )}
        </div>
        {!readOnly && (
          <span className="text-xs text-gray-500">
            Auto-saves every 2 seconds
          </span>
        )}
      </div>

      {/* Render all fields based on schema */}
      <div className="space-y-6">
        {Object.entries(schemaProperties).map(([key, fieldSchema]) =>
          renderField(key, consultationData?.[key], fieldSchema)
        )}
      </div>
    </div>
  )
})

JsonDrivenEditor.displayName = 'JsonDrivenEditor'

// Old EditableField component removed - now using EnhancedEditableField with AI assist
