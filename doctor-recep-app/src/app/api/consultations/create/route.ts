/**
 * CELER AI ENTERPRISE ARCHITECTURE 3.0
 * Event-Driven API Gateway - Consultation Creation Endpoint
 *
 * This endpoint implements the GOLD STANDARD direct-to-R2 architecture:
 * - Files are already uploaded directly to R2 by client
 * - Zero file handling on Vercel (maximum cost efficiency)
 * - Zero database operations (fire-and-forget)
 * - Reliable event publishing to Pub/Sub
 * - 202 Accepted responses for async processing
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/auth/supabase-helpers'
import { checkQuotaRedis, incrementQuotaRedis } from '@/lib/redis'
import { FileUploadSaga } from '@/lib/sagas/file-upload-saga'
import { publishConsultationCreate } from '@/lib/services/pubsub-service'
import {
  createErrorResponse,
  ConsultationEvent
} from '@/lib/validation/consultation-schemas'

// Custom type for File-based consultation creation (legacy route)
interface ConsultationCreateWithFilesRequest {
  primary_audio_file: File
  additional_audio_files: File[]
  image_files: File[]
  submitted_by: 'doctor' | 'receptionist'
  consultation_type: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'
  patient_name?: string
  doctor_notes?: string
  additional_notes?: string
  metadata?: any
  client_version?: string
  user_agent?: string
  ip_address?: string
}

// =====================================================
// MAIN ENDPOINT HANDLER
// =====================================================

export async function POST(request: NextRequest) {
  const correlationId = crypto.randomUUID()
  const consultationId = crypto.randomUUID()
  const startTime = Date.now()
  
  console.log(`🚀 [${correlationId}] Starting consultation creation request`)
  
  try {
    // Step 1: Verify user session
    const session = await verifySession()
    if (!session) {
      console.log(`❌ [${correlationId}] Unauthorized request`)
      return NextResponse.json(
        createErrorResponse('system_error', 'Unauthorized', undefined, correlationId),
        { status: 401 }
      )
    }
    
    console.log(`✅ [${correlationId}] User authenticated: ${session.userId}`)
    
    // Step 2: Parse and validate request
    const formData = await request.formData()
    const requestData = await parseFormData(formData)
    
    // Custom validation for File objects (legacy route)
    const validationResult = validateConsultationCreateWithFiles(requestData)
    if (!validationResult.success) {
      console.log(`❌ [${correlationId}] Validation failed:`, validationResult.error)
      return NextResponse.json(
        createErrorResponse(
          'validation_error',
          'Invalid request data',
          { field_errors: validationResult.error },
          correlationId
        ),
        { status: 400 }
      )
    }
    
    const validatedData = requestData
    console.log(`✅ [${correlationId}] Request validated successfully`)
    
    // Step 3: Check rate limits and quota
    const quotaCheck = await checkQuotaRedis(session.userId)
    if (!quotaCheck.allowed) {
      console.log(`❌ [${correlationId}] Rate limit exceeded for user ${session.userId}`)
      return NextResponse.json(
        createErrorResponse(
          'rate_limit_exceeded',
          'Monthly quota exceeded',
          {
            retry_after: 3600,
            quota_info: {
              current_usage: quotaCheck.currentUsage,
              quota_limit: 50, // TODO: Get from database
              reset_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            }
          },
          correlationId
        ),
        { status: 429 }
      )
    }
    
    console.log(`✅ [${correlationId}] Quota check passed: ${quotaCheck.currentUsage}/50`)
    
    // Step 4: Execute file upload saga (atomic operations)
    const fileUploadSaga = new FileUploadSaga(correlationId, consultationId, session.userId)
    
    const uploadResult = await fileUploadSaga.execute({
      primary_audio: validatedData.primary_audio_file,
      additional_audio: validatedData.additional_audio_files,
      images: validatedData.image_files
    })
    
    console.log(`✅ [${correlationId}] File upload saga completed successfully`)
    
    // Step 5: Increment quota (after successful file upload)
    await incrementQuotaRedis(session.userId)
    console.log(`✅ [${correlationId}] User quota incremented`)
    
    // Step 6: Create consultation event for Pub/Sub
    const consultationEvent: ConsultationEvent = {
      schema_version: '1.0',
      correlation_id: correlationId,
      consultation_id: consultationId,
      event_type: 'consultation.create',
      timestamp: new Date().toISOString(),
      
      // User context
      user_id: session.userId,
      session_id: request.headers.get('x-session-id') || undefined,
      
      // File URLs and checksums
      primary_audio_url: uploadResult.primary_audio.url,
      primary_audio_checksum: uploadResult.primary_audio.checksum,
      additional_audio_urls: uploadResult.additional_audio.map(f => f.url),
      additional_audio_checksums: uploadResult.additional_audio.map(f => f.checksum),
      image_urls: uploadResult.images.map(f => f.url),
      image_checksums: uploadResult.images.map(f => f.checksum),
      
      // Consultation metadata
      consultation_type: validatedData.consultation_type,
      submitted_by: validatedData.submitted_by,
      patient_name: validatedData.patient_name,
      doctor_notes: validatedData.doctor_notes,
      additional_notes: validatedData.additional_notes,
      
      // File information
      total_file_size_bytes: 
        uploadResult.primary_audio.size +
        uploadResult.additional_audio.reduce((sum, f) => sum + f.size, 0) +
        uploadResult.images.reduce((sum, f) => sum + f.size, 0),
      
      // Processing metadata
      retry_count: 0,
      priority: 'normal',
      
      // Additional metadata
      metadata: {
        ...validatedData.metadata,
        saga_id: fileUploadSaga.getSagaId(),
        api_version: '3.0',
        request_source: 'web_app'
      },
      
      // Client information
      client_version: validatedData.client_version,
      user_agent: request.headers.get('user-agent') || undefined,
      ip_address: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  undefined
    }
    
    // Step 7: Publish event to Pub/Sub using centralized service
    const publishResult = await publishConsultationCreate(consultationEvent, correlationId)
    
    if (!publishResult.success) {
      console.error(`❌ [${correlationId}] Failed to publish event:`, publishResult.error)
      
      // Compensate by cleaning up uploaded files
      await fileUploadSaga.compensate()
      
      return NextResponse.json(
        createErrorResponse(
          'system_error',
          'Failed to queue consultation for processing',
          { support_reference: correlationId },
          correlationId
        ),
        { status: 500 }
      )
    }
    
    console.log(`✅ [${correlationId}] Event published successfully: ${publishResult.messageId}`)
    
    // Step 8: Return immediate success response (202 Accepted)
    const processingTime = Date.now() - startTime
    const response = {
      consultation_id: consultationId,
      correlation_id: correlationId,
      status: 'accepted' as const,
      estimated_processing_time: '2-5 minutes',
      created_at: new Date().toISOString(),
      
      uploaded_files: {
        primary_audio: {
          url: uploadResult.primary_audio.url,
          checksum: uploadResult.primary_audio.checksum,
          size: uploadResult.primary_audio.size
        },
        additional_audio: uploadResult.additional_audio.map(f => ({
          url: f.url,
          checksum: f.checksum,
          size: f.size
        })),
        images: uploadResult.images.map(f => ({
          url: f.url,
          checksum: f.checksum,
          size: f.size
        }))
      },
      
      tracking: {
        job_id: publishResult.messageId || correlationId,
        status_url: `${request.nextUrl.origin}/api/job-status/${consultationId}`,
        webhook_url: undefined // TODO: Implement webhook support
      }
    }
    
    console.log(`🎉 [${correlationId}] Consultation creation completed in ${processingTime}ms`)
    
    return NextResponse.json(response, { 
      status: 202,
      headers: {
        'X-Correlation-ID': correlationId,
        'X-Processing-Time': processingTime.toString()
      }
    })
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    console.error(`💥 [${correlationId}] Consultation creation failed after ${processingTime}ms:`, error)
    
    return NextResponse.json(
      createErrorResponse(
        'system_error',
        'An unexpected error occurred',
        { support_reference: correlationId },
        correlationId
      ),
      { 
        status: 500,
        headers: {
          'X-Correlation-ID': correlationId,
          'X-Processing-Time': processingTime.toString()
        }
      }
    )
  }
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Parse FormData into structured request data
 */
async function parseFormData(formData: FormData): Promise<ConsultationCreateWithFilesRequest> {
  // Extract files
  const primaryAudioFile = formData.get('primary_audio_file') as File
  const additionalAudioFiles: File[] = []
  const imageFiles: File[] = []
  
  // Parse additional audio files
  let audioIndex = 0
  while (formData.has(`additional_audio_files[${audioIndex}]`)) {
    const file = formData.get(`additional_audio_files[${audioIndex}]`) as File
    if (file && file.size > 0) {
      additionalAudioFiles.push(file)
    }
    audioIndex++
  }
  
  // Parse image files
  let imageIndex = 0
  while (formData.has(`image_files[${imageIndex}]`)) {
    const file = formData.get(`image_files[${imageIndex}]`) as File
    if (file && file.size > 0) {
      imageFiles.push(file)
    }
    imageIndex++
  }
  
  // Parse metadata
  let metadata = {}
  try {
    const metadataString = formData.get('metadata') as string
    if (metadataString) {
      metadata = JSON.parse(metadataString)
    }
  } catch (error) {
    console.warn('Failed to parse metadata:', error)
  }
  
  return {
    primary_audio_file: primaryAudioFile,
    additional_audio_files: additionalAudioFiles,
    image_files: imageFiles,
    submitted_by: (formData.get('submitted_by') as 'doctor' | 'receptionist') || 'doctor',
    consultation_type: (formData.get('consultation_type') as any) || 'outpatient',
    patient_name: formData.get('patient_name') as string || undefined,
    doctor_notes: formData.get('doctor_notes') as string || undefined,
    additional_notes: formData.get('additional_notes') as string || undefined,
    metadata,
    client_version: formData.get('client_version') as string || undefined,
    user_agent: formData.get('user_agent') as string || undefined,
    ip_address: formData.get('ip_address') as string || undefined
  }
}

/**
 * Custom validation for File objects (legacy route)
 */
function validateConsultationCreateWithFiles(data: any): { success: boolean; error?: any } {
  try {
    // Validate required fields
    if (!data.primary_audio_file || !(data.primary_audio_file instanceof File)) {
      return { success: false, error: { primary_audio_file: 'Primary audio file is required' } }
    }

    if (!data.submitted_by || !['doctor', 'receptionist'].includes(data.submitted_by)) {
      return { success: false, error: { submitted_by: 'Valid submitted_by value is required' } }
    }

    // Validate file types and sizes
    const audioTypes = ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/mpeg', 'audio/mp4', 'audio/aac']
    if (!audioTypes.includes(data.primary_audio_file.type)) {
      return { success: false, error: { primary_audio_file: 'Invalid audio file type' } }
    }

    if (data.primary_audio_file.size > 100 * 1024 * 1024) { // 100MB
      return { success: false, error: { primary_audio_file: 'Audio file must be less than 100MB' } }
    }

    // Validate additional audio files
    if (data.additional_audio_files && Array.isArray(data.additional_audio_files)) {
      for (const file of data.additional_audio_files) {
        if (!(file instanceof File)) {
          return { success: false, error: { additional_audio_files: 'All additional audio files must be valid File objects' } }
        }
        if (!audioTypes.includes(file.type)) {
          return { success: false, error: { additional_audio_files: 'Invalid additional audio file type' } }
        }
        if (file.size > 100 * 1024 * 1024) {
          return { success: false, error: { additional_audio_files: 'Additional audio files must be less than 100MB each' } }
        }
      }
    }

    // Validate image files
    if (data.image_files && Array.isArray(data.image_files)) {
      const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic']
      for (const file of data.image_files) {
        if (!(file instanceof File)) {
          return { success: false, error: { image_files: 'All image files must be valid File objects' } }
        }
        if (!imageTypes.includes(file.type)) {
          return { success: false, error: { image_files: 'Invalid image file type' } }
        }
        if (file.size > 50 * 1024 * 1024) { // 50MB
          return { success: false, error: { image_files: 'Image files must be less than 50MB each' } }
        }
      }
    }

    return { success: true }
  } catch (error) {
    return { success: false, error: { general: 'Validation error occurred' } }
  }
}

// =====================================================
// CORS AND OPTIONS HANDLER
// =====================================================

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Session-ID',
      'Access-Control-Max-Age': '86400'
    }
  })
}
