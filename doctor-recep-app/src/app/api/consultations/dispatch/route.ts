/**
 * LEAN ENTERPRISE ARCHITECTURE 3.0
 * Consultation Job Dispatch Endpoint
 * 
 * This endpoint receives the "work order" after files are uploaded to R2
 * and dispatches the job to Pub/Sub for processing.
 * Zero additional infrastructure cost - runs on existing Vercel + Pub/Sub.
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'
import { pubsubService, checkConsultationTopicHealth } from '@/lib/services/pubsub-service'

// =====================================================
// CONFIGURATION
// =====================================================

// Configuration constants
const TOPIC_NAME = process.env.PUBSUB_TOPIC_NAME || 'consultation-processing'

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

const FileUrlSchema = z.object({
  url: z.string().url('Invalid file URL'),
  fileName: z.string().min(1, 'File name required'),
  fileType: z.string().min(1, 'File type required'),
  fileSize: z.number().positive('File size must be positive')
})

const DispatchJobSchema = z.object({
  consultationId: z.string().uuid('Invalid consultation ID'),
  consultationType: z.enum(['outpatient', 'discharge', 'surgery'], {
    errorMap: () => ({ message: 'Invalid consultation type' })
  }),
  
  // Patient and consultation details
  patientName: z.string().optional(),
  doctorNotes: z.string().optional(),
  additionalNotes: z.string().optional(),
  submittedBy: z.string().min(1, 'Submitted by is required'),
  
  // File URLs (already uploaded to R2)
  primaryAudioUrl: z.string().url('Primary audio URL required'),
  additionalAudioUrls: z.array(z.string().url()).default([]),
  imageUrls: z.array(z.string().url()).default([]),
  
  // File metadata for validation
  files: z.array(FileUrlSchema).min(1, 'At least one file required'),
  
  // Total file size for monitoring
  totalFileSizeBytes: z.number().positive().optional()
})

// =====================================================
// SECURITY & VALIDATION
// =====================================================

async function validateUser(request: NextRequest, supabase: any) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return { error: 'Missing or invalid authorization header', user: null }
    }

    const token = authHeader.substring(7)
    const { data: { user }, error } = await supabase.auth.getUser(token)

    if (error || !user) {
      return { error: 'Invalid or expired token', user: null }
    }

    // Check if user is approved
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('approved')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.approved) {
      return { error: 'User not approved for consultations', user: null }
    }

    return { user, error: null }
  } catch (error) {
    return { error: 'Authentication failed', user: null }
  }
}

function validateFileUrls(files: any[], primaryAudioUrl: string, additionalAudioUrls: string[], imageUrls: string[]): boolean {
  // Ensure all provided URLs are in the files array
  const allUrls = [primaryAudioUrl, ...additionalAudioUrls, ...imageUrls]
  const fileUrls = files.map(f => f.url)
  
  return allUrls.every(url => fileUrls.includes(url))
}

// =====================================================
// LEAN MONITORING (Zero Cost)
// =====================================================

async function logJobDispatch(consultationId: string, userId: string, metadata: any, supabase: any) {
  try {
    // Log to Supabase for free monitoring
    await supabase
      .from('consultation_audit')
      .insert({
        consultation_id: consultationId,
        event_type: 'job_dispatched',
        user_id: userId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          source: 'lean_dispatch_api'
        }
      })
  } catch (error) {
    console.error('Failed to log job dispatch:', error)
    // Don't fail the request if logging fails
  }
}

// =====================================================
// MAIN HANDLER
// =====================================================

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  const correlationId = uuidv4()

  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )

  try {
    console.log('🚀 Dispatching consultation job', { correlationId })

    // 1. Validate user authentication
    const { user, error: authError } = await validateUser(request, supabase)
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError, { correlationId })
      return NextResponse.json(
        { error: authError || 'Authentication required' },
        { status: 401 }
      )
    }

    // 2. Parse and validate request body
    const body = await request.json()
    const validationResult = DispatchJobSchema.safeParse(body)
    
    if (!validationResult.success) {
      console.error('❌ Validation failed:', validationResult.error.errors, { correlationId })
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const jobData = validationResult.data

    // 3. Additional validation - ensure file URLs match
    if (!validateFileUrls(jobData.files, jobData.primaryAudioUrl, jobData.additionalAudioUrls, jobData.imageUrls)) {
      console.error('❌ File URL validation failed', { correlationId })
      return NextResponse.json(
        { error: 'File URLs do not match uploaded files' },
        { status: 400 }
      )
    }

    // 4. Create the lean job message for Pub/Sub
    const jobMessage = {
      // Core identifiers
      consultationId: jobData.consultationId,
      correlationId,
      userId: user.id,
      
      // Job metadata
      jobType: 'consultation_processing',
      priority: 'normal',
      createdAt: new Date().toISOString(),
      
      // Consultation data
      consultationType: jobData.consultationType,
      patientName: jobData.patientName,
      doctorNotes: jobData.doctorNotes,
      additionalNotes: jobData.additionalNotes,
      submittedBy: jobData.submittedBy,
      
      // File URLs (already in R2)
      primaryAudioUrl: jobData.primaryAudioUrl,
      additionalAudioUrls: jobData.additionalAudioUrls,
      imageUrls: jobData.imageUrls,
      
      // File metadata
      files: jobData.files,
      totalFileSizeBytes: jobData.totalFileSizeBytes || 0,
      
      // Processing hints for the worker
      processingHints: {
        estimatedProcessingTimeSeconds: Math.max(30, jobData.files.length * 10),
        batchable: true,
        retryable: true
      }
    }

    // 5. Dispatch to Pub/Sub using centralized service
    const publishResult = await pubsubService.publishMessage(
      TOPIC_NAME,
      jobMessage,
      {
        correlationId,
        orderingKey: jobData.consultationId,
        attributes: {
          consultationId: jobData.consultationId,
          userId: user.id,
          consultationType: jobData.consultationType,
          priority: 'normal',
          source: 'lean_dispatch_api'
        }
      }
    )

    if (!publishResult.success) {
      throw new Error(`Failed to publish message: ${publishResult.error}`)
    }

    const messageId = publishResult.messageId!

    // 6. Log for lean monitoring
    await logJobDispatch(jobData.consultationId, user.id, {
      correlationId,
      messageId,
      fileCount: jobData.files.length,
      totalFileSize: jobData.totalFileSizeBytes,
      consultationType: jobData.consultationType,
      processingTime: Date.now() - startTime
    }, supabase)

    // 7. Return immediate 202 Accepted response
    console.log('✅ Job dispatched successfully', {
      consultationId: jobData.consultationId,
      correlationId,
      messageId,
      duration: Date.now() - startTime
    })

    return NextResponse.json({
      success: true,
      consultationId: jobData.consultationId,
      correlationId,
      messageId,
      status: 'dispatched',
      estimatedProcessingTime: jobMessage.processingHints.estimatedProcessingTimeSeconds,
      message: 'Consultation job dispatched for processing'
    }, { status: 202 }) // 202 Accepted

  } catch (error) {
    console.error('❌ Failed to dispatch job:', error, { correlationId })
    
    // Log the error for monitoring
    try {
      await supabase
        .from('consultation_audit')
        .insert({
          consultation_id: null,
          event_type: 'dispatch_error',
          user_id: null,
          metadata: {
            error: String(error),
            correlationId,
            timestamp: new Date().toISOString(),
            source: 'lean_dispatch_api'
          }
        })
    } catch (logError) {
      console.error('Failed to log error:', logError)
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to dispatch consultation job',
        correlationId,
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      },
      { status: 500 }
    )
  }
}

// =====================================================
// HEALTH CHECK
// =====================================================

export async function GET() {
  try {
    // Use centralized service for health check
    const healthStatus = await checkConsultationTopicHealth()

    if (!healthStatus.healthy) {
      return NextResponse.json(
        {
          healthy: false,
          error: healthStatus.error || `Pub/Sub topic '${TOPIC_NAME}' is not healthy`,
          connectionStatus: healthStatus.connectionStatus,
          retryCount: healthStatus.retryCount
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      healthy: true,
      service: 'Lean Consultation Dispatch',
      pubsubTopic: TOPIC_NAME,
      connectionStatus: healthStatus.connectionStatus,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return NextResponse.json(
      { 
        healthy: false, 
        error: 'Health check failed',
        details: String(error)
      },
      { status: 500 }
    )
  }
}
