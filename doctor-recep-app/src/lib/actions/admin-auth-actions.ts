'use server'

import { createAdminClient } from '@/lib/supabase/admin'
import { revalidatePath } from 'next/cache'

/**
 * Approve a user in the database after successful OTP verification
 * This is a secure, server-only function that uses the service_role key
 * 
 * @param userId - The user ID to approve
 * @returns Promise with success status and optional error message
 */
export async function approveUserInDatabase(userId: string): Promise<{ success: boolean; error?: string }> {
  if (!userId) {
    return { success: false, error: 'User ID is required.' }
  }

  try {
    // Use the admin client to bypass RLS and perform a privileged update
    const supabaseAdmin = createAdminClient()

    // Our logic doesn't confirm the Supabase auth user
    // It updates the `approved` flag in OUR `profiles` table
    const { error } = await supabaseAdmin
      .from('profiles')
      .update({
        approved: true,
        approved_at: new Date().toISOString(),
        approved_by: null // Auto-approved via phone verification
      })
      .eq('id', userId)

    if (error) {
      console.error('Failed to approve user in database:', error)
      return { success: false, error: 'Database update failed.' }
    }

    // Update app_metadata to reflect approval status change
    await supabaseAdmin.auth.admin.updateUserById(userId, {
      app_metadata: { approved: true }
    })

    // Revalidate to reflect login status changes
    revalidatePath('/')
    return { success: true }
  } catch (error) {
    console.error('Unexpected error in approveUserInDatabase:', error)
    return { success: false, error: 'An unexpected error occurred.' }
  }
}
