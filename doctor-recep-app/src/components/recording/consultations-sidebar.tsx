'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { Search, Plus, X, Clock, CheckCircle, FileText, Calendar, ChevronDown, Loader2 } from 'lucide-react'
import { Consultation } from '@/lib/types'
import { getConsultations } from '@/lib/actions/consultations'
// import { formatDate, formatRelativeTime } from '@/lib/utils'
import { motion } from 'framer-motion'

interface ConsultationsSidebarProps {
  consultations: Consultation[]
  hasMore: boolean
  onConsultationSelect: (consultation: Consultation) => void
  selectedConsultation: Consultation | null
  isDarkMode: boolean
  doctorId: string
  isMobile?: boolean
  onClose?: () => void
  isLoading?: boolean
}

// Format date to readable string (SSR-safe)
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)

  // Use a consistent format that works on both server and client
  const year = date.getFullYear()
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
  const month = monthNames[date.getMonth()]
  const day = date.getDate().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const ampm = date.getHours() >= 12 ? 'PM' : 'AM'
  const displayHours = date.getHours() % 12 || 12

  return `${month} ${day}, ${year} at ${displayHours.toString().padStart(2, '0')}:${minutes} ${ampm}`
}

// Format relative time (e.g., "2 hours ago")
const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  } else {
    return formatDate(dateString)
  }
}

export function ConsultationsSidebar({
  consultations: initialConsultations,
  hasMore: initialHasMore,
  onConsultationSelect,
  selectedConsultation,
  isDarkMode,
  doctorId,
  isMobile = false,
  onClose,
  isLoading: externalIsLoading = false
}: ConsultationsSidebarProps) {
  // State for infinite scroll
  const [consultations, setConsultations] = useState<Consultation[]>(initialConsultations)
  const [hasMore, setHasMore] = useState(initialHasMore)
  const [isLoading, setIsLoading] = useState(false)
  const [isBackgroundLoading, setIsBackgroundLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)

  // Existing state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [isClient, setIsClient] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending_generation' | 'generated' | 'approved'>('all')
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'yesterday' | 'custom'>('all')
  const [customDate, setCustomDate] = useState<string>('')
  const [templateFilter, setTemplateFilter] = useState<'all' | 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'>('all')

  // Refs for infinite scroll
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadMoreRef = useRef<HTMLDivElement | null>(null)
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const scrollContainerRef = useRef<HTMLDivElement | null>(null)
  const [showStatusDropdown, setShowStatusDropdown] = useState(false)
  const [showDateDropdown, setShowDateDropdown] = useState(false)
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Update consultations when props change
  useEffect(() => {
    setConsultations(initialConsultations)
    setHasMore(initialHasMore)
    setCurrentPage(1)
  }, [initialConsultations, initialHasMore])

  // Debounced search effect
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 500) // 500ms debounce

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [searchQuery])

  // Reset and reload when debounced search changes (seamless, no flash)
  useEffect(() => {
    // Load fresh data with search (keep existing data visible during load)
    const loadWithSearch = async () => {
      setIsLoading(true)
      try {
        const result = await getConsultations({
          page: 1,
          pageSize: 15,
          status: statusFilter === 'all' ? undefined : statusFilter,
          searchTerm: debouncedSearchQuery || undefined
        })

        if (result.success) {
          // Only update state after successful fetch (prevents flash)
          setConsultations(result.data.consultations)
          setHasMore(result.data.hasMore)
          setCurrentPage(1)
        }
      } catch (error) {
        console.error('Failed to search consultations:', error)
        setHasMore(false)
      } finally {
        setIsLoading(false)
      }
    }

    loadWithSearch()
  }, [debouncedSearchQuery, statusFilter])

  // Background preloading function (seamless, no loading UI)
  const backgroundPreload = useCallback(async () => {
    if (isBackgroundLoading || !hasMore || isLoading) {
      return
    }

    console.log('Background preloading - page:', currentPage + 1)
    setIsBackgroundLoading(true)
    try {
      const result = await getConsultations({
        page: currentPage + 1,
        pageSize: 15,
        status: statusFilter === 'all' ? undefined : statusFilter,
        searchTerm: debouncedSearchQuery || undefined
      })

      if (result.success && result.data.consultations.length > 0) {
        setConsultations(prev => {
          // Prevent duplicates by filtering out consultations that already exist
          const existingIds = new Set(prev.map(c => c.id))
          const newConsultations = result.data.consultations.filter(c => !existingIds.has(c.id))
          return [...prev, ...newConsultations]
        })
        setHasMore(result.data.hasMore)
        setCurrentPage(prev => prev + 1)
        console.log('Background preloaded', result.data.consultations.length, 'consultations')

        // Track background preload performance
        if (typeof window !== 'undefined') {
          import('@/lib/analytics').then(({ trackBackgroundPreload }) => {
            trackBackgroundPreload({
              scrollPercentage: 70, // We trigger at 70%
              itemsPreloaded: result.data.consultations.length
            })
          }).catch(() => {
            // Silently fail analytics tracking
          })
        }
      } else {
        setHasMore(false)
        console.log('No more consultations to preload')
      }
    } catch (error) {
      console.error('Failed to preload consultations:', error)
      setHasMore(false)
    } finally {
      setIsBackgroundLoading(false)
    }
  }, [currentPage, hasMore, isLoading, isBackgroundLoading, statusFilter, debouncedSearchQuery])

  // Load more consultations
  const loadMoreConsultations = useCallback(async () => {
    // CRITICAL: Don't make any requests if we don't have more data
    if (isLoading || !hasMore) {
      console.log('Skipping load more - isLoading:', isLoading, 'hasMore:', hasMore)
      return
    }

    console.log('Loading more consultations - page:', currentPage + 1)
    setIsLoading(true)
    try {
      const result = await getConsultations({
        page: currentPage + 1,
        pageSize: 15,
        status: statusFilter === 'all' ? undefined : statusFilter,
        searchTerm: debouncedSearchQuery || undefined
      })

      if (result.success && result.data.consultations.length > 0) {
        setConsultations(prev => {
          // Prevent duplicates by filtering out consultations that already exist
          const existingIds = new Set(prev.map(c => c.id))
          const newConsultations = result.data.consultations.filter(c => !existingIds.has(c.id))
          return [...prev, ...newConsultations]
        })
        setHasMore(result.data.hasMore)
        setCurrentPage(prev => prev + 1)
        console.log('Loaded', result.data.consultations.length, 'more consultations')
      } else {
        // No more data available
        setHasMore(false)
        console.log('No more consultations to load')
      }
    } catch (error) {
      console.error('Failed to load more consultations:', error)
      setHasMore(false) // Stop trying on error
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, hasMore, isLoading, statusFilter, debouncedSearchQuery])



  // Set up intersection observer for infinite scroll
  useEffect(() => {
    if (!loadMoreRef.current || !hasMore || isLoading) {
      // Clean up observer if conditions aren't met
      if (observerRef.current) {
        observerRef.current.disconnect()
        observerRef.current = null
      }
      return
    }

    // Clean up existing observer before creating new one
    if (observerRef.current) {
      observerRef.current.disconnect()
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        // Use background preloading instead of visible loading for seamless UX
        if (entries[0].isIntersecting && !isBackgroundLoading && !isLoading && hasMore && consultations.length > 0) {
          console.log('Intersection observer triggered - background preloading')
          backgroundPreload()
        } else {
          console.log('Intersection observer triggered but conditions not met:', {
            isIntersecting: entries[0].isIntersecting,
            isLoading,
            isBackgroundLoading,
            hasMore,
            consultationsCount: consultations.length
          })
        }
      },
      { threshold: 0.1 }
    )

    observerRef.current.observe(loadMoreRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
        observerRef.current = null
      }
    }
  }, [loadMoreConsultations, hasMore, isLoading, consultations.length, backgroundPreload, isBackgroundLoading])

  // Background preloading on scroll (70% threshold)
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (!scrollContainer || !hasMore || isBackgroundLoading) {
      return
    }

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight

      // Trigger background preload when user scrolls past 70%
      if (scrollPercentage > 0.7 && hasMore && !isBackgroundLoading && !isLoading) {
        console.log('70% scroll reached - triggering background preload')
        backgroundPreload()
      }
    }

    scrollContainer.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll)
    }
  }, [backgroundPreload, hasMore, isBackgroundLoading, isLoading])

  // Reset pagination when filters change (excluding search which is handled separately)
  useEffect(() => {
    setCurrentPage(1)
    // Note: Search is handled separately with debouncing above
  }, [dateFilter, templateFilter])

  // Filter consultations based on search and filters
  const getDateFilteredConsultations = (consultations: Consultation[]) => {
    if (dateFilter === 'all') return consultations

    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    return consultations.filter(consultation => {
      const consultationDate = new Date(consultation.created_at)

      switch (dateFilter) {
        case 'today':
          return consultationDate.toDateString() === today.toDateString()
        case 'yesterday':
          return consultationDate.toDateString() === yesterday.toDateString()
        case 'custom':
          if (!customDate) return true
          const selectedDate = new Date(customDate)
          return consultationDate.toDateString() === selectedDate.toDateString()
        default:
          return true
      }
    })
  }

  const filteredConsultations = consultations.filter(consultation => {
    // Search filter
    const searchLower = searchQuery.toLowerCase()
    const matchesSearch = !searchQuery || (
      consultation.patient_number?.toString().includes(searchLower) ||
      consultation.patient_name?.toLowerCase().includes(searchLower)
    )

    // Status filter
    const matchesStatus = statusFilter === 'all' || consultation.status === statusFilter

    // Template filter
    const matchesTemplate = templateFilter === 'all' || consultation.consultation_type === templateFilter

    return matchesSearch && matchesStatus && matchesTemplate
  })

  const dateAndFilteredConsultations = getDateFilteredConsultations(filteredConsultations)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending_generation':
        return <Clock className="w-4 h-4 text-orange-500" />
      case 'generated':
        return <FileText className="w-4 h-4 text-blue-500" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (status) {
      case 'pending_generation':
        return `${baseClasses} bg-orange-200 text-orange-900 dark:bg-orange-900/30 dark:text-orange-400`
      case 'generated':
        return `${baseClasses} bg-blue-200 text-blue-900 dark:bg-blue-900/30 dark:text-blue-400`
      case 'approved':
        return `${baseClasses} bg-green-200 text-green-900 dark:bg-green-900/30 dark:text-green-400`
      default:
        return `${baseClasses} bg-gray-200 text-gray-900 dark:bg-black dark:text-gray-400`
    }
  }

  const getTypeBadge = (type: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (type) {
      case 'outpatient':
        return `${baseClasses} bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400`
      case 'discharge':
        return `${baseClasses} bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400`
      case 'surgery':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400`
      case 'radiology':
        return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`
      case 'dermatology':
        return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400`
      case 'cardiology_echo':
        return `${baseClasses} bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400`
      case 'ivf_cycle':
        return `${baseClasses} bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400`
      case 'pathology':
        return `${baseClasses} bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-black dark:text-gray-400`
    }
  }

  return (
    <div className={`h-full flex flex-col transition-colors duration-300 ${
      isDarkMode
        ? 'bg-transparent'
        : 'bg-transparent'
    }`}>
      {/* Header */}
      <div className="p-6 transition-colors duration-300 border-r-0">
        <div className="flex items-center justify-between mb-4">
          <h2 className={`text-lg font-semibold ${
            isDarkMode ? 'text-gray-100' : 'text-slate-800'
          }`}>
            Consultations
          </h2>
          <div className="flex items-center space-x-2">
            {/* Round Add Button */}
            <button
              onClick={() => onConsultationSelect(null as unknown as Consultation)}
              className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${
                isDarkMode
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg'
                  : 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'
              }`}
              title="Add New Consultation"
            >
              <Plus className="w-4 h-4" />
            </button>

            {isMobile && onClose && (
              <button
                onClick={onClose}
                className={`p-1 rounded-xl transition-all duration-300 ${
                  isDarkMode
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105'
                }`}
              >
                <X className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          {searchQuery !== debouncedSearchQuery ? (
            <Loader2 className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`} />
          ) : (
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`} />
          )}
          <input
            type="text"
            placeholder="Search by patient name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full pl-10 pr-4 py-3 rounded-xl border transition-all duration-300 ${
              isDarkMode
                ? 'bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500'
                : 'bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500 focus:bg-white/90'
            } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex space-x-2 mb-4">
          {/* Status Filter */}
          <div className="relative">
            <button
              onClick={() => {
                setShowStatusDropdown(!showStatusDropdown)
                setShowDateDropdown(false)
                setShowTemplateDropdown(false)
              }}
              className={`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-300 hover:bg-gray-800'
                  : 'bg-white/80 border-white/30 text-slate-700 hover:bg-indigo-50'
              }`}
            >
              <span>{statusFilter === 'all' ? 'All' : statusFilter === 'pending_generation' ? 'Pending Generation' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}</span>
              <ChevronDown className="w-3 h-3" />
            </button>
            {showStatusDropdown && (
              <div className={`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${
                isDarkMode
                  ? 'bg-black border-gray-700'
                  : 'bg-white/90 backdrop-blur-xl border-white/30'
              }`}>
                {['all', 'pending_generation', 'generated', 'approved'].map((status) => (
                  <button
                    key={status}
                    onClick={() => {
                      setStatusFilter(status as 'all' | 'pending_generation' | 'generated' | 'approved')
                      setShowStatusDropdown(false)
                    }}
                    className={`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${
                      statusFilter === status
                        ? isDarkMode
                          ? 'bg-teal-900/30 text-teal-400'
                          : 'bg-teal-50 text-teal-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-800'
                          : 'text-amber-700 hover:bg-orange-50'
                    }`}
                  >
                    {status === 'all' ? 'All' : status === 'pending_generation' ? 'Pending Generation' : status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Date Filter */}
          <div className="relative">
            <button
              onClick={() => {
                setShowDateDropdown(!showDateDropdown)
                setShowStatusDropdown(false)
                setShowTemplateDropdown(false)
              }}
              className={`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-300 hover:bg-gray-800'
                  : 'bg-white/80 border-white/30 text-slate-700 hover:bg-indigo-50'
              }`}
            >
              <Calendar className="w-3 h-3" />
              <span>{dateFilter === 'all' ? 'All' : dateFilter.charAt(0).toUpperCase() + dateFilter.slice(1)}</span>
              <ChevronDown className="w-3 h-3" />
            </button>
            {showDateDropdown && (
              <div className={`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${
                isDarkMode
                  ? 'bg-black border-gray-700'
                  : 'bg-white/90 backdrop-blur-xl border-white/30'
              }`}>
                {['all', 'today', 'yesterday', 'custom'].map((date) => (
                  <button
                    key={date}
                    onClick={() => {
                      setDateFilter(date as 'all' | 'today' | 'yesterday' | 'custom')
                      setShowDateDropdown(false)
                      if (date !== 'custom') {
                        setCustomDate('')
                      }
                    }}
                    className={`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${
                      dateFilter === date
                        ? isDarkMode
                          ? 'bg-teal-900/30 text-teal-400'
                          : 'bg-teal-50 text-teal-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-800'
                          : 'text-amber-700 hover:bg-orange-50'
                    }`}
                  >
                    {date === 'all' ? 'All' : date.charAt(0).toUpperCase() + date.slice(1)}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Template Filter */}
          <div className="relative">
            <button
              onClick={() => {
                setShowTemplateDropdown(!showTemplateDropdown)
                setShowStatusDropdown(false)
                setShowDateDropdown(false)
              }}
              className={`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-300 hover:bg-gray-800'
                  : 'bg-white/80 border-white/30 text-slate-700 hover:bg-indigo-50'
              }`}
            >
              <FileText className="w-3 h-3" />
              <span>{templateFilter === 'all' ? 'All' : templateFilter.charAt(0).toUpperCase() + templateFilter.slice(1)}</span>
              <ChevronDown className="w-3 h-3" />
            </button>
            {showTemplateDropdown && (
              <div className={`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${
                isDarkMode
                  ? 'bg-black border-gray-700'
                  : 'bg-white/90 backdrop-blur-xl border-white/30'
              }`}>
                {['all', 'outpatient', 'discharge', 'surgery', 'radiology', 'dermatology', 'cardiology_echo', 'ivf_cycle', 'pathology'].map((template) => (
                  <button
                    key={template}
                    onClick={() => {
                      setTemplateFilter(template as 'all' | 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology')
                      setShowTemplateDropdown(false)
                    }}
                    className={`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${
                      templateFilter === template
                        ? isDarkMode
                          ? 'bg-teal-900/30 text-teal-400'
                          : 'bg-teal-50 text-teal-700'
                        : isDarkMode
                          ? 'text-gray-300 hover:bg-gray-800'
                          : 'text-amber-700 hover:bg-orange-50'
                    }`}
                  >
                    {template === 'all' ? 'All' :
                     template === 'cardiology_echo' ? 'Cardiology Echo' :
                     template === 'ivf_cycle' ? 'IVF Cycle' :
                     template.charAt(0).toUpperCase() + template.slice(1)}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Custom Date Input */}
        {dateFilter === 'custom' && (
          <div className="mb-4">
            <input
              type="date"
              value={customDate}
              onChange={(e) => setCustomDate(e.target.value)}
              className={`w-full px-3 py-2 text-xs rounded-lg border transition-colors ${
                isDarkMode
                  ? 'bg-black border-gray-700 text-gray-100 focus:border-indigo-500'
                  : 'bg-white/80 border-white/30 text-slate-800 focus:border-indigo-500'
              } focus:outline-none focus:ring-2 focus:ring-indigo-500/20`}
            />
          </div>
        )}

      </div>

      {/* Consultations List */}
      <div ref={scrollContainerRef} className="flex-1 overflow-y-auto">
        {(isLoading || externalIsLoading) ? (
          <div className={`p-4 text-center ${
            isDarkMode ? 'text-gray-300' : 'text-slate-600'
          }`}>
            {/* Magical Running Animation */}
            <div className="flex items-center justify-center mb-4">
              <motion.div
                className="relative"
                animate={{
                  x: [-20, 20, -20],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
                  <motion.div
                    className="w-2 h-2 bg-white rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [1, 0.7, 1]
                    }}
                    transition={{
                      duration: 0.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                </div>
                {/* Trail effect */}
                <motion.div
                  className="absolute top-1 left-1 w-6 h-6 bg-orange-300 rounded-full opacity-30"
                  animate={{
                    x: [-15, 15, -15],
                    scale: [0.8, 1.2, 0.8]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.1
                  }}
                />
              </motion.div>
            </div>
            <motion.p
              className="text-sm"
              animate={{
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              Loading consultations...
            </motion.p>
          </div>
        ) : dateAndFilteredConsultations.length === 0 ? (
          <div className={`p-4 text-center ${
            isDarkMode ? 'text-gray-300' : 'text-slate-600'
          }`}>
            <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {searchQuery ? 'No recordings found' : 'No recordings yet'}
            </p>
          </div>
        ) : (
          <div className="p-2">
            {dateAndFilteredConsultations.map((consultation) => (
              <motion.div
                key={consultation.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onConsultationSelect(consultation)}
                className={`p-3 mb-2 rounded-xl cursor-pointer transition-all duration-300 ${
                  selectedConsultation?.id === consultation.id
                    ? isDarkMode
                      ? 'bg-indigo-900/30 border border-indigo-500 shadow-lg'
                      : 'bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 shadow-lg'
                    : isDarkMode
                      ? 'bg-gray-900/50 hover:bg-gray-800/70 border border-gray-700/50 hover:border-gray-600'
                      : 'bg-white/70 hover:bg-white/90 backdrop-blur-sm border border-white/30 hover:border-white/50 hover:shadow-md'
                }`}
              >
                {/* Patient Info */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium text-sm truncate ${
                      isDarkMode ? 'text-gray-100' : 'text-slate-800'
                    }`}>
                      {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}
                    </h3>
                    <div className="flex items-center space-x-1 mt-1">
                      <Calendar className={`w-3 h-3 ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-500'
                      }`} />
                      <span className={`text-xs ${
                        isDarkMode ? 'text-gray-300' : 'text-amber-600'
                      }`}>
                        {isClient ? formatRelativeTime(consultation.created_at) : formatDate(consultation.created_at)}
                      </span>
                    </div>
                  </div>
                  {getStatusIcon(consultation.status)}
                </div>

                {/* Badges */}
                <div className="flex flex-wrap gap-1 mb-2">
                  <span className={getStatusBadge(consultation.status)}>
                    {consultation.status}
                  </span>
                  <span className={getTypeBadge(consultation.consultation_type || 'outpatient')}>
                    {consultation.consultation_type || 'outpatient'}
                  </span>
                </div>

                {/* Preview Text */}
                {(consultation.doctor_notes || consultation.ai_generated_note) && (
                  <p className={`text-xs line-clamp-2 ${
                    isDarkMode ? 'text-gray-300' : 'text-amber-700'
                  }`}>
                    {consultation.doctor_notes || 
                     consultation.ai_generated_note?.substring(0, 100) + '...'}
                  </p>
                )}
              </motion.div>
            ))}

            {/* Infinite Scroll Trigger - only show if we actually have more data */}
            {hasMore && consultations.length > 0 && (
              <div
                ref={loadMoreRef}
                className="flex justify-center py-4"
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className={`w-4 h-4 animate-spin ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`} />
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      Loading more...
                    </span>
                  </div>
                ) : (
                  <div className={`text-xs ${
                    isDarkMode ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    Scroll for more
                  </div>
                )}
              </div>
            )}

            {/* Show end message when no more data */}
            {!hasMore && consultations.length > 0 && (
              <div className="flex justify-center py-4">
                <div className={`text-xs ${
                  isDarkMode ? 'text-gray-500' : 'text-gray-400'
                }`}>
                  No more consultations
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
