"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 3
Performance Tuning and Optimization

This module implements advanced performance optimization techniques
including connection pooling, memory optimization, and CPU utilization improvements.
"""

import asyncio
import psutil
import gc
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import threading
from concurrent.futures import ThreadPoolExecutor
import redis
import json

from monitoring import logger, metrics, performance_context

# =====================================================
# PERFORMANCE CONFIGURATION
# =====================================================

PERFORMANCE_CONFIG = {
    'connection_pool': {
        'max_connections': 20,
        'min_connections': 5,
        'connection_timeout': 30,
        'idle_timeout': 300,
        'max_retries': 3
    },
    'memory_optimization': {
        'gc_threshold': 0.8,  # 80% memory usage
        'gc_interval_seconds': 60,
        'cache_cleanup_interval': 300,
        'max_cache_size_mb': 100
    },
    'cpu_optimization': {
        'max_worker_threads': None,  # Auto-detect based on CPU cores
        'thread_pool_size': None,    # Auto-detect
        'async_batch_size': 10,
        'cpu_threshold_high': 85.0,
        'cpu_threshold_low': 40.0
    },
    'network_optimization': {
        'request_timeout': 30,
        'connection_keepalive': True,
        'max_concurrent_requests': 50,
        'retry_backoff_factor': 2.0
    }
}

# =====================================================
# PERFORMANCE METRICS
# =====================================================

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    cpu_usage_percent: float
    memory_usage_percent: float
    memory_used_mb: float
    active_connections: int
    thread_pool_utilization: float
    cache_hit_rate: float
    average_response_time_ms: float
    requests_per_second: float
    timestamp: datetime

# =====================================================
# CONNECTION POOL MANAGER
# =====================================================

class ConnectionPoolManager:
    """Manages database and external service connection pools"""
    
    def __init__(self):
        self.pools: Dict[str, Any] = {}
        self.pool_stats: Dict[str, Dict[str, Any]] = {}
        self.monitoring_task: Optional[asyncio.Task] = None
        
        logger.info("🚀 Connection pool manager initialized")
    
    async def create_pool(self, 
                         pool_name: str, 
                         connection_factory: Callable,
                         **pool_config) -> Any:
        """Create a connection pool"""
        try:
            config = {**PERFORMANCE_CONFIG['connection_pool'], **pool_config}
            
            # Create connection pool (implementation depends on the service)
            # This is a simplified version - actual implementation would vary by service
            pool = {
                'name': pool_name,
                'factory': connection_factory,
                'config': config,
                'connections': [],
                'active_connections': 0,
                'created_at': datetime.utcnow()
            }
            
            # Pre-create minimum connections
            for _ in range(config['min_connections']):
                conn = await self._create_connection(connection_factory)
                if conn:
                    pool['connections'].append(conn)
            
            self.pools[pool_name] = pool
            self.pool_stats[pool_name] = {
                'total_connections': len(pool['connections']),
                'active_connections': 0,
                'connection_requests': 0,
                'connection_errors': 0,
                'average_wait_time_ms': 0.0
            }
            
            logger.info("Connection pool created", 
                       pool_name=pool_name,
                       initial_connections=len(pool['connections']))
            
            return pool
            
        except Exception as e:
            logger.error("Failed to create connection pool", 
                        pool_name=pool_name, error=str(e))
            raise
    
    async def get_connection(self, pool_name: str, timeout: float = 30.0):
        """Get connection from pool"""
        if pool_name not in self.pools:
            raise ValueError(f"Pool {pool_name} not found")
        
        pool = self.pools[pool_name]
        stats = self.pool_stats[pool_name]
        
        start_time = time.time()
        stats['connection_requests'] += 1
        
        try:
            # Try to get available connection
            if pool['connections']:
                conn = pool['connections'].pop()
                pool['active_connections'] += 1
                stats['active_connections'] += 1
                
                wait_time = (time.time() - start_time) * 1000
                stats['average_wait_time_ms'] = (
                    (stats['average_wait_time_ms'] * (stats['connection_requests'] - 1) + wait_time) /
                    stats['connection_requests']
                )
                
                logger.debug("Connection retrieved from pool", 
                           pool_name=pool_name,
                           wait_time_ms=wait_time)
                
                return conn
            
            # Create new connection if under max limit
            config = pool['config']
            if stats['total_connections'] < config['max_connections']:
                conn = await self._create_connection(pool['factory'])
                if conn:
                    pool['active_connections'] += 1
                    stats['active_connections'] += 1
                    stats['total_connections'] += 1
                    
                    logger.debug("New connection created", 
                               pool_name=pool_name,
                               total_connections=stats['total_connections'])
                    
                    return conn
            
            # Wait for connection to become available
            logger.warning("Connection pool exhausted, waiting", 
                          pool_name=pool_name)
            
            # Simplified wait logic - in production, use proper semaphore
            await asyncio.sleep(0.1)
            return await self.get_connection(pool_name, timeout - 0.1)
            
        except Exception as e:
            stats['connection_errors'] += 1
            logger.error("Failed to get connection from pool", 
                        pool_name=pool_name, error=str(e))
            raise
    
    async def return_connection(self, pool_name: str, connection: Any):
        """Return connection to pool"""
        if pool_name not in self.pools:
            return
        
        pool = self.pools[pool_name]
        stats = self.pool_stats[pool_name]
        
        try:
            # Validate connection is still good
            if await self._validate_connection(connection):
                pool['connections'].append(connection)
                pool['active_connections'] -= 1
                stats['active_connections'] -= 1
                
                logger.debug("Connection returned to pool", 
                           pool_name=pool_name)
            else:
                # Connection is bad, don't return it
                pool['active_connections'] -= 1
                stats['active_connections'] -= 1
                stats['total_connections'] -= 1
                
                logger.warning("Bad connection discarded", 
                             pool_name=pool_name)
                
        except Exception as e:
            logger.error("Failed to return connection to pool", 
                        pool_name=pool_name, error=str(e))
    
    async def _create_connection(self, factory: Callable):
        """Create a new connection"""
        try:
            return await factory()
        except Exception as e:
            logger.error("Failed to create connection", error=str(e))
            return None
    
    async def _validate_connection(self, connection: Any) -> bool:
        """Validate connection is still good"""
        try:
            # This would be service-specific validation
            # For now, assume all connections are valid
            return True
        except Exception:
            return False
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all pools"""
        return self.pool_stats.copy()
    
    async def cleanup_idle_connections(self):
        """Clean up idle connections"""
        current_time = datetime.utcnow()
        
        for pool_name, pool in self.pools.items():
            config = pool['config']
            idle_timeout = timedelta(seconds=config['idle_timeout'])
            
            # Remove connections that have been idle too long
            # This is simplified - in production, track connection last used time
            min_connections = config['min_connections']
            current_connections = len(pool['connections'])
            
            if current_connections > min_connections:
                excess_connections = current_connections - min_connections
                for _ in range(min(excess_connections, current_connections // 2)):
                    if pool['connections']:
                        conn = pool['connections'].pop()
                        self.pool_stats[pool_name]['total_connections'] -= 1
                        
                        logger.debug("Idle connection cleaned up", 
                                   pool_name=pool_name)

# =====================================================
# MEMORY OPTIMIZATION
# =====================================================

class MemoryOptimizer:
    """Memory usage optimization and garbage collection management"""
    
    def __init__(self):
        self.cache_registry: Dict[str, Dict[str, Any]] = {}
        self.gc_task: Optional[asyncio.Task] = None
        self.last_gc_time = datetime.utcnow()
        
        # Configure garbage collection
        gc.set_threshold(700, 10, 10)  # More aggressive GC
        
        logger.info("🚀 Memory optimizer initialized")
    
    async def start_monitoring(self):
        """Start memory monitoring task"""
        if self.gc_task:
            return
        
        self.gc_task = asyncio.create_task(self._memory_monitoring_loop())
        logger.info("Memory monitoring started")
    
    async def _memory_monitoring_loop(self):
        """Monitor memory usage and trigger cleanup"""
        while True:
            try:
                await asyncio.sleep(PERFORMANCE_CONFIG['memory_optimization']['gc_interval_seconds'])
                
                # Check memory usage
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                gc_threshold = PERFORMANCE_CONFIG['memory_optimization']['gc_threshold'] * 100
                
                if memory_percent > gc_threshold:
                    logger.warning("High memory usage detected", 
                                 memory_percent=memory_percent,
                                 threshold=gc_threshold)
                    
                    await self._perform_memory_cleanup()
                
                # Record memory metrics
                metrics.set_gauge('memory_usage_percent', memory_percent)
                metrics.set_gauge('memory_used_mb', memory.used / (1024 * 1024))
                
            except Exception as e:
                logger.error("Error in memory monitoring loop", error=str(e))
    
    async def _perform_memory_cleanup(self):
        """Perform memory cleanup operations"""
        start_time = time.time()
        
        try:
            # Force garbage collection
            collected = gc.collect()
            
            # Clean up caches
            await self._cleanup_caches()
            
            # Get memory usage after cleanup
            memory_after = psutil.virtual_memory()
            cleanup_time = time.time() - start_time
            
            logger.info("Memory cleanup completed", 
                       objects_collected=collected,
                       memory_percent_after=memory_after.percent,
                       cleanup_time_seconds=cleanup_time)
            
            # Record cleanup metrics
            metrics.increment_counter('memory_cleanup_total')
            metrics.record_histogram('memory_cleanup_duration_seconds', cleanup_time)
            
            self.last_gc_time = datetime.utcnow()
            
        except Exception as e:
            logger.error("Memory cleanup failed", error=str(e))
    
    async def _cleanup_caches(self):
        """Clean up application caches"""
        try:
            max_cache_size = PERFORMANCE_CONFIG['memory_optimization']['max_cache_size_mb'] * 1024 * 1024
            
            for cache_name, cache_info in self.cache_registry.items():
                cache = cache_info.get('cache')
                if cache and hasattr(cache, 'clear'):
                    # Estimate cache size (simplified)
                    cache_size = len(str(cache)) if cache else 0
                    
                    if cache_size > max_cache_size:
                        cache.clear()
                        logger.info("Cache cleared due to size", 
                                   cache_name=cache_name,
                                   estimated_size_bytes=cache_size)
            
        except Exception as e:
            logger.error("Cache cleanup failed", error=str(e))
    
    def register_cache(self, name: str, cache_object: Any):
        """Register a cache for monitoring and cleanup"""
        self.cache_registry[name] = {
            'cache': cache_object,
            'registered_at': datetime.utcnow()
        }
        
        logger.debug("Cache registered for monitoring", cache_name=name)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get current memory statistics"""
        memory = psutil.virtual_memory()
        
        return {
            'memory_percent': memory.percent,
            'memory_used_mb': memory.used / (1024 * 1024),
            'memory_available_mb': memory.available / (1024 * 1024),
            'last_gc_time': self.last_gc_time.isoformat(),
            'registered_caches': len(self.cache_registry),
            'gc_stats': {
                'collections': gc.get_stats(),
                'threshold': gc.get_threshold()
            }
        }

# =====================================================
# CPU OPTIMIZATION
# =====================================================

class CPUOptimizer:
    """CPU utilization optimization and thread pool management"""
    
    def __init__(self):
        self.thread_pools: Dict[str, ThreadPoolExecutor] = {}
        self.cpu_monitoring_task: Optional[asyncio.Task] = None
        
        # Auto-detect optimal thread pool sizes
        cpu_count = psutil.cpu_count()
        self.optimal_thread_count = min(cpu_count * 2, 32)  # Cap at 32 threads
        
        # Create default thread pools
        self._create_default_pools()
        
        logger.info("🚀 CPU optimizer initialized", 
                   cpu_count=cpu_count,
                   optimal_threads=self.optimal_thread_count)
    
    def _create_default_pools(self):
        """Create default thread pools for different workloads"""
        # I/O bound tasks (file processing, network requests)
        self.thread_pools['io_bound'] = ThreadPoolExecutor(
            max_workers=self.optimal_thread_count,
            thread_name_prefix='io-worker'
        )
        
        # CPU bound tasks (data processing, compression)
        cpu_workers = max(1, psutil.cpu_count() - 1)  # Leave one core for system
        self.thread_pools['cpu_bound'] = ThreadPoolExecutor(
            max_workers=cpu_workers,
            thread_name_prefix='cpu-worker'
        )
        
        # Quick tasks (validation, small computations)
        self.thread_pools['quick_tasks'] = ThreadPoolExecutor(
            max_workers=min(8, self.optimal_thread_count),
            thread_name_prefix='quick-worker'
        )
        
        logger.info("Default thread pools created", 
                   io_workers=self.optimal_thread_count,
                   cpu_workers=cpu_workers,
                   quick_workers=min(8, self.optimal_thread_count))
    
    async def execute_in_thread_pool(self, 
                                   pool_name: str, 
                                   func: Callable, 
                                   *args, **kwargs):
        """Execute function in specified thread pool"""
        if pool_name not in self.thread_pools:
            raise ValueError(f"Thread pool {pool_name} not found")
        
        pool = self.thread_pools[pool_name]
        
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(pool, func, *args, **kwargs)
            
            # Record execution metrics
            metrics.increment_counter('thread_pool_executions_total', 
                                    {'pool': pool_name, 'status': 'success'})
            
            return result
            
        except Exception as e:
            metrics.increment_counter('thread_pool_executions_total', 
                                    {'pool': pool_name, 'status': 'error'})
            logger.error("Thread pool execution failed", 
                        pool_name=pool_name, error=str(e))
            raise
    
    async def start_cpu_monitoring(self):
        """Start CPU monitoring task"""
        if self.cpu_monitoring_task:
            return
        
        self.cpu_monitoring_task = asyncio.create_task(self._cpu_monitoring_loop())
        logger.info("CPU monitoring started")
    
    async def _cpu_monitoring_loop(self):
        """Monitor CPU usage and adjust thread pool sizes"""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # Record CPU metrics
                metrics.set_gauge('cpu_usage_percent', cpu_percent)
                
                # Adjust thread pool sizes based on CPU usage
                await self._adjust_thread_pools(cpu_percent)
                
            except Exception as e:
                logger.error("Error in CPU monitoring loop", error=str(e))
    
    async def _adjust_thread_pools(self, cpu_percent: float):
        """Adjust thread pool sizes based on CPU usage"""
        try:
            high_threshold = PERFORMANCE_CONFIG['cpu_optimization']['cpu_threshold_high']
            low_threshold = PERFORMANCE_CONFIG['cpu_optimization']['cpu_threshold_low']
            
            if cpu_percent > high_threshold:
                # High CPU usage - reduce thread pool sizes
                logger.warning("High CPU usage detected, reducing thread pool sizes", 
                             cpu_percent=cpu_percent)
                
                # This is a simplified approach - in production, you'd need more sophisticated logic
                # to actually resize thread pools (which is complex in Python)
                
            elif cpu_percent < low_threshold:
                # Low CPU usage - can increase thread pool sizes if needed
                logger.debug("Low CPU usage, thread pools can handle more load", 
                           cpu_percent=cpu_percent)
            
        except Exception as e:
            logger.error("Failed to adjust thread pools", error=str(e))
    
    def get_thread_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get thread pool statistics"""
        stats = {}
        
        for pool_name, pool in self.thread_pools.items():
            stats[pool_name] = {
                'max_workers': pool._max_workers,
                'active_threads': len(pool._threads),
                'pending_tasks': pool._work_queue.qsize() if hasattr(pool._work_queue, 'qsize') else 0
            }
        
        return stats
    
    def shutdown_thread_pools(self):
        """Shutdown all thread pools"""
        for pool_name, pool in self.thread_pools.items():
            pool.shutdown(wait=True)
            logger.info("Thread pool shutdown", pool_name=pool_name)

# =====================================================
# GLOBAL PERFORMANCE MANAGER
# =====================================================

class PerformanceManager:
    """Centralized performance management"""
    
    def __init__(self):
        self.connection_pool_manager = ConnectionPoolManager()
        self.memory_optimizer = MemoryOptimizer()
        self.cpu_optimizer = CPUOptimizer()
        
        logger.info("🚀 Performance manager initialized")
    
    async def start_all_monitoring(self):
        """Start all performance monitoring tasks"""
        await self.memory_optimizer.start_monitoring()
        await self.cpu_optimizer.start_cpu_monitoring()
        
        logger.info("✅ All performance monitoring started")
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        return {
            'connection_pools': self.connection_pool_manager.get_pool_stats(),
            'memory': self.memory_optimizer.get_memory_stats(),
            'thread_pools': self.cpu_optimizer.get_thread_pool_stats(),
            'timestamp': datetime.utcnow().isoformat()
        }
    
    async def shutdown(self):
        """Shutdown performance manager"""
        self.cpu_optimizer.shutdown_thread_pools()
        
        if self.memory_optimizer.gc_task:
            self.memory_optimizer.gc_task.cancel()
        
        if self.cpu_optimizer.cpu_monitoring_task:
            self.cpu_optimizer.cpu_monitoring_task.cancel()
        
        logger.info("✅ Performance manager shutdown complete")

# =====================================================
# GLOBAL INSTANCE
# =====================================================

_performance_manager: Optional[PerformanceManager] = None

def get_performance_manager() -> PerformanceManager:
    """Get singleton performance manager"""
    global _performance_manager
    if _performance_manager is None:
        _performance_manager = PerformanceManager()
    return _performance_manager
