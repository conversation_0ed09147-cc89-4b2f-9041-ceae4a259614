'use client'

import React, { useEffect } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Document from '@tiptap/extension-document'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'
import { ContextualAssistButton } from './contextual-assist-button'

interface EnhancedEditableFieldProps {
  value: string
  onChange: (value: string) => void
  readOnly?: boolean
  multiline?: boolean
  fieldName?: string // For AI assist context
}

export function EnhancedEditableField({ 
  value, 
  onChange, 
  readOnly = false, 
  multiline = false,
  fieldName 
}: EnhancedEditableFieldProps) {
  const editor = useEditor({
    extensions: [
      Document,
      Paragraph,
      Text,
      StarterKit.configure({
        document: false,
        paragraph: false,
        text: false,
      }),
    ],
    content: value,
    editable: !readOnly,
    immediatelyRender: false, // Fix SSR hydration mismatch
    onUpdate: ({ editor }) => {
      const newValue = editor.getText()
      if (newValue !== value) {
        onChange(newValue)
      }
    },
  })

  useEffect(() => {
    if (editor && editor.getText() !== value) {
      editor.commands.setContent(value)
    }
  }, [editor, value])

  if (!editor) {
    return (
      <div className="min-h-[2.5rem] bg-gray-100 rounded animate-pulse" />
    )
  }

  return (
    <div className="group relative">
      <EditorContent
        editor={editor}
        className={`
          prose prose-sm max-w-none
          border border-gray-200 rounded-md px-3 py-2
          focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-transparent
          transition-all duration-200
          ${readOnly ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-300'}
          ${multiline ? 'min-h-[6rem]' : 'min-h-[2.5rem]'}
        `}
      />
      
      {/* AI Assist Button - only show if not read-only and fieldName is provided */}
      {!readOnly && fieldName && (
        <ContextualAssistButton
          fieldName={fieldName}
          currentValue={value}
          onSuggestionApplied={() => {
            // Refresh editor content after AI suggestion is applied
            if (editor) {
              editor.commands.setContent(value)
            }
          }}
        />
      )}
    </div>
  )
}
