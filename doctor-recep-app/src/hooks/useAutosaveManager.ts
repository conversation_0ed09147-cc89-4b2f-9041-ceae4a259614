'use client'

import { useEffect, useRef } from 'react'
import { useConsultationStore } from '@/lib/stores/consultation-store'
import { useAutosave } from '@/hooks/useAutosave'

/**
 * Headless autosave manager hook
 * Subscribes to Zustand store changes and handles complex autosave logic centrally
 * Following CTO's Phase 2 specification: centralized, cross-cutting concern management
 */
export function useAutosaveManager() {
  // Get autosave functions from the existing hook
  const { autoSaveText, autoSaveAudio, autoSaveImages, autoSaveJson } = useAutosave({
    onSuccess: (type) => {
      console.log(`✅ Autosave successful: ${type}`)
    },
    onError: (type, error) => {
      console.error(`❌ Autosave failed: ${type}`, error)
    }
  })

  // Use refs to track previous state for comparison
  const prevStateRef = useRef(useConsultationStore.getState())

  useEffect(() => {
    // Subscribe to any change in the Zustand store
    const unsubscribe = useConsultationStore.subscribe(
      (currentState) => {
        const prevState = prevStateRef.current

        // --- Logic for patientName changes ---
        if (prevState.patientName !== currentState.patientName) {
          // Only autosave if we have an existing consultation and actual content
          if (currentState.currentConsultation && currentState.patientName.trim()) {
            autoSaveText('patient_name', currentState.patientName, async (field: string, value: string) => {
              if (!currentState.currentConsultation) return
              
              const { updatePatientName } = await import('@/lib/actions/consultations')
              return updatePatientName(currentState.currentConsultation.id, value)
            })
          }
        }

        // --- Logic for selectedTemplate changes ---
        if (prevState.selectedTemplate !== currentState.selectedTemplate) {
          // Only autosave if we have an existing consultation
          if (currentState.currentConsultation) {
            autoSaveText('consultation_type', currentState.selectedTemplate, async (field: string, value: string) => {
              if (!currentState.currentConsultation) return

              const { updateConsultationType } = await import('@/lib/actions/consultations')
              return updateConsultationType(currentState.currentConsultation.id, value as any)
            })
          }
        }

        // --- Logic for additionalNotes changes ---
        if (prevState.additionalNotes !== currentState.additionalNotes) {
          // Only autosave if we have an existing consultation and actual content
          if (currentState.currentConsultation && currentState.additionalNotes.trim()) {
            autoSaveText('notes', currentState.additionalNotes, async (field: string, value: string) => {
              if (!currentState.currentConsultation) return

              const { updateAdditionalNotes } = await import('@/lib/actions/consultations')
              return updateAdditionalNotes(currentState.currentConsultation.id, value)
            })
          }
        }

        // Update the ref to the current state for the next comparison
        prevStateRef.current = currentState
      }
    )

    return unsubscribe // Clean up the subscription on unmount
  }, [autoSaveText])

  // Handle audio blob changes from external sources (like recording hook)
  // This will be called manually when audio recording completes
  const handleAudioBlobChange = (audioBlob: Blob | null, patientName: string, selectedTemplate: string) => {
    if (audioBlob && patientName.trim() && selectedTemplate) {
      autoSaveAudio(audioBlob, async (blob: Blob) => {
        if (!patientName.trim() || !selectedTemplate) {
          throw new Error('Patient name and template required')
        }

        const currentConsultation = useConsultationStore.getState().currentConsultation

        if (currentConsultation) {
          // Add additional audio to existing consultation
          const audioFile = new File([blob], `additional_audio_${Date.now()}.webm`, { type: 'audio/webm' })
          const { addAdditionalAudio } = await import('@/lib/actions/consultations')
          return addAdditionalAudio(currentConsultation.id, audioFile)
        } else {
          // Create new consultation
          const audioFile = new File([blob], 'audio.webm', { type: 'audio/webm' })
          const { createConsultationWithFiles } = await import('@/lib/actions/consultations')
          
          const result = await createConsultationWithFiles(
            audioFile,
            [], // No images for now
            [], // No additional audio files
            'doctor',
            undefined, // No additional notes for now
            selectedTemplate as any,
            patientName || undefined
          )

          // Update the store with the new consultation
          if (result && result.success && result.data) {
            // Convert Consultation to ConsultationData
            const consultationData = {
              id: result.data.id,
              primary_audio_url: result.data.primary_audio_url || '',
              additional_audio_urls: Array.isArray(result.data.additional_audio_urls)
                ? result.data.additional_audio_urls as string[]
                : [],
              image_urls: Array.isArray(result.data.image_urls)
                ? result.data.image_urls as string[]
                : [],
              submitted_by: result.data.submitted_by,
              consultation_type: result.data.consultation_type,
              patient_name: result.data.patient_name || undefined,
              doctor_notes: result.data.doctor_notes || undefined,
              additional_notes: result.data.additional_notes || undefined,
              ai_generated_note_json: result.data.ai_generated_note_json,
              edited_note_json: result.data.edited_note_json,
              status: result.data.status as 'pending' | 'processing' | 'generated' | 'failed',
              created_at: result.data.created_at,
              updated_at: result.data.updated_at
            }
            useConsultationStore.getState().setCurrentConsultation(consultationData)
          }

          return result
        }
      })
    }
  }

  // Handle image files changes from external sources
  const handleImageFilesChange = (imageFiles: File[]) => {
    const currentConsultation = useConsultationStore.getState().currentConsultation
    
    if (imageFiles.length > 0 && currentConsultation) {
      autoSaveImages(imageFiles, async (files: File[]) => {
        if (!currentConsultation) return
        
        const { addAdditionalImages } = await import('@/lib/actions/consultations')
        return addAdditionalImages(currentConsultation.id, files)
      })
    }
  }

  // Handle additional notes changes from external sources (legacy support)
  const handleAdditionalNotesChange = (notes: string) => {
    // Update the store, which will trigger the centralized autosave logic
    useConsultationStore.getState().setAdditionalNotes(notes)
  }

  // Handle JSON note changes from external sources with debouncing
  const handleJsonNoteChange = (jsonData: any) => {
    const currentConsultation = useConsultationStore.getState().currentConsultation

    if (currentConsultation && jsonData) {
      // Use the store's built-in saveEditedNote function which handles debouncing and error handling
      useConsultationStore.getState().saveEditedNote(jsonData)
    }
  }

  // Handle image array changes from external sources (legacy support)
  const handleImageArrayChange = (newImages: Array<{ id: string; file: File; preview?: string }>) => {
    // Update the store, which will trigger any necessary side effects
    useConsultationStore.getState().addImages(newImages)
  }

  return {
    // Manual trigger functions for external components (legacy support during migration)
    handleAudioBlobChange,
    handleImageFilesChange,
    handleAdditionalNotesChange,
    handleJsonNoteChange,
    handleImageArrayChange
  }
}
