import { NextRequest, NextResponse } from 'next/server'
import { getRedisClient } from '@/lib/redis'
import { createClient } from '@/lib/supabase/server'
import { verifySession } from '@/lib/auth/supabase-helpers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const consultationId = (await params).id

  try {
    // Verify user session
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (!consultationId) {
      return NextResponse.json(
        { error: 'Consultation ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔍 Checking status for consultation: ${consultationId}`)

    // CRITICAL: Redis-first status checking as mandated by phase1.md
    // This endpoint MUST only query Redis first, then database only if status is complete
    const redis = getRedisClient()

    try {
      const status = await redis.get(`status:${consultationId}`)

      if (!status) {
        // No status key in Redis means job is still processing
        console.log(`⏳ Job ${consultationId} still processing (no Redis key)`)
        return NextResponse.json(
          { status: 'processing' },
          { status: 202 } // 202 Accepted - still processing
        )
      }

      if (status === 'generated') {
        // Job completed successfully - NOW make single query to database
        console.log(`✅ Job ${consultationId} completed, fetching final data from database`)

        const supabase = await createClient()

        try {
          const { data: consultation, error } = await supabase
            .from('consultations')
            .select('ai_generated_note_json, edited_note_json, status, updated_at')
            .eq('id', consultationId)
            .single()

          if (error || !consultation) {
            console.error(`❌ Failed to fetch consultation ${consultationId}:`, error)
            return NextResponse.json(
              { error: 'Failed to fetch consultation data' },
              { status: 500 }
            )
          }

          // Return the final data - prefer edited version if it exists
          return NextResponse.json({
            status: 'generated',
            data: consultation.edited_note_json || consultation.ai_generated_note_json,
            updatedAt: consultation.updated_at
          })

        } catch (dbError) {
          console.error(`❌ Database error for consultation ${consultationId}:`, dbError)
          return NextResponse.json(
            { error: 'Database temporarily unavailable' },
            { status: 500 }
          )
        }
      }

      if (status === 'failed') {
        console.log(`❌ Job ${consultationId} failed`)
        return NextResponse.json({
          status: 'failed',
          error: 'AI generation failed. Please try again.'
        })
      }

      // Any other status (shouldn't happen, but handle gracefully)
      console.log(`⏳ Job ${consultationId} has status: ${status}`)
      return NextResponse.json(
        { status: status },
        { status: 202 }
      )

    } catch (redisError) {
      console.error(`❌ Redis error for job ${consultationId}:`, redisError)

      // CRITICAL: NO DATABASE FALLBACK as mandated by phase1.md
      // Return 503 Service Unavailable to protect database from thundering herd
      return NextResponse.json(
        { error: 'Status service temporarily unavailable. Your result will appear once ready.' },
        { status: 503 }
      )
    }

  } catch (error) {
    console.error(`❌ Status check error for consultation ${consultationId}:`, error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
