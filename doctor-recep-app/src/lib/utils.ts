import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
// RetryConfig type definition
export type RetryConfig = {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// File utility functions for Supabase storage
export function createFileFromBlob(blob: Blob, filename: string): File {
  return new File([blob], filename, { type: blob.type })
}

// Format duration in seconds to MM:SS
export function formatDuration(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// Format date for display (SSR-safe)
export function formatDate(dateString: string): string {
  const date = new Date(dateString)

  // Use a consistent format that works on both server and client
  const year = date.getFullYear()
  const month = date.toLocaleDateString('en-US', { month: 'short' })
  const day = date.getDate().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const ampm = date.getHours() >= 12 ? 'PM' : 'AM'
  const displayHours = date.getHours() % 12 || 12

  return `${month} ${day}, ${year} at ${displayHours.toString().padStart(2, '0')}:${minutes} ${ampm}`
}

// Format relative time (e.g., "2 hours ago")
export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  }
}

// Retry function with exponential backoff
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  config: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000
  }
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === config.maxAttempts) {
        throw lastError
      }

      const delay = Math.min(
        config.baseDelay * Math.pow(2, attempt - 1),
        config.maxDelay
      )

      console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms:`, error)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError!
}

// Validate URL string
export function isValidUrl(str: string): boolean {
  try {
    new URL(str)
    return true
  } catch {
    return false
  }
}

// Generate patient summary prompt for Gemini
export function generatePrompt(
  submittedBy: 'doctor' | 'receptionist'
): string {
  const contextNote = submittedBy === 'doctor'
    ? 'This consultation was recorded by the doctor during patient visit.'
    : 'This consultation is being reviewed by the receptionist for final summary.'

  return `
You are an AI assistant helping Indian doctors create patient consultation summaries.

Context: ${contextNote}

Please analyze the provided audio recording and any handwritten notes (if image provided) to generate a comprehensive patient summary.

Requirements:
- Language: English
- Tone: Professional
- Format: Standard medical format
- Include sections: Chief Complaint, History, Examination, Diagnosis, Treatment Plan, Follow-up

Instructions:
1. Transcribe the audio accurately
2. Extract key medical information
3. If image provided, include any relevant handwritten notes
4. Structure the summary according to the specified sections
5. Use appropriate medical terminology
6. Ensure the summary is clear and professional

Please provide a well-structured patient consultation summary based on the audio and image inputs.
  `.trim()
}

// Truncate text with ellipsis
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// Check if device supports audio recording
export function supportsAudioRecording(): boolean {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false
  }
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
}

// Check if device supports camera
export function supportsCamera(): boolean {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
}

// Format JSON consultation data for clean copying (doctor-friendly format)
export function formatConsultationForCopy(consultationData: any, consultationType: string = 'outpatient'): string {
  if (!consultationData || typeof consultationData !== 'object') {
    return ''
  }

  const sections: string[] = []

  // Helper function to format arrays as bullet points
  const formatArray = (arr: any[]): string => {
    if (!Array.isArray(arr) || arr.length === 0) return 'None'
    return arr.map(item => `• ${typeof item === 'object' ? JSON.stringify(item) : item}`).join('\n')
  }

  // Helper function to format objects as key-value pairs
  const formatObject = (obj: any, indent: string = ''): string => {
    if (!obj || typeof obj !== 'object') return String(obj || 'Not specified')

    return Object.entries(obj)
      .filter(([key, value]) => key !== 'schema_version' && value !== null && value !== undefined)
      .map(([key, value]) => {
        const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        if (typeof value === 'object' && !Array.isArray(value)) {
          return `${indent}${label}:\n${formatObject(value, indent + '  ')}`
        } else if (Array.isArray(value)) {
          return `${indent}${label}:\n${value.map(item => `${indent}  • ${item}`).join('\n')}`
        } else {
          return `${indent}${label}: ${value}`
        }
      })
      .join('\n')
  }

  // Patient Details
  if (consultationData.patient_details) {
    sections.push('PATIENT DETAILS')
    sections.push('================')
    sections.push(formatObject(consultationData.patient_details))
    sections.push('')
  }

  // Chief Complaints
  if (consultationData.chief_complaints || consultationData.chief_complaint) {
    sections.push('CHIEF COMPLAINTS')
    sections.push('================')
    const complaints = consultationData.chief_complaints || [consultationData.chief_complaint]
    sections.push(formatArray(complaints))
    sections.push('')
  }

  // History of Present Illness
  if (consultationData.history_of_present_illness) {
    sections.push('HISTORY OF PRESENT ILLNESS')
    sections.push('===========================')
    sections.push(consultationData.history_of_present_illness)
    sections.push('')
  }

  // Past Medical History
  if (consultationData.past_medical_history) {
    sections.push('PAST MEDICAL HISTORY')
    sections.push('====================')
    sections.push(formatArray(consultationData.past_medical_history))
    sections.push('')
  }

  // Examination Findings
  if (consultationData.examination_findings) {
    sections.push('EXAMINATION FINDINGS')
    sections.push('====================')
    sections.push(formatObject(consultationData.examination_findings))
    sections.push('')
  }

  // Diagnosis
  if (consultationData.provisional_diagnosis || consultationData.diagnosis) {
    sections.push('DIAGNOSIS')
    sections.push('=========')
    const diagnosis = consultationData.provisional_diagnosis || consultationData.diagnosis
    sections.push(formatObject(diagnosis))
    sections.push('')
  }

  // Investigations
  if (consultationData.investigations_ordered) {
    sections.push('INVESTIGATIONS ORDERED')
    sections.push('======================')
    sections.push(formatArray(consultationData.investigations_ordered))
    sections.push('')
  }

  // Prescription/Medications
  if (consultationData.prescription || consultationData.medications_on_discharge) {
    sections.push('MEDICATIONS')
    sections.push('===========')
    const meds = consultationData.prescription || consultationData.medications_on_discharge
    if (Array.isArray(meds)) {
      sections.push(meds.map(med => {
        if (typeof med === 'object') {
          return `• ${med.medication || med.name || 'Unknown'} - ${med.dosage || med.dose || ''} ${med.frequency || ''} ${med.duration || ''}`.trim()
        }
        return `• ${med}`
      }).join('\n'))
    } else {
      sections.push(formatObject(meds))
    }
    sections.push('')
  }

  // Follow-up Plan
  if (consultationData.follow_up_plan || consultationData.follow_up_instructions) {
    sections.push('FOLLOW-UP PLAN')
    sections.push('==============')
    const followUp = consultationData.follow_up_plan || consultationData.follow_up_instructions
    if (typeof followUp === 'object') {
      sections.push(formatObject(followUp))
    } else {
      sections.push(followUp)
    }
    sections.push('')
  }

  // Additional Notes
  if (consultationData.notes) {
    sections.push('ADDITIONAL NOTES')
    sections.push('================')
    sections.push(consultationData.notes)
    sections.push('')
  }

  // Handle consultation-type specific fields
  if (consultationType === 'discharge') {
    if (consultationData.hospital_course) {
      sections.push('HOSPITAL COURSE')
      sections.push('===============')
      sections.push(consultationData.hospital_course)
      sections.push('')
    }

    if (consultationData.discharge_instructions) {
      sections.push('DISCHARGE INSTRUCTIONS')
      sections.push('======================')
      sections.push(formatObject(consultationData.discharge_instructions))
      sections.push('')
    }
  }

  return sections.join('\n').trim()
}

// UI-Aware Copy Function - Extracts content from actual rendered DOM
export function copyFromRenderedUI(containerElement: HTMLElement): string {
  if (!containerElement) return ''

  const sections: string[] = []

  // Helper function to extract text content from EditableField components
  const extractEditableFieldValue = (element: HTMLElement): string => {
    // Look for TipTap editor content
    const editorContent = element.querySelector('[data-tiptap-editor]') ||
                         element.querySelector('.ProseMirror') ||
                         element.querySelector('[contenteditable]')

    if (editorContent) {
      return editorContent.textContent?.trim() || ''
    }

    // Fallback to regular text content
    return element.textContent?.trim() || ''
  }

  // Helper function to format field labels
  const formatLabel = (label: string): string => {
    return label.toUpperCase().replace(/_/g, ' ')
  }

  // Helper function to create section headers
  const createSectionHeader = (label: string): string[] => {
    const formattedLabel = formatLabel(label)
    return [
      formattedLabel,
      '='.repeat(formattedLabel.length)
    ]
  }

  // Traverse all field elements with data attributes
  const fieldElements = containerElement.querySelectorAll('[data-field-type]')

  fieldElements.forEach((element) => {
    const fieldType = element.getAttribute('data-field-type')
    const fieldPath = element.getAttribute('data-field-path')
    const fieldLabel = element.getAttribute('data-field-label')

    if (!fieldLabel || !fieldType) return

    // Skip nested fields - we'll handle them when processing their parent
    const isNestedField = fieldPath?.includes('.')
    if (isNestedField) return

    switch (fieldType) {
      case 'object':
        // Handle object fields (like patient_details, examination_findings)
        const objectContent = extractObjectContent(element as HTMLElement)
        if (objectContent.trim()) {
          sections.push(...createSectionHeader(fieldLabel))
          sections.push(objectContent)
          sections.push('')
        }
        break

      case 'array':
        // Handle array fields (like chief_complaints, prescription)
        const arrayContent = extractArrayContent(element as HTMLElement)
        if (arrayContent.trim()) {
          sections.push(...createSectionHeader(fieldLabel))
          sections.push(arrayContent)
          sections.push('')
        }
        break

      case 'string':
        // Handle simple string fields
        const stringValue = extractEditableFieldValue(element as HTMLElement)
        if (stringValue.trim()) {
          sections.push(...createSectionHeader(fieldLabel))
          sections.push(stringValue)
          sections.push('')
        }
        break
    }
  })

  return sections.join('\n').trim()
}

// Extract content from object fields (nested structure)
function extractObjectContent(objectElement: HTMLElement): string {
  const lines: string[] = []

  // Find all nested fields within this object
  const nestedFields = objectElement.querySelectorAll('[data-field-type="string"]')

  nestedFields.forEach((field) => {
    const label = field.getAttribute('data-field-label')
    const fieldPath = field.getAttribute('data-field-path')

    if (!label) return

    // Extract the value from the editable field
    const value = extractEditableFieldValue(field as HTMLElement)

    if (value.trim()) {
      // Format as "Label: Value"
      const formattedLabel = label.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      lines.push(`${formattedLabel}: ${value}`)
    }
  })

  return lines.join('\n')
}

// Extract content from array fields
function extractArrayContent(arrayElement: HTMLElement): string {
  const lines: string[] = []

  // Find all array items
  const arrayItems = arrayElement.querySelectorAll('[data-array-item-index]')

  arrayItems.forEach((item, index) => {
    // Check if this array contains objects or simple strings
    const objectFields = item.querySelectorAll('[data-field-type="string"]')

    if (objectFields.length > 0) {
      // Array of objects (like prescriptions)
      const objectLines: string[] = []

      objectFields.forEach((field) => {
        const label = field.getAttribute('data-field-label')
        const value = extractEditableFieldValue(field as HTMLElement)

        if (label && value.trim()) {
          const formattedLabel = label.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
          objectLines.push(`${formattedLabel}: ${value}`)
        }
      })

      if (objectLines.length > 0) {
        lines.push(`• ${objectLines.join(' - ')}`)
      }
    } else {
      // Array of simple strings (like chief complaints)
      const value = extractEditableFieldValue(item as HTMLElement)
      if (value.trim()) {
        lines.push(`• ${value}`)
      }
    }
  })

  return lines.join('\n')
}

// Helper function to extract text from EditableField (used by other functions)
function extractEditableFieldValue(element: HTMLElement): string {
  // Look for TipTap editor content
  const editorContent = element.querySelector('[data-tiptap-editor]') ||
                       element.querySelector('.ProseMirror') ||
                       element.querySelector('[contenteditable]')

  if (editorContent) {
    return editorContent.textContent?.trim() || ''
  }

  // Fallback to regular text content
  return element.textContent?.trim() || ''
}
