"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 3
Auto-Scaling and Capacity Management

This module implements intelligent auto-scaling based on real-time metrics
and predictive capacity planning for optimal resource utilization.
"""

import asyncio
import psutil
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import redis
import json
import statistics

from monitoring import logger, metrics, performance_context

# =====================================================
# AUTO-SCALING CONFIGURATION
# =====================================================

AUTOSCALING_CONFIG = {
    'metrics_collection_interval': 30,  # seconds
    'scaling_decision_interval': 120,   # seconds
    'metrics_history_window': 3600,     # 1 hour
    
    'cpu_thresholds': {
        'scale_up': 75.0,
        'scale_down': 30.0,
        'critical': 90.0
    },
    
    'memory_thresholds': {
        'scale_up': 80.0,
        'scale_down': 40.0,
        'critical': 95.0
    },
    
    'queue_thresholds': {
        'scale_up': 50,      # messages in queue
        'scale_down': 5,
        'critical': 200
    },
    
    'response_time_thresholds': {
        'scale_up': 5000,    # milliseconds
        'scale_down': 1000,
        'critical': 10000
    },
    
    'scaling_limits': {
        'min_instances': 1,
        'max_instances': 10,
        'scale_up_cooldown': 300,    # 5 minutes
        'scale_down_cooldown': 600,  # 10 minutes
    }
}

# =====================================================
# SCALING ENUMS AND DATA STRUCTURES
# =====================================================

class ScalingAction(str, Enum):
    """Scaling actions"""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    NO_ACTION = "no_action"
    EMERGENCY_SCALE = "emergency_scale"

class MetricType(str, Enum):
    """Metric types for scaling decisions"""
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    QUEUE_LENGTH = "queue_length"
    RESPONSE_TIME = "response_time"
    REQUEST_RATE = "request_rate"
    ERROR_RATE = "error_rate"

@dataclass
class ScalingMetric:
    """Individual scaling metric"""
    metric_type: MetricType
    value: float
    timestamp: datetime
    threshold_breached: bool
    severity: str  # 'normal', 'warning', 'critical'

@dataclass
class ScalingDecision:
    """Scaling decision with reasoning"""
    action: ScalingAction
    current_instances: int
    target_instances: int
    reasoning: str
    confidence: float
    metrics_snapshot: List[ScalingMetric]
    timestamp: datetime

@dataclass
class CapacityPrediction:
    """Capacity prediction for future load"""
    predicted_load: float
    confidence: float
    time_horizon_minutes: int
    recommended_instances: int
    reasoning: str

# =====================================================
# METRICS COLLECTOR
# =====================================================

class AutoScalingMetricsCollector:
    """Collects metrics for auto-scaling decisions"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.metrics_history: Dict[MetricType, List[ScalingMetric]] = {
            metric_type: [] for metric_type in MetricType
        }
        self.collection_task: Optional[asyncio.Task] = None
        
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
        except Exception as e:
            logger.error("Failed to initialize Redis for auto-scaling", error=str(e))
            return None
    
    async def start_collection(self):
        """Start metrics collection"""
        if self.collection_task:
            return
        
        self.collection_task = asyncio.create_task(self._collection_loop())
        logger.info("Auto-scaling metrics collection started")
    
    async def _collection_loop(self):
        """Main metrics collection loop"""
        while True:
            try:
                await asyncio.sleep(AUTOSCALING_CONFIG['metrics_collection_interval'])
                await self._collect_all_metrics()
                
            except Exception as e:
                logger.error("Error in metrics collection loop", error=str(e))
    
    async def _collect_all_metrics(self):
        """Collect all metrics for scaling decisions"""
        current_time = datetime.utcnow()
        
        # Collect system metrics
        cpu_metric = await self._collect_cpu_metric(current_time)
        memory_metric = await self._collect_memory_metric(current_time)
        
        # Collect application metrics
        queue_metric = await self._collect_queue_metric(current_time)
        response_time_metric = await self._collect_response_time_metric(current_time)
        request_rate_metric = await self._collect_request_rate_metric(current_time)
        error_rate_metric = await self._collect_error_rate_metric(current_time)
        
        # Store metrics
        metrics_to_store = [
            cpu_metric, memory_metric, queue_metric,
            response_time_metric, request_rate_metric, error_rate_metric
        ]
        
        for metric in metrics_to_store:
            if metric:
                self._store_metric(metric)
        
        # Clean up old metrics
        self._cleanup_old_metrics()
    
    async def _collect_cpu_metric(self, timestamp: datetime) -> Optional[ScalingMetric]:
        """Collect CPU usage metric"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            
            threshold_breached = (
                cpu_percent > AUTOSCALING_CONFIG['cpu_thresholds']['scale_up'] or
                cpu_percent < AUTOSCALING_CONFIG['cpu_thresholds']['scale_down']
            )
            
            severity = 'normal'
            if cpu_percent > AUTOSCALING_CONFIG['cpu_thresholds']['critical']:
                severity = 'critical'
            elif cpu_percent > AUTOSCALING_CONFIG['cpu_thresholds']['scale_up']:
                severity = 'warning'
            
            return ScalingMetric(
                metric_type=MetricType.CPU_USAGE,
                value=cpu_percent,
                timestamp=timestamp,
                threshold_breached=threshold_breached,
                severity=severity
            )
            
        except Exception as e:
            logger.error("Failed to collect CPU metric", error=str(e))
            return None
    
    async def _collect_memory_metric(self, timestamp: datetime) -> Optional[ScalingMetric]:
        """Collect memory usage metric"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            threshold_breached = (
                memory_percent > AUTOSCALING_CONFIG['memory_thresholds']['scale_up'] or
                memory_percent < AUTOSCALING_CONFIG['memory_thresholds']['scale_down']
            )
            
            severity = 'normal'
            if memory_percent > AUTOSCALING_CONFIG['memory_thresholds']['critical']:
                severity = 'critical'
            elif memory_percent > AUTOSCALING_CONFIG['memory_thresholds']['scale_up']:
                severity = 'warning'
            
            return ScalingMetric(
                metric_type=MetricType.MEMORY_USAGE,
                value=memory_percent,
                timestamp=timestamp,
                threshold_breached=threshold_breached,
                severity=severity
            )
            
        except Exception as e:
            logger.error("Failed to collect memory metric", error=str(e))
            return None
    
    async def _collect_queue_metric(self, timestamp: datetime) -> Optional[ScalingMetric]:
        """Collect queue length metric"""
        try:
            # Get queue length from Redis or message broker
            queue_length = 0
            if self.redis_client:
                # This would be specific to your queue implementation
                # For now, simulate queue length
                queue_length = 0  # Placeholder
            
            threshold_breached = (
                queue_length > AUTOSCALING_CONFIG['queue_thresholds']['scale_up'] or
                queue_length < AUTOSCALING_CONFIG['queue_thresholds']['scale_down']
            )
            
            severity = 'normal'
            if queue_length > AUTOSCALING_CONFIG['queue_thresholds']['critical']:
                severity = 'critical'
            elif queue_length > AUTOSCALING_CONFIG['queue_thresholds']['scale_up']:
                severity = 'warning'
            
            return ScalingMetric(
                metric_type=MetricType.QUEUE_LENGTH,
                value=float(queue_length),
                timestamp=timestamp,
                threshold_breached=threshold_breached,
                severity=severity
            )
            
        except Exception as e:
            logger.error("Failed to collect queue metric", error=str(e))
            return None
    
    async def _collect_response_time_metric(self, timestamp: datetime) -> Optional[ScalingMetric]:
        """Collect average response time metric"""
        try:
            # Get average response time from metrics
            # This would integrate with your metrics system
            avg_response_time = 1000.0  # Placeholder in milliseconds
            
            threshold_breached = (
                avg_response_time > AUTOSCALING_CONFIG['response_time_thresholds']['scale_up'] or
                avg_response_time < AUTOSCALING_CONFIG['response_time_thresholds']['scale_down']
            )
            
            severity = 'normal'
            if avg_response_time > AUTOSCALING_CONFIG['response_time_thresholds']['critical']:
                severity = 'critical'
            elif avg_response_time > AUTOSCALING_CONFIG['response_time_thresholds']['scale_up']:
                severity = 'warning'
            
            return ScalingMetric(
                metric_type=MetricType.RESPONSE_TIME,
                value=avg_response_time,
                timestamp=timestamp,
                threshold_breached=threshold_breached,
                severity=severity
            )
            
        except Exception as e:
            logger.error("Failed to collect response time metric", error=str(e))
            return None
    
    async def _collect_request_rate_metric(self, timestamp: datetime) -> Optional[ScalingMetric]:
        """Collect request rate metric"""
        try:
            # Get requests per second from metrics
            request_rate = 10.0  # Placeholder
            
            return ScalingMetric(
                metric_type=MetricType.REQUEST_RATE,
                value=request_rate,
                timestamp=timestamp,
                threshold_breached=False,  # Request rate doesn't have direct thresholds
                severity='normal'
            )
            
        except Exception as e:
            logger.error("Failed to collect request rate metric", error=str(e))
            return None
    
    async def _collect_error_rate_metric(self, timestamp: datetime) -> Optional[ScalingMetric]:
        """Collect error rate metric"""
        try:
            # Get error rate percentage from metrics
            error_rate = 1.0  # Placeholder percentage
            
            severity = 'normal'
            if error_rate > 10.0:
                severity = 'critical'
            elif error_rate > 5.0:
                severity = 'warning'
            
            return ScalingMetric(
                metric_type=MetricType.ERROR_RATE,
                value=error_rate,
                timestamp=timestamp,
                threshold_breached=error_rate > 5.0,
                severity=severity
            )
            
        except Exception as e:
            logger.error("Failed to collect error rate metric", error=str(e))
            return None
    
    def _store_metric(self, metric: ScalingMetric):
        """Store metric in history"""
        self.metrics_history[metric.metric_type].append(metric)
        
        # Persist to Redis for cross-instance sharing
        if self.redis_client:
            try:
                key = f"autoscaling:metrics:{metric.metric_type.value}"
                value = {
                    'value': metric.value,
                    'timestamp': metric.timestamp.isoformat(),
                    'threshold_breached': metric.threshold_breached,
                    'severity': metric.severity
                }
                
                self.redis_client.lpush(key, json.dumps(value))
                self.redis_client.ltrim(key, 0, 100)  # Keep last 100 metrics
                self.redis_client.expire(key, 7200)   # 2 hours TTL
                
            except Exception as e:
                logger.error("Failed to persist metric to Redis", error=str(e))
    
    def _cleanup_old_metrics(self):
        """Clean up old metrics from memory"""
        cutoff_time = datetime.utcnow() - timedelta(
            seconds=AUTOSCALING_CONFIG['metrics_history_window']
        )
        
        for metric_type in self.metrics_history:
            self.metrics_history[metric_type] = [
                metric for metric in self.metrics_history[metric_type]
                if metric.timestamp > cutoff_time
            ]
    
    def get_recent_metrics(self, metric_type: MetricType, minutes: int = 10) -> List[ScalingMetric]:
        """Get recent metrics of specified type"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        return [
            metric for metric in self.metrics_history[metric_type]
            if metric.timestamp > cutoff_time
        ]
    
    def get_metric_average(self, metric_type: MetricType, minutes: int = 10) -> Optional[float]:
        """Get average value for metric type over time period"""
        recent_metrics = self.get_recent_metrics(metric_type, minutes)
        
        if not recent_metrics:
            return None
        
        return statistics.mean(metric.value for metric in recent_metrics)

# =====================================================
# SCALING DECISION ENGINE
# =====================================================

class ScalingDecisionEngine:
    """Makes intelligent scaling decisions based on metrics"""
    
    def __init__(self, metrics_collector: AutoScalingMetricsCollector):
        self.metrics_collector = metrics_collector
        self.last_scaling_action = datetime.utcnow()
        self.current_instances = 1  # This would be tracked from actual infrastructure
        self.scaling_history: List[ScalingDecision] = []
        
    async def make_scaling_decision(self) -> ScalingDecision:
        """Make scaling decision based on current metrics"""
        current_time = datetime.utcnow()
        
        # Collect current metrics snapshot
        metrics_snapshot = await self._get_metrics_snapshot()
        
        # Analyze metrics and determine action
        action, reasoning, confidence = await self._analyze_metrics(metrics_snapshot)
        
        # Calculate target instances
        target_instances = await self._calculate_target_instances(action)
        
        # Create scaling decision
        decision = ScalingDecision(
            action=action,
            current_instances=self.current_instances,
            target_instances=target_instances,
            reasoning=reasoning,
            confidence=confidence,
            metrics_snapshot=metrics_snapshot,
            timestamp=current_time
        )
        
        # Store decision in history
        self.scaling_history.append(decision)
        
        # Log decision
        logger.info("Scaling decision made", 
                   action=action.value,
                   current_instances=self.current_instances,
                   target_instances=target_instances,
                   reasoning=reasoning,
                   confidence=confidence)
        
        return decision
    
    async def _get_metrics_snapshot(self) -> List[ScalingMetric]:
        """Get current snapshot of all metrics"""
        snapshot = []
        
        for metric_type in MetricType:
            recent_metrics = self.metrics_collector.get_recent_metrics(metric_type, 5)
            if recent_metrics:
                # Use most recent metric
                snapshot.append(recent_metrics[-1])
        
        return snapshot
    
    async def _analyze_metrics(self, metrics_snapshot: List[ScalingMetric]) -> tuple[ScalingAction, str, float]:
        """Analyze metrics and determine scaling action"""
        
        # Check for critical conditions first
        critical_metrics = [m for m in metrics_snapshot if m.severity == 'critical']
        if critical_metrics:
            return (
                ScalingAction.EMERGENCY_SCALE,
                f"Critical metrics detected: {[m.metric_type.value for m in critical_metrics]}",
                0.95
            )
        
        # Check cooldown periods
        if not self._can_scale():
            return (
                ScalingAction.NO_ACTION,
                "Scaling action in cooldown period",
                0.0
            )
        
        # Analyze individual metrics
        scale_up_votes = 0
        scale_down_votes = 0
        reasoning_parts = []
        
        for metric in metrics_snapshot:
            if metric.metric_type == MetricType.CPU_USAGE:
                if metric.value > AUTOSCALING_CONFIG['cpu_thresholds']['scale_up']:
                    scale_up_votes += 2  # CPU is important
                    reasoning_parts.append(f"High CPU: {metric.value:.1f}%")
                elif metric.value < AUTOSCALING_CONFIG['cpu_thresholds']['scale_down']:
                    scale_down_votes += 1
                    reasoning_parts.append(f"Low CPU: {metric.value:.1f}%")
            
            elif metric.metric_type == MetricType.MEMORY_USAGE:
                if metric.value > AUTOSCALING_CONFIG['memory_thresholds']['scale_up']:
                    scale_up_votes += 2  # Memory is important
                    reasoning_parts.append(f"High Memory: {metric.value:.1f}%")
                elif metric.value < AUTOSCALING_CONFIG['memory_thresholds']['scale_down']:
                    scale_down_votes += 1
                    reasoning_parts.append(f"Low Memory: {metric.value:.1f}%")
            
            elif metric.metric_type == MetricType.QUEUE_LENGTH:
                if metric.value > AUTOSCALING_CONFIG['queue_thresholds']['scale_up']:
                    scale_up_votes += 3  # Queue length is very important
                    reasoning_parts.append(f"High Queue: {metric.value}")
                elif metric.value < AUTOSCALING_CONFIG['queue_thresholds']['scale_down']:
                    scale_down_votes += 1
                    reasoning_parts.append(f"Low Queue: {metric.value}")
            
            elif metric.metric_type == MetricType.RESPONSE_TIME:
                if metric.value > AUTOSCALING_CONFIG['response_time_thresholds']['scale_up']:
                    scale_up_votes += 2
                    reasoning_parts.append(f"High Response Time: {metric.value:.0f}ms")
        
        # Make decision based on votes
        if scale_up_votes > scale_down_votes and scale_up_votes >= 2:
            confidence = min(0.9, scale_up_votes / 6.0)
            return (
                ScalingAction.SCALE_UP,
                f"Scale up recommended: {', '.join(reasoning_parts)}",
                confidence
            )
        elif scale_down_votes > scale_up_votes and scale_down_votes >= 2:
            confidence = min(0.8, scale_down_votes / 4.0)
            return (
                ScalingAction.SCALE_DOWN,
                f"Scale down recommended: {', '.join(reasoning_parts)}",
                confidence
            )
        else:
            return (
                ScalingAction.NO_ACTION,
                "Metrics within normal ranges",
                0.5
            )
    
    def _can_scale(self) -> bool:
        """Check if scaling action is allowed (cooldown check)"""
        if not self.scaling_history:
            return True
        
        last_decision = self.scaling_history[-1]
        time_since_last = (datetime.utcnow() - last_decision.timestamp).total_seconds()
        
        if last_decision.action == ScalingAction.SCALE_UP:
            return time_since_last > AUTOSCALING_CONFIG['scaling_limits']['scale_up_cooldown']
        elif last_decision.action == ScalingAction.SCALE_DOWN:
            return time_since_last > AUTOSCALING_CONFIG['scaling_limits']['scale_down_cooldown']
        
        return True
    
    async def _calculate_target_instances(self, action: ScalingAction) -> int:
        """Calculate target number of instances"""
        current = self.current_instances
        min_instances = AUTOSCALING_CONFIG['scaling_limits']['min_instances']
        max_instances = AUTOSCALING_CONFIG['scaling_limits']['max_instances']
        
        if action == ScalingAction.SCALE_UP:
            # Scale up by 1 instance, or more for emergency
            increment = 2 if action == ScalingAction.EMERGENCY_SCALE else 1
            return min(max_instances, current + increment)
        
        elif action == ScalingAction.SCALE_DOWN:
            # Scale down by 1 instance
            return max(min_instances, current - 1)
        
        else:
            return current

# =====================================================
# GLOBAL AUTO-SCALING MANAGER
# =====================================================

class AutoScalingManager:
    """Main auto-scaling manager"""
    
    def __init__(self):
        self.metrics_collector = AutoScalingMetricsCollector()
        self.decision_engine = ScalingDecisionEngine(self.metrics_collector)
        self.decision_task: Optional[asyncio.Task] = None
        
        logger.info("🚀 Auto-scaling manager initialized")
    
    async def start(self):
        """Start auto-scaling system"""
        await self.metrics_collector.start_collection()
        
        self.decision_task = asyncio.create_task(self._decision_loop())
        
        logger.info("✅ Auto-scaling system started")
    
    async def _decision_loop(self):
        """Main decision-making loop"""
        while True:
            try:
                await asyncio.sleep(AUTOSCALING_CONFIG['scaling_decision_interval'])
                
                decision = await self.decision_engine.make_scaling_decision()
                
                if decision.action != ScalingAction.NO_ACTION:
                    await self._execute_scaling_decision(decision)
                
            except Exception as e:
                logger.error("Error in scaling decision loop", error=str(e))
    
    async def _execute_scaling_decision(self, decision: ScalingDecision):
        """Execute scaling decision"""
        logger.info("Executing scaling decision", 
                   action=decision.action.value,
                   target_instances=decision.target_instances)
        
        # This would integrate with your infrastructure provider
        # For now, just log the decision
        
        # Update current instance count
        self.decision_engine.current_instances = decision.target_instances
        self.decision_engine.last_scaling_action = datetime.utcnow()
        
        # Record scaling action
        metrics.increment_counter('autoscaling_actions_total', 
                                {'action': decision.action.value})
    
    def get_status(self) -> Dict[str, Any]:
        """Get auto-scaling system status"""
        recent_decisions = self.decision_engine.scaling_history[-5:]
        
        return {
            'current_instances': self.decision_engine.current_instances,
            'last_scaling_action': self.decision_engine.last_scaling_action.isoformat(),
            'recent_decisions': [
                {
                    'action': d.action.value,
                    'target_instances': d.target_instances,
                    'reasoning': d.reasoning,
                    'confidence': d.confidence,
                    'timestamp': d.timestamp.isoformat()
                }
                for d in recent_decisions
            ],
            'metrics_summary': {
                metric_type.value: self.metrics_collector.get_metric_average(metric_type, 10)
                for metric_type in MetricType
            }
        }
    
    async def shutdown(self):
        """Shutdown auto-scaling system"""
        if self.decision_task:
            self.decision_task.cancel()
        
        if self.metrics_collector.collection_task:
            self.metrics_collector.collection_task.cancel()
        
        logger.info("✅ Auto-scaling system shutdown")

# =====================================================
# GLOBAL INSTANCE
# =====================================================

_autoscaling_manager: Optional[AutoScalingManager] = None

def get_autoscaling_manager() -> AutoScalingManager:
    """Get singleton auto-scaling manager"""
    global _autoscaling_manager
    if _autoscaling_manager is None:
        _autoscaling_manager = AutoScalingManager()
    return _autoscaling_manager
