// Comprehensive Zod schemas for all consultation types based on prompts.json
// This is the single source of truth for JSON structure validation

import { z } from 'zod';

// Common schemas used across multiple consultation types
const PatientDetailsSchema = z.object({
  name: z.string().describe("Full Name or Initials if unknown"),
  age: z.string().describe("Age in years"),
  gender: z.enum(["Male", "Female", "Other"]).describe("Patient gender"),
  date_of_consultation: z.string().describe("DD-MM-YYYY format"),
  time: z.string().optional().describe("HH:MM AM/PM format"),
  patient_id: z.string().optional().describe("Hospital-specific Patient ID"),
  hospital_number: z.string().optional().describe("Hospital/IP Number"),
});

const VitalsSchema = z.object({
  bp: z.string().optional().describe("Blood pressure"),
  pulse: z.string().optional().describe("Pulse rate"),
  temp: z.string().optional().describe("Temperature"),
  spo2: z.string().optional().describe("Oxygen saturation"),
  respiration_rate: z.string().optional().describe("Respiratory rate"),
});

const PrescriptionItemSchema = z.object({
  drug_name: z.string().describe("Name of the medication"),
  dose: z.string().describe("Dosage amount"),
  frequency: z.string().describe("How often to take"),
  duration: z.string().describe("How long to take"),
});

const DiagnosisSchema = z.object({
  diagnosis: z.string().describe("Primary diagnosis"),
  icd_10_code: z.string().describe("ICD-10 code"),
  differential: z.string().optional().describe("Differential diagnosis if applicable"),
});

// OUTPATIENT CONSULTATION SCHEMA
const ExaminationFindingsSchema = z.object({
  vitals: VitalsSchema,
  general_examination: z.string().optional().describe("General physical examination findings"),
  systemic_exam: z.record(z.string()).optional().describe("System-wise examination findings"),
});

export const OutpatientNoteSchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: PatientDetailsSchema,
  chief_complaints: z.array(z.string()).describe("List of primary complaints with duration"),
  history_of_present_illness: z.string().describe("Detailed narrative of symptom progression"),
  past_medical_history: z.array(z.string()).describe("Previous medical conditions and allergies"),
  examination_findings: ExaminationFindingsSchema,
  provisional_diagnosis: DiagnosisSchema,
  investigations_ordered: z.array(z.string()).describe("Tests and investigations ordered"),
  prescription: z.array(PrescriptionItemSchema).describe("Medications prescribed"),
  follow_up_plan: z.string().describe("Follow-up instructions"),
  notes: z.string().optional().describe("Additional remarks"),
  doctor_id: z.string().optional().describe("Doctor identification"),
});

// DISCHARGE SUMMARY SCHEMA
export const DischargeSummarySchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: PatientDetailsSchema.extend({
    admission_date: z.string().describe("DD-MM-YYYY"),
    discharge_date: z.string().describe("DD-MM-YYYY"),
    consultant: z.string().describe("Consultant doctor name"),
    department: z.string().describe("Department name"),
  }),
  presenting_complaints: z.string().describe("Primary complaints at admission"),
  history_of_present_illness: z.string().describe("Chronological narrative of illness"),
  past_medical_surgical_history: z.string().describe("Previous medical and surgical history"),
  allergies: z.string().describe("Known allergies or NKDA"),
  personal_family_history: z.string().describe("Social habits and family history"),
  examination_findings_at_admission: z.string().describe("Physical examination at admission"),
  investigations: z.string().describe("Laboratory and imaging results"),
  final_diagnosis: z.string().describe("Primary diagnosis with ICD-10 codes"),
  hospital_course_treatment: z.string().describe("Inpatient treatment summary"),
  surgery_performed: z.string().optional().describe("Surgical procedures if any"),
  condition_at_discharge: z.string().describe("Patient status at discharge"),
  medications_on_discharge: z.array(PrescriptionItemSchema).describe("Discharge medications"),
  advice_on_discharge: z.record(z.string()).describe("Discharge instructions"),
  prognosis_outcome: z.string().describe("Overall prognosis"),
  doctor_signature: z.string().describe("Doctor name and signature"),
});

// SURGERY/OPERATIVE NOTE SCHEMA
export const OperativeNoteSchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: PatientDetailsSchema,
  date_and_time_of_surgery: z.string().describe("Surgery date and time"),
  indications_for_surgery: z.string().describe("Clinical justification"),
  preoperative_diagnosis: z.string().describe("Diagnosis before surgery"),
  postoperative_diagnosis: z.string().describe("Final diagnosis with ICD-10 code"),
  consent: z.string().describe("Informed consent confirmation"),
  type_of_anesthesia: z.string().describe("Anesthesia type administered"),
  positioning_and_preparation: z.string().describe("Patient positioning and prep"),
  operative_procedure: z.record(z.string()).describe("Step-by-step surgical procedure"),
  intraoperative_findings: z.string().describe("Surgical findings"),
  intraoperative_complications: z.string().describe("Complications or 'None noted'"),
  postoperative_plan: z.string().describe("Post-op care instructions"),
  condition_at_end_of_procedure: z.string().describe("Patient status post-surgery"),
  specimen_sent_for_hpe: z.string().describe("HPE specimen details"),
  signatures: z.record(z.string()).describe("All personnel signatures"),
});

// RADIOLOGY REPORT SCHEMA
export const RadiologyReportSchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: PatientDetailsSchema,
  exam_details: z.object({
    type_of_exam: z.string().describe("Type of imaging study"),
    date_of_exam: z.string().describe("DD-MM-YYYY"),
    time_of_exam: z.string().describe("HH:MM AM/PM"),
    reason_for_exam: z.string().describe("Clinical indication"),
  }),
  comparison: z.string().describe("Prior studies comparison"),
  technique: z.string().describe("Scan technique description"),
  findings: z.record(z.string()).describe("Organ-by-organ findings"),
  impression: z.array(z.string()).describe("Diagnoses with ICD-10 codes"),
  recommendations: z.string().describe("Follow-up recommendations"),
  radiologist_id: z.string().describe("Radiologist identification"),
});

// DERMATOLOGY CASE NOTE SCHEMA
export const DermatologyNoteSchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: PatientDetailsSchema,
  subjective: z.object({
    chief_complaint: z.string().describe("Primary reason for visit with duration"),
    history_of_present_illness: z.string().describe("Detailed chronological narrative"),
    review_of_systems: z.string().describe("Brief review of relevant systems"),
  }),
  past_medical_history: z.array(z.string()).describe("Medical comorbidities and allergies"),
  objective_physical_exam: z.object({
    vital_signs: VitalsSchema.optional(),
    general_appearance: z.string().describe("Overall patient appearance"),
    lesion_description: z.object({
      location: z.string().describe("Precise anatomical location"),
      morphology: z.string().describe("Primary and secondary lesions"),
      size: z.string().describe("Measurements in mm or cm"),
      color: z.string().describe("Color description"),
      shape_border: z.string().describe("Shape and border characteristics"),
    }),
    procedure_note: z.string().optional().describe("Any procedures performed"),
  }),
  assessment_diagnosis: z.array(DiagnosisSchema).describe("Primary and differential diagnoses"),
  plan_treatment: z.object({
    investigations: z.string().optional().describe("Tests ordered"),
    follow_up: z.string().describe("Follow-up instructions"),
    advice: z.string().describe("Patient advice and recommendations"),
  }),
  doctor_id: z.string().describe("Doctor identification"),
});

// CARDIOLOGY ECHO SCHEMA
export const EchocardiogramSchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_information: PatientDetailsSchema.extend({
    referring_physician: z.string().describe("Referring doctor name"),
    date_of_study: z.string().describe("DD-MM-YYYY"),
  }),
  reason_for_study: z.string().describe("Clinical indication for echo"),
  measurements: z.record(z.string()).describe("2D & Doppler measurements"),
  findings: z.object({
    left_ventricle: z.string().describe("LV size, function, wall motion"),
    right_ventricle: z.string().describe("RV size and function"),
    atria: z.string().describe("Atrial size assessment"),
    valves: z.object({
      aortic_valve: z.string().describe("Aortic valve assessment"),
      mitral_valve: z.string().describe("Mitral valve assessment"),
      tricuspid_valve: z.string().describe("Tricuspid valve assessment"),
      pulmonic_valve: z.string().describe("Pulmonic valve assessment"),
    }),
    pericardium: z.string().describe("Pericardial assessment"),
    great_vessels: z.string().describe("Aorta and pulmonary artery"),
  }),
  conclusion: z.array(z.string()).describe("Summary with ICD-10 codes"),
  cardiologist_id: z.string().describe("Cardiologist identification"),
});

// IVF CYCLE SUMMARY SCHEMA
export const IVFCycleSummarySchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: z.object({
    female_partner_name: z.string().describe("Female partner full name"),
    male_partner_name: z.string().describe("Male partner full name"),
    female_age: z.string().describe("Female age in years"),
    male_age: z.string().describe("Male age in years"),
    patient_id: z.string().describe("Hospital-specific ID"),
    cycle_number: z.string().describe("IVF cycle number"),
    primary_diagnosis_female: z.string().describe("Female diagnosis with ICD-10"),
    primary_diagnosis_male: z.string().describe("Male diagnosis with ICD-10"),
  }),
  procedure_details: z.object({
    procedure: z.string().describe("Type of procedure performed"),
    date_of_procedure: z.string().describe("DD-MM-YYYY"),
  }),
  oocyte_data: z.object({
    oocytes_retrieved: z.string().describe("Number of oocytes retrieved"),
    mature_oocytes: z.string().describe("Number of mature (MII) oocytes"),
    oocytes_fertilized: z.string().describe("Number fertilized"),
  }),
  embryo_development_log: z.object({
    embryos_day_3: z.string().describe("Number of embryos on Day 3"),
    embryos_day_5: z.string().describe("Number of blastocysts on Day 5"),
    embryos_cryopreserved: z.string().describe("Number cryopreserved"),
  }),
  embryo_transfer_note: z.object({
    embryos_transferred: z.string().describe("Number transferred"),
    embryo_quality: z.string().describe("Quality/grade of embryos"),
    procedure_ease: z.string().describe("Transfer procedure notes"),
  }),
  follow_up_instructions: z.object({
    medications: z.string().describe("Medication instructions"),
    activity: z.string().describe("Activity restrictions"),
    next_appointment: z.string().describe("Follow-up schedule"),
  }),
  doctor_id: z.string().describe("Doctor identification"),
});

// PATHOLOGY/HISTOPATHOLOGY SCHEMA
export const HistopathologySchema = z.object({
  schema_version: z.string().default("1.0"),
  patient_details: PatientDetailsSchema.extend({
    specimen_id: z.string().describe("Unique laboratory specimen ID"),
  }),
  specimen_details: z.object({
    specimen_source: z.string().describe("Anatomical site and specimen type"),
    date_received: z.string().describe("DD-MM-YYYY"),
    clinical_history: z.string().describe("Clinical indication"),
  }),
  gross_description: z.string().describe("Macroscopic specimen description"),
  microscopic_description: z.string().describe("Detailed microscopic findings"),
  final_diagnosis: z.string().describe("Definitive diagnosis with ICD-10/ICD-O codes"),
  comments: z.string().describe("Additional pathologist comments"),
  pathologist_id: z.string().describe("Pathologist identification"),
});

export type OutpatientNote = z.infer<typeof OutpatientNoteSchema>;
export type DischargeSummary = z.infer<typeof DischargeSummarySchema>;
export type OperativeNote = z.infer<typeof OperativeNoteSchema>;
export type RadiologyReport = z.infer<typeof RadiologyReportSchema>;
export type DermatologyNote = z.infer<typeof DermatologyNoteSchema>;
export type EchocardiogramReport = z.infer<typeof EchocardiogramSchema>;
export type IVFCycleSummary = z.infer<typeof IVFCycleSummarySchema>;
export type HistopathologyReport = z.infer<typeof HistopathologySchema>;

// Schema mapping for consultation types
export const CONSULTATION_SCHEMAS = {
  outpatient: OutpatientNoteSchema,
  discharge: DischargeSummarySchema,
  surgery: OperativeNoteSchema,
  radiology: RadiologyReportSchema,
  dermatology: DermatologyNoteSchema,
  cardiology_echo: EchocardiogramSchema,
  ivf_cycle: IVFCycleSummarySchema,
  pathology: HistopathologySchema,
} as const;

// Helper function to get schema by consultation type
export function getSchemaForConsultationType(consultationType: string) {
  return CONSULTATION_SCHEMAS[consultationType as keyof typeof CONSULTATION_SCHEMAS] || OutpatientNoteSchema;
}
