import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/auth/supabase-helpers'
import { getRedisClient } from '@/lib/redis'

/**
 * Job Status API - Part of the UI Reconciliation for Optimistic Updates
 * 
 * This endpoint allows the frontend to poll for the status of background
 * database operations after optimistic updates.
 */
export async function GET(request: NextRequest) {
  try {
    // Verify user session
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('id')

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    // Get job status from Redis using v2 atomic format
    const redis = getRedisClient()
    const jobKeyV2 = `job:v2:${jobId}`

    const jobDataV2 = await redis.hGetAll(jobKeyV2)

    if (Object.keys(jobDataV2).length === 0) {
      return NextResponse.json(
        { error: 'Job not found or expired' },
        { status: 404 }
      )
    }

    // Parse job data from v2 atomic hash format
    const job = {
      ...jobDataV2,
      result: jobDataV2.result ? JSON.parse(jobDataV2.result) : undefined,
      metadata: jobDataV2.metadata ? JSON.parse(jobDataV2.metadata) : {}
    }

    // Validate job ownership (security check)
    if (jobDataV2.userId !== session.userId) {
      return NextResponse.json(
        { error: 'Unauthorized access to job' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        jobId: jobDataV2.jobId,
        status: jobDataV2.status,
        type: jobDataV2.type,
        createdAt: jobDataV2.createdAt,
        completedAt: jobDataV2.completedAt,
        error: jobDataV2.error,
        result: job.result, // Use parsed result
        metadata: job.metadata // Use parsed metadata
      }
    })

  } catch (error) {
    console.error('Job status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Update job status - Used internally by background processes
 */
export async function PATCH(request: NextRequest) {
  try {
    // This endpoint should only be called internally
    // In production, you might want to add additional security checks
    const authHeader = request.headers.get('authorization')
    const internalToken = process.env.INTERNAL_API_TOKEN

    if (!internalToken || authHeader !== `Bearer ${internalToken}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { jobId, status, error, result, metadata } = body

    if (!jobId || !status) {
      return NextResponse.json(
        { error: 'Job ID and status are required' },
        { status: 400 }
      )
    }

    // Update job status in Redis using atomic operations
    const redis = getRedisClient()
    const jobKeyV2 = `job:v2:${jobId}`

    // Check if job exists
    const existingJobDataV2 = await redis.hGetAll(jobKeyV2)

    if (Object.keys(existingJobDataV2).length === 0) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Use atomic HSET operations for v2 format
    const ttl = ['completed', 'failed'].includes(status) ? 3600 : 1800 // 1 hour for completed, 30 min for pending
    const completedAt = ['completed', 'failed'].includes(status)
      ? new Date().toISOString()
      : existingJobDataV2.completedAt

    // Atomic updates using HSET
    const updates: Record<string, string> = {
      status,
      ...(error && { error }),
      ...(result && { result: JSON.stringify(result) }),
      ...(metadata && { metadata: JSON.stringify(metadata) }),
      ...(completedAt && { completedAt })
    }

    // Perform atomic update
    await redis.hSet(jobKeyV2, updates)
    await redis.expire(jobKeyV2, ttl)

    // Get the updated job data atomically
    const updatedJobData = await redis.hGetAll(jobKeyV2)

    // Convert back to expected format
    const updatedJob = {
      ...updatedJobData,
      result: updatedJobData.result ? JSON.parse(updatedJobData.result) : undefined,
      metadata: updatedJobData.metadata ? JSON.parse(updatedJobData.metadata) : {}
    }

    return NextResponse.json({
      success: true,
      data: updatedJob
    })

  } catch (error) {
    console.error('Job status update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
