"""
CELER AI ENTERPRISE ARCHITECTURE 3.0
Message Schemas - Pydantic Models for Event Validation

This module defines Pydantic models for validating and serializing
messages in the event-driven architecture.
"""

from typing import List, Dict, Any, Optional, Union, Literal
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator
import json

# =====================================================
# ENUMS
# =====================================================

class EventType(str, Enum):
    """Event types in the system"""
    CONSULTATION_CREATE = "consultation.create"
    CONSULTATION_UPDATE = "consultation.update"
    CONSULTATION_STATUS_CHANGE = "consultation.status_change"
    PROCESSING_STARTED = "processing.started"
    PROCESSING_COMPLETED = "processing.completed"
    PROCESSING_FAILED = "processing.failed"
    FILE_UPLOADED = "file.uploaded"
    FILE_DELETED = "file.deleted"

class ConsultationType(str, Enum):
    """Consultation types"""
    OUTPATIENT = "outpatient"
    DISCHARGE = "discharge"
    SURGERY = "surgery"
    RADIOLOGY = "radiology"
    DERMATOLOGY = "dermatology"
    CARDIOLOGY_ECHO = "cardiology_echo"
    IVF_CYCLE = "ivf_cycle"
    PATHOLOGY = "pathology"

class SubmittedBy(str, Enum):
    """Who submitted the consultation"""
    DOCTOR = "doctor"
    RECEPTIONIST = "receptionist"

class Priority(str, Enum):
    """Processing priority"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"

class ProcessingStatus(str, Enum):
    """Processing status"""
    PENDING = "pending"
    ACCEPTED = "accepted"
    PROCESSING = "processing"
    GENERATED = "generated"
    FAILED = "failed"
    CANCELLED = "cancelled"
    APPROVED = "approved"

# =====================================================
# BASE MODELS
# =====================================================

class BaseEvent(BaseModel):
    """Base event model with common fields"""
    schema_version: str = Field(default="1.0", description="Schema version")
    correlation_id: str = Field(..., description="Correlation ID for tracing")
    event_type: EventType = Field(..., description="Type of event")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# =====================================================
# CONSULTATION EVENT MODEL
# =====================================================

class ConsultationCreateEvent(BaseEvent):
    """Event for consultation creation - matches frontend schema"""
    consultation_id: str = Field(..., description="Unique consultation ID")
    user_id: str = Field(..., description="User who created the consultation")

    # File information
    primary_audio_url: str = Field(..., description="Primary audio file URL")
    additional_audio_urls: List[str] = Field(default_factory=list, description="Additional audio URLs")
    image_urls: List[str] = Field(default_factory=list, description="Image URLs")

    # Consultation details
    consultation_type: ConsultationType = Field(..., description="Type of consultation")
    submitted_by: SubmittedBy = Field(..., description="Who submitted the consultation")
    patient_name: Optional[str] = Field(None, description="Patient name")
    doctor_notes: Optional[str] = Field(None, description="Doctor notes")
    additional_notes: Optional[str] = Field(None, description="Additional notes")

    # File metadata
    total_file_size_bytes: int = Field(default=0, ge=0, description="Total file size")

    # Processing metadata
    retry_count: int = Field(default=0, ge=0, description="Retry count")
    priority: Priority = Field(default=Priority.NORMAL, description="Processing priority")

    event_type: Literal[EventType.CONSULTATION_CREATE] = Field(default=EventType.CONSULTATION_CREATE)

# =====================================================
# VALIDATION UTILITIES
# =====================================================

def validate_consultation_event(event_data: Dict[str, Any]) -> Optional[ConsultationCreateEvent]:
    """Validate consultation creation event"""
    try:
        return ConsultationCreateEvent(**event_data)
    except Exception as e:
        print(f"❌ Event validation failed: {e}")
        return None

def serialize_event(event: BaseEvent) -> str:
    """Serialize event to JSON string"""
    return event.json()

def deserialize_consultation_event(event_json: str) -> Optional[ConsultationCreateEvent]:
    """Deserialize JSON string to consultation event"""
    try:
        event_data = json.loads(event_json)
        return validate_consultation_event(event_data)
    except Exception as e:
        print(f"❌ Event deserialization failed: {e}")
        return None

# =====================================================
# BASE MODELS
# =====================================================

class BaseEvent(BaseModel):
    """Base event model with common fields"""
    schema_version: str = Field(default="1.0", description="Schema version")
    correlation_id: str = Field(..., description="Correlation ID for tracing")
    event_type: EventType = Field(..., description="Type of event")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class FileInfo(BaseModel):
    """File information model"""
    url: str = Field(..., description="File URL")
    checksum: Optional[str] = Field(None, description="File checksum for integrity")
    size: int = Field(..., ge=0, description="File size in bytes")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type")
    uploaded_at: datetime = Field(default_factory=datetime.utcnow, description="Upload timestamp")
    
    @validator('url')
    def validate_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL must start with http:// or https://')
        return v

# =====================================================
# CONSULTATION EVENTS
# =====================================================

class ConsultationCreateEvent(BaseEvent):
    """Event for consultation creation"""
    consultation_id: str = Field(..., description="Unique consultation ID")
    user_id: str = Field(..., description="User who created the consultation")
    session_id: Optional[str] = Field(None, description="Session ID")
    
    # File information
    primary_audio_url: str = Field(..., description="Primary audio file URL")
    primary_audio_checksum: Optional[str] = Field(None, description="Primary audio checksum")
    additional_audio_urls: List[str] = Field(default_factory=list, description="Additional audio URLs")
    additional_audio_checksums: List[str] = Field(default_factory=list, description="Additional audio checksums")
    image_urls: List[str] = Field(default_factory=list, description="Image URLs")
    image_checksums: List[str] = Field(default_factory=list, description="Image checksums")
    
    # Consultation details
    consultation_type: ConsultationType = Field(..., description="Type of consultation")
    submitted_by: SubmittedBy = Field(..., description="Who submitted the consultation")
    patient_name: Optional[str] = Field(None, max_length=255, description="Patient name")
    doctor_notes: Optional[str] = Field(None, max_length=2000, description="Doctor notes")
    additional_notes: Optional[str] = Field(None, max_length=2000, description="Additional notes")
    
    # File metadata
    total_file_size_bytes: int = Field(default=0, ge=0, description="Total file size")
    
    # Processing metadata
    retry_count: int = Field(default=0, ge=0, description="Retry count")
    priority: Priority = Field(default=Priority.NORMAL, description="Processing priority")
    
    # Client information
    client_version: Optional[str] = Field(None, description="Client version")
    user_agent: Optional[str] = Field(None, description="User agent")
    ip_address: Optional[str] = Field(None, description="IP address")
    
    event_type: Literal[EventType.CONSULTATION_CREATE] = Field(default=EventType.CONSULTATION_CREATE)
    
    @validator('additional_audio_checksums')
    def validate_audio_checksums(cls, v, values):
        if 'additional_audio_urls' in values:
            urls = values['additional_audio_urls']
            if len(v) > 0 and len(v) != len(urls):
                raise ValueError('Number of audio checksums must match number of audio URLs')
        return v
    
    @validator('image_checksums')
    def validate_image_checksums(cls, v, values):
        if 'image_urls' in values:
            urls = values['image_urls']
            if len(v) > 0 and len(v) != len(urls):
                raise ValueError('Number of image checksums must match number of image URLs')
        return v

class ConsultationUpdateEvent(BaseEvent):
    """Event for consultation updates"""
    consultation_id: str = Field(..., description="Consultation ID")
    user_id: str = Field(..., description="User who updated the consultation")
    
    # Updated fields
    status: Optional[ProcessingStatus] = Field(None, description="New status")
    ai_generated_note_json: Optional[Dict[str, Any]] = Field(None, description="AI generated note")
    edited_note_json: Optional[Dict[str, Any]] = Field(None, description="Edited note")
    processing_started_at: Optional[datetime] = Field(None, description="Processing start time")
    processing_completed_at: Optional[datetime] = Field(None, description="Processing completion time")
    
    # Version control
    expected_version: Optional[int] = Field(None, description="Expected version for optimistic locking")
    
    event_type: Literal[EventType.CONSULTATION_UPDATE] = Field(default=EventType.CONSULTATION_UPDATE)

class ConsultationStatusChangeEvent(BaseEvent):
    """Event for consultation status changes"""
    consultation_id: str = Field(..., description="Consultation ID")
    old_status: ProcessingStatus = Field(..., description="Previous status")
    new_status: ProcessingStatus = Field(..., description="New status")
    changed_by: str = Field(..., description="User who changed the status")
    reason: Optional[str] = Field(None, description="Reason for status change")
    
    event_type: Literal[EventType.CONSULTATION_STATUS_CHANGE] = Field(default=EventType.CONSULTATION_STATUS_CHANGE)

# =====================================================
# PROCESSING EVENTS
# =====================================================

class ProcessingStartedEvent(BaseEvent):
    """Event when processing starts"""
    consultation_id: str = Field(..., description="Consultation ID")
    batch_id: Optional[str] = Field(None, description="Batch ID if part of batch processing")
    worker_instance: str = Field(..., description="Worker instance identifier")
    estimated_duration_seconds: Optional[int] = Field(None, description="Estimated processing time")
    
    event_type: Literal[EventType.PROCESSING_STARTED] = Field(default=EventType.PROCESSING_STARTED)

class ProcessingCompletedEvent(BaseEvent):
    """Event when processing completes successfully"""
    consultation_id: str = Field(..., description="Consultation ID")
    batch_id: Optional[str] = Field(None, description="Batch ID if part of batch processing")
    worker_instance: str = Field(..., description="Worker instance identifier")
    processing_duration_seconds: float = Field(..., ge=0, description="Actual processing time")
    
    # Results
    ai_generated_note_json: Dict[str, Any] = Field(..., description="Generated note JSON")
    file_urls_processed: List[str] = Field(default_factory=list, description="URLs of processed files")
    
    # Performance metrics
    memory_usage_mb: Optional[float] = Field(None, description="Memory usage in MB")
    cpu_usage_percent: Optional[float] = Field(None, description="CPU usage percentage")
    
    event_type: Literal[EventType.PROCESSING_COMPLETED] = Field(default=EventType.PROCESSING_COMPLETED)

class ProcessingFailedEvent(BaseEvent):
    """Event when processing fails"""
    consultation_id: str = Field(..., description="Consultation ID")
    batch_id: Optional[str] = Field(None, description="Batch ID if part of batch processing")
    worker_instance: str = Field(..., description="Worker instance identifier")
    
    # Error information
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    stack_trace: Optional[str] = Field(None, description="Stack trace")
    
    # Context
    retry_count: int = Field(default=0, ge=0, description="Current retry count")
    max_retries: int = Field(default=3, ge=0, description="Maximum retries allowed")
    will_retry: bool = Field(default=False, description="Whether this will be retried")
    
    event_type: Literal[EventType.PROCESSING_FAILED] = Field(default=EventType.PROCESSING_FAILED)

# =====================================================
# FILE EVENTS
# =====================================================

class FileUploadedEvent(BaseEvent):
    """Event when file is uploaded"""
    consultation_id: str = Field(..., description="Consultation ID")
    file_info: FileInfo = Field(..., description="File information")
    file_type: str = Field(..., description="Type of file (audio/image)")
    uploaded_by: str = Field(..., description="User who uploaded the file")
    
    event_type: Literal[EventType.FILE_UPLOADED] = Field(default=EventType.FILE_UPLOADED)

class FileDeletedEvent(BaseEvent):
    """Event when file is deleted"""
    consultation_id: str = Field(..., description="Consultation ID")
    file_url: str = Field(..., description="URL of deleted file")
    file_type: str = Field(..., description="Type of file (audio/image)")
    deleted_by: str = Field(..., description="User who deleted the file")
    reason: str = Field(..., description="Reason for deletion")
    
    event_type: Literal[EventType.FILE_DELETED] = Field(default=EventType.FILE_DELETED)

# =====================================================
# BATCH PROCESSING MODELS
# =====================================================

class BatchProcessingInfo(BaseModel):
    """Information about batch processing"""
    batch_id: str = Field(..., description="Unique batch ID")
    batch_size: int = Field(..., ge=1, description="Number of items in batch")
    consultation_ids: List[str] = Field(..., description="Consultation IDs in batch")
    correlation_ids: List[str] = Field(..., description="Correlation IDs in batch")
    started_at: datetime = Field(..., description="Batch processing start time")
    worker_instance: str = Field(..., description="Worker instance processing the batch")
    
    # Results
    successful_count: int = Field(default=0, ge=0, description="Number of successful items")
    failed_count: int = Field(default=0, ge=0, description="Number of failed items")
    failed_consultation_ids: List[str] = Field(default_factory=list, description="IDs of failed consultations")
    
    # Performance
    processing_duration_ms: Optional[int] = Field(None, description="Processing duration in milliseconds")
    memory_usage_mb: Optional[float] = Field(None, description="Memory usage in MB")
    cpu_usage_percent: Optional[float] = Field(None, description="CPU usage percentage")

# =====================================================
# VALIDATION UTILITIES
# =====================================================

def validate_event(event_data: Dict[str, Any], event_type: EventType) -> Union[BaseEvent, None]:
    """Validate event data against appropriate schema"""
    try:
        if event_type == EventType.CONSULTATION_CREATE:
            return ConsultationCreateEvent(**event_data)
        elif event_type == EventType.CONSULTATION_UPDATE:
            return ConsultationUpdateEvent(**event_data)
        elif event_type == EventType.CONSULTATION_STATUS_CHANGE:
            return ConsultationStatusChangeEvent(**event_data)
        elif event_type == EventType.PROCESSING_STARTED:
            return ProcessingStartedEvent(**event_data)
        elif event_type == EventType.PROCESSING_COMPLETED:
            return ProcessingCompletedEvent(**event_data)
        elif event_type == EventType.PROCESSING_FAILED:
            return ProcessingFailedEvent(**event_data)
        elif event_type == EventType.FILE_UPLOADED:
            return FileUploadedEvent(**event_data)
        elif event_type == EventType.FILE_DELETED:
            return FileDeletedEvent(**event_data)
        else:
            raise ValueError(f"Unknown event type: {event_type}")
            
    except Exception as e:
        print(f"❌ Event validation failed: {e}")
        return None

def serialize_event(event: BaseEvent) -> str:
    """Serialize event to JSON string"""
    return event.json()

def deserialize_event(event_json: str, event_type: EventType) -> Union[BaseEvent, None]:
    """Deserialize JSON string to event object"""
    try:
        event_data = json.loads(event_json)
        return validate_event(event_data, event_type)
    except Exception as e:
        print(f"❌ Event deserialization failed: {e}")
        return None

# =====================================================
# SCHEMA REGISTRY
# =====================================================

EVENT_SCHEMAS = {
    EventType.CONSULTATION_CREATE: ConsultationCreateEvent,
    EventType.CONSULTATION_UPDATE: ConsultationUpdateEvent,
    EventType.CONSULTATION_STATUS_CHANGE: ConsultationStatusChangeEvent,
    EventType.PROCESSING_STARTED: ProcessingStartedEvent,
    EventType.PROCESSING_COMPLETED: ProcessingCompletedEvent,
    EventType.PROCESSING_FAILED: ProcessingFailedEvent,
    EventType.FILE_UPLOADED: FileUploadedEvent,
    EventType.FILE_DELETED: FileDeletedEvent,
}

def get_event_schema(event_type: EventType) -> BaseModel:
    """Get schema class for event type"""
    return EVENT_SCHEMAS.get(event_type, BaseEvent)

def get_event_schema_json(event_type: EventType) -> Dict[str, Any]:
    """Get JSON schema for event type"""
    schema_class = get_event_schema(event_type)
    return schema_class.schema()
