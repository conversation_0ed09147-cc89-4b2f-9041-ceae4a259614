/**
 * CELER ASSIST API ENDPOINT
 * Frontend-only AI enhancement tool using Groq via Vercel AI SDK
 * 
 * Provides contextual suggestions for editing consultation JSON fields
 * Built on existing enterprise architecture without backend changes
 */

import { NextRequest, NextResponse } from 'next/server'
import { groq } from '@ai-sdk/groq'
import { generateText } from 'ai'
import { verifySession } from '@/lib/auth/supabase-helpers'

// Context interface for AI requests
interface AssistContext {
  fieldName: string
  currentValue: string
  fullConsultation: any
  patientAge?: number
  consultationType?: string
  customRequest?: string
}

export async function POST(request: NextRequest) {
  try {
    // Verify user session using existing auth system
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { fieldName, currentValue, fullConsultation, customRequest } = body as AssistContext

    // Validate required fields
    if (!fieldName || currentValue === undefined || !fullConsultation) {
      return NextResponse.json(
        { error: 'Missing required fields: fieldName, currentValue, fullConsultation' },
        { status: 400 }
      )
    }

    // Build context for AI
    const context = buildAssistContext(body)

    // Generate correlation ID for tracking (use existing enterprise pattern)
    const correlationId = `assist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    console.log(`🤖 [${correlationId}] AI assist request for field: ${fieldName}`)

    // Sanitize data before sending to Groq (basic privacy protection)
    const sanitizedConsultation = sanitizeForGroq(fullConsultation)

    // Build prompt for Groq
    const prompt = buildPrompt(fieldName, currentValue, sanitizedConsultation, customRequest)

    // Call Groq via Vercel AI SDK with JSON mode (2025 approach)
    const startTime = Date.now()
    const result = await generateText({
      model: groq('meta-llama/llama-4-scout-17b-16e-instruct'),
      experimental_providerMetadata: {
        groq: {
          response_format: { type: 'json_object' }
        }
      },
      system: `You are a medical AI assistant specialized in Clinical Decision Intelligence (CDI) that helps improve medical documentation accuracy and completeness.

CRITICAL MEDICAL GUIDELINES:
- Only suggest improvements based on the provided consultation context
- NEVER hallucinate or invent medical information not present in the context
- If insufficient information exists to improve the field, return empty suggestions array
- Maintain strict medical accuracy and professional terminology
- Focus only on the specific field requested, not other aspects of the consultation
- Preserve all existing medical facts while improving clarity and completeness

Your response must be a valid JSON object with this exact structure:
{
  "suggestions": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "newValue": "string",
      "confidence": number (0.0 to 1.0)
    }
  ],
  "reasoning": "string"
}

CONFIDENCE SCORING:
- 0.9-1.0: High confidence based on clear medical context
- 0.7-0.8: Moderate confidence with reasonable medical basis
- 0.5-0.6: Low confidence, minor improvements only
- Below 0.5: Do not suggest (insufficient context)

IMPORTANT:
- Return ONLY the JSON object, no other text
- Include 1-3 suggestions maximum
- If current value is medically appropriate or lacks context, return empty suggestions array
- Each suggestion must have all required fields`,
      prompt,
      temperature: 0.7,
    })

    const latency = Date.now() - startTime

    // Parse and validate the JSON response
    // Strip markdown formatting if present (```json blocks)
    let cleanText = result.text.trim()
    if (cleanText.startsWith('```json')) {
      cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '')
    } else if (cleanText.startsWith('```')) {
      cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '')
    }

    const object = JSON.parse(cleanText)

    // Basic validation to ensure required fields exist
    if (!object.suggestions || !Array.isArray(object.suggestions) || !object.reasoning) {
      throw new Error('Invalid response structure from AI')
    }

    // Log interaction using existing structured logging
    console.log(`✅ [${correlationId}] AI suggestions generated`, {
      fieldName,
      suggestionsCount: object.suggestions.length,
      latency,
      userId: session.userId,
      provider: 'groq',
      model: 'meta-llama/llama-4-scout-17b-16e-instruct'
    })

    // Track usage in Redis (use existing metrics system)
    await trackAIUsage(session.userId, latency) // Track usage without cost estimation

    return NextResponse.json({
      success: true,
      correlationId,
      ...object
    })

  } catch (error) {
    console.error('❌ AI assist error:', error)

    // Return graceful fallback
    return NextResponse.json({
      success: false,
      error: 'AI assistance temporarily unavailable',
      suggestions: [{
        id: 'fallback',
        title: 'AI temporarily unavailable',
        description: 'Please try again in a moment or edit manually',
        newValue: '',
        confidence: 0
      }],
      reasoning: 'Service temporarily unavailable'
    }, { status: 200 }) // Return 200 for graceful degradation
  }
}

// Build context from consultation data
function buildAssistContext(body: AssistContext): AssistContext {
  return {
    fieldName: body.fieldName,
    currentValue: body.currentValue,
    fullConsultation: body.fullConsultation,
    patientAge: body.fullConsultation?.patient_details?.age,
    consultationType: body.fullConsultation?.consultation_type || 'outpatient',
    customRequest: body.customRequest
  }
}

// Simple data sanitization before sending to Groq
function sanitizeForGroq(consultation: any) {
  const sanitized = { ...consultation }
  
  // Remove obvious PII
  if (sanitized.patient_details?.name) {
    sanitized.patient_details.name = '[PATIENT]'
  }
  
  // Keep medical data for context (already anonymized in our system)
  return sanitized
}

// Build intelligent prompt for Groq with medical focus
function buildPrompt(fieldName: string, currentValue: string, consultation: any, customRequest?: string): string {
  // Extract key medical context
  const patientAge = consultation?.patient_details?.age
  const consultationType = consultation?.consultation_type || 'outpatient'
  const chiefComplaints = consultation?.chief_complaints || []
  const diagnosis = consultation?.diagnosis || consultation?.primary_diagnosis

  // Build medical context summary
  const medicalContext = [
    patientAge ? `Patient Age: ${patientAge}` : null,
    `Consultation Type: ${consultationType}`,
    chiefComplaints.length ? `Chief Complaints: ${chiefComplaints.join(', ')}` : null,
    diagnosis ? `Diagnosis: ${diagnosis}` : null
  ].filter(Boolean).join('\n')

  const basePrompt = `MEDICAL DOCUMENTATION IMPROVEMENT TASK

TARGET FIELD: "${fieldName}"
CURRENT VALUE: "${currentValue || '[EMPTY]'}"

MEDICAL CONTEXT:
${medicalContext}

FULL CONSULTATION DATA:
${JSON.stringify(consultation, null, 2)}

${customRequest ? `SPECIFIC REQUEST: ${customRequest}` : ''}

CLINICAL DECISION INTELLIGENCE GUIDELINES:
1. Only suggest improvements based on the provided medical context
2. Do NOT add information not present in the consultation data
3. If current value is empty and no relevant context exists, return empty suggestions
4. Focus on improving medical terminology, clarity, and completeness
5. Maintain professional medical documentation standards
6. Consider patient age, consultation type, and presenting complaints
7. Ensure suggestions are contextually appropriate and medically sound

IMPROVEMENT FOCUS AREAS:
- Medical terminology accuracy and specificity
- Professional documentation standards
- Completeness based on available context
- Clarity and readability for healthcare providers
- Consistency with consultation type and patient demographics

If the current value is already medically appropriate or you lack sufficient context to improve it meaningfully, return an empty suggestions array.`

  return basePrompt
}

// Simple usage tracking using existing Redis metrics
async function trackAIUsage(userId: string, latency: number) {
  try {
    // This would integrate with existing Redis metrics system
    // For now, just log the usage without cost estimation (Groq pricing varies)
    console.log(`📊 AI usage tracked: userId=${userId}, latency=${latency}ms`)
  } catch (error) {
    console.error('Failed to track AI usage:', error)
  }
}
