/**
 * LEAN ENTERPRISE ARCHITECTURE 3.0
 * Direct-to-R2 Upload Client
 * 
 * This module implements the gold standard direct-to-R2 upload pattern:
 * 1. Client requests pre-signed URL from secure backend
 * 2. Client uploads directly to Cloudflare R2 (bypassing Vercel)
 * 3. Maximum security, performance, and cost efficiency
 */

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface UploadRequest {
  consultationId: string
  fileName: string
  fileType: string
  fileSize: number
}

export interface PreSignedUrlResponse {
  success: boolean
  uploadUrl: string
  publicUrl: string
  fileName: string
  expiresIn: number
  metadata: {
    consultationId: string
    originalFileName: string
    fileType: string
    fileSize: number
  }
}

export interface DirectUploadResult {
  success: boolean
  publicUrl?: string
  fileName?: string
  error?: string
  uploadTime?: number
}

export interface BatchUploadResult {
  success: boolean
  results: DirectUploadResult[]
  successfulUploads: string[]
  failedUploads: { file: string; error: string }[]
  totalTime: number
}

// =====================================================
// DIRECT-TO-R2 UPLOAD FUNCTIONS
// =====================================================

/**
 * Upload a single file directly to R2 using pre-signed URL
 */
export async function uploadFileDirectToR2(
  file: File,
  consultationId: string
): Promise<DirectUploadResult> {
  const startTime = Date.now()
  
  try {
    console.log(`🔐 Starting direct-to-R2 upload for: ${file.name}`)
    
    // Step 1: Request pre-signed URL from secure backend
    const preSignedResponse = await requestPreSignedUrl({
      consultationId,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size
    })
    
    if (!preSignedResponse.success) {
      throw new Error('Failed to get pre-signed URL')
    }
    
    // Step 2: Upload directly to R2 using pre-signed URL
    const uploadResult = await uploadToR2(file, preSignedResponse.uploadUrl)
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Direct upload failed')
    }
    
    const uploadTime = Date.now() - startTime
    
    console.log(`✅ Direct-to-R2 upload completed in ${uploadTime}ms: ${preSignedResponse.publicUrl}`)
    
    return {
      success: true,
      publicUrl: preSignedResponse.publicUrl,
      fileName: preSignedResponse.fileName,
      uploadTime
    }
    
  } catch (error) {
    const uploadTime = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : 'Unknown upload error'
    
    console.error(`❌ Direct-to-R2 upload failed for ${file.name}:`, errorMessage)
    
    return {
      success: false,
      error: errorMessage,
      uploadTime
    }
  }
}

/**
 * Upload multiple files in parallel using direct-to-R2 pattern
 */
export async function uploadMultipleFilesDirectToR2(
  files: File[],
  consultationId: string,
  maxConcurrency: number = 3
): Promise<BatchUploadResult> {
  const startTime = Date.now()
  
  console.log(`🚀 Starting batch direct-to-R2 upload for ${files.length} files`)
  
  // Process files in batches to avoid overwhelming the system
  const results: DirectUploadResult[] = []
  
  for (let i = 0; i < files.length; i += maxConcurrency) {
    const batch = files.slice(i, i + maxConcurrency)
    
    const batchPromises = batch.map(file => 
      uploadFileDirectToR2(file, consultationId)
    )
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }
  
  // Analyze results
  const successfulUploads = results
    .filter(r => r.success && r.publicUrl)
    .map(r => r.publicUrl!)
  
  const failedUploads = results
    .filter(r => !r.success)
    .map((r, index) => ({
      file: files[index]?.name || 'unknown',
      error: r.error || 'Unknown error'
    }))
  
  const totalTime = Date.now() - startTime
  
  console.log(`✅ Batch upload completed: ${successfulUploads.length}/${files.length} successful in ${totalTime}ms`)
  
  return {
    success: failedUploads.length === 0,
    results,
    successfulUploads,
    failedUploads,
    totalTime
  }
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Request pre-signed URL from secure backend
 */
async function requestPreSignedUrl(request: UploadRequest): Promise<PreSignedUrlResponse> {
  try {
    const response = await fetch('/api/storage/generate-upload-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }
    
    return await response.json()
    
  } catch (error) {
    console.error('Failed to request pre-signed URL:', error)
    throw error
  }
}

/**
 * Upload file directly to R2 using pre-signed URL
 */
async function uploadToR2(file: File, preSignedUrl: string): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(preSignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      }
    })
    
    if (!response.ok) {
      throw new Error(`R2 upload failed: ${response.status} ${response.statusText}`)
    }
    
    return { success: true }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown R2 upload error'
    return { success: false, error: errorMessage }
  }
}

// =====================================================
// UPLOAD PROGRESS TRACKING
// =====================================================

export interface UploadProgress {
  fileName: string
  loaded: number
  total: number
  percentage: number
  status: 'pending' | 'uploading' | 'completed' | 'failed'
  error?: string
}

/**
 * Upload file with progress tracking
 */
export async function uploadFileWithProgress(
  file: File,
  consultationId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<DirectUploadResult> {
  const startTime = Date.now()
  
  // Initialize progress
  const progress: UploadProgress = {
    fileName: file.name,
    loaded: 0,
    total: file.size,
    percentage: 0,
    status: 'pending'
  }
  
  onProgress?.(progress)
  
  try {
    // Step 1: Get pre-signed URL
    progress.status = 'uploading'
    progress.percentage = 10
    onProgress?.(progress)
    
    const preSignedResponse = await requestPreSignedUrl({
      consultationId,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size
    })
    
    if (!preSignedResponse.success) {
      throw new Error('Failed to get pre-signed URL')
    }
    
    progress.percentage = 20
    onProgress?.(progress)
    
    // Step 2: Upload with XMLHttpRequest for progress tracking
    const uploadResult = await uploadWithProgress(file, preSignedResponse.uploadUrl, (loaded) => {
      progress.loaded = loaded
      progress.percentage = 20 + Math.round((loaded / file.size) * 70) // 20-90%
      onProgress?.(progress)
    })
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Upload failed')
    }
    
    // Complete
    progress.status = 'completed'
    progress.percentage = 100
    onProgress?.(progress)
    
    const uploadTime = Date.now() - startTime
    
    return {
      success: true,
      publicUrl: preSignedResponse.publicUrl,
      fileName: preSignedResponse.fileName,
      uploadTime
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    
    progress.status = 'failed'
    progress.error = errorMessage
    onProgress?.(progress)
    
    return {
      success: false,
      error: errorMessage,
      uploadTime: Date.now() - startTime
    }
  }
}

/**
 * Upload with XMLHttpRequest for progress tracking
 */
function uploadWithProgress(
  file: File,
  preSignedUrl: string,
  onProgress: (loaded: number) => void
): Promise<{ success: boolean; error?: string }> {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest()
    
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        onProgress(event.loaded)
      }
    })
    
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve({ success: true })
      } else {
        resolve({ success: false, error: `HTTP ${xhr.status}: ${xhr.statusText}` })
      }
    })
    
    xhr.addEventListener('error', () => {
      resolve({ success: false, error: 'Network error during upload' })
    })
    
    xhr.addEventListener('abort', () => {
      resolve({ success: false, error: 'Upload aborted' })
    })
    
    xhr.open('PUT', preSignedUrl)
    xhr.setRequestHeader('Content-Type', file.type)
    xhr.send(file)
  })
}

// =====================================================
// VALIDATION HELPERS
// =====================================================

/**
 * Validate file before upload
 */
export function validateFileForUpload(file: File): { valid: boolean; error?: string } {
  // File size validation (100MB max)
  const MAX_FILE_SIZE = 100 * 1024 * 1024
  if (file.size > MAX_FILE_SIZE) {
    return { valid: false, error: `File too large: ${Math.round(file.size / 1024 / 1024)}MB (max 100MB)` }
  }
  
  // File type validation
  const allowedTypes = [
    // Audio types
    'audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg',
    // Image types
    'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'
  ]
  
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: `File type not supported: ${file.type}` }
  }
  
  return { valid: true }
}

/**
 * Estimate upload time based on file size and connection speed
 */
export function estimateUploadTime(fileSize: number, connectionSpeedMbps: number = 10): number {
  const fileSizeMb = fileSize / (1024 * 1024)
  const estimatedSeconds = (fileSizeMb * 8) / connectionSpeedMbps // Convert to bits and divide by speed
  return Math.ceil(estimatedSeconds)
}
