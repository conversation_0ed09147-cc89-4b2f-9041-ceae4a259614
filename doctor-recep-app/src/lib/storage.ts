import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'

// Storage configuration - Migrated to Cloudflare R2
export const STORAGE_CONFIG = {
  // R2 Configuration
  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',
  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',

  // Folder prefixes (replaces separate buckets)
  AUDIO_PREFIX: 'consultation-audio',
  IMAGE_PREFIX: 'consultation-images',

  // File limits
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file
  MAX_TOTAL_SIZE: 200 * 1024 * 1024, // 200MB per consultation
  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg'],
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],
  RETENTION_DAYS: 30
}

// R2 Client configuration
export const createR2Client = () => {
  return new S3Client({
    region: 'auto',
    endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,
    credentials: {
      accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',
      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************',
    },
  })
}

// File validation
export function validateFile(file: File, type: 'audio' | 'image'): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {
    return { valid: false, error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit` }
  }

  // Check file type
  const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: `File type ${file.type} is not allowed` }
  }

  return { valid: true }
}

// Generate storage path with folder prefix for R2
export function generateStoragePath(
  doctorId: string,
  consultationId: string,
  fileName: string,
  type: 'audio' | 'image'
): string {
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
  const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX
  return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`
}

// Upload file to Cloudflare R2
export async function uploadFile(
  file: File,
  doctorId: string,
  consultationId: string,
  type: 'audio' | 'image'
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    // Validate file
    const validation = validateFile(file, type)
    if (!validation.valid) {
      return { success: false, error: validation.error }
    }

    const r2Client = createR2Client()
    const filePath = generateStoragePath(doctorId, consultationId, file.name, type)

    // Convert file to buffer
    const fileBuffer = await file.arrayBuffer()

    // Upload to R2 - preserve exact Content-Type from file
    const uploadCommand = new PutObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath,
      Body: new Uint8Array(fileBuffer),
      ContentType: file.type, // Use original file.type to preserve codec info
      CacheControl: 'public, max-age=3600',
    })

    await r2Client.send(uploadCommand)

    // Generate public URL
    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`

    return { success: true, url: publicUrl }
  } catch (error) {
    console.error('R2 upload error:', error)
    return { success: false, error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}` }
  }
}

// Upload multiple files
export async function uploadMultipleFiles(
  files: File[],
  doctorId: string,
  consultationId: string,
  type: 'audio' | 'image'
): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> {
  const results = await Promise.all(
    files.map(file => uploadFile(file, doctorId, consultationId, type))
  )

  const successful = results.filter(r => r.success)
  const failed = results.filter(r => !r.success)

  if (failed.length > 0) {
    return {
      success: false,
      errors: failed.map(f => f.error || 'Unknown error')
    }
  }

  return {
    success: true,
    urls: successful.map(s => s.url!).filter(Boolean)
  }
}

// Delete file from R2 storage
export async function deleteFile(
  filePath: string,
  _type: 'audio' | 'image'
): Promise<{ success: boolean; error?: string }> {
  try {
    const r2Client = createR2Client()

    const deleteCommand = new DeleteObjectCommand({
      Bucket: STORAGE_CONFIG.BUCKET_NAME,
      Key: filePath,
    })

    await r2Client.send(deleteCommand)

    return { success: true }
  } catch (error) {
    console.error('R2 delete error:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' }
  }
}

// Extract file path from R2 URL
export function extractFilePathFromUrl(url: string, type: 'audio' | 'image'): string | null {
  try {
    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX
    const prefixPath = `/${prefix}/`
    const index = url.indexOf(prefixPath)

    if (index === -1) return null

    return url.substring(url.indexOf(prefix))
  } catch {
    return null
  }
}

// Download file from R2 storage
export async function downloadFile(
  filePath: string,
  _type: 'audio' | 'image'
): Promise<{ success: boolean; data?: Blob; error?: string }> {
  try {
    // For public files, we can fetch directly from the custom domain
    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`

    const response = await fetch(publicUrl)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.blob()
    return { success: true, data }
  } catch (error) {
    console.error('R2 download error:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Download failed' }
  }
}

// Calculate total file size
export function calculateTotalFileSize(files: File[]): number {
  return files.reduce((total, file) => total + file.size, 0)
}

// Validate total consultation file size
export function validateTotalSize(files: File[]): { valid: boolean; error?: string } {
  const totalSize = calculateTotalFileSize(files)
  if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {
    return {
      valid: false,
      error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`
    }
  }
  return { valid: true }
}
