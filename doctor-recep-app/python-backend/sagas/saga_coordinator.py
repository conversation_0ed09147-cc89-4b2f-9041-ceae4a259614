"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 2
Saga Coordinator - Distributed Transaction Management

This module implements the saga pattern for managing distributed transactions
across multiple services with automatic compensation and recovery.
"""

import asyncio
import json
import uuid
from typing import List, Dict, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import redis

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from monitoring import logger, metrics, performance_context
from message_schemas import ConsultationCreateEvent

# =====================================================
# SAGA CONFIGURATION
# =====================================================

SAGA_CONFIG = {
    'default_timeout_seconds': 300,  # 5 minutes
    'max_retry_attempts': 3,
    'retry_delay_seconds': 5,
    'compensation_timeout_seconds': 60,
    'state_persistence_ttl': 86400,  # 24 hours
    'cleanup_interval_seconds': 3600  # 1 hour
}

# =====================================================
# SAGA ENUMS AND DATA STRUCTURES
# =====================================================

class SagaStatus(str, Enum):
    """Saga execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    COMPENSATING = "compensating"
    COMPENSATED = "compensated"
    TIMEOUT = "timeout"

class StepStatus(str, Enum):
    """Individual step status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    COMPENSATED = "compensated"
    SKIPPED = "skipped"

@dataclass
class SagaStep:
    """Individual saga step definition"""
    step_id: str
    step_name: str
    execute_func: Callable
    compensate_func: Optional[Callable] = None
    timeout_seconds: int = 60
    retry_attempts: int = 3
    critical: bool = True  # If False, failure doesn't fail entire saga
    depends_on: List[str] = None  # Step dependencies
    
    def __post_init__(self):
        if self.depends_on is None:
            self.depends_on = []

@dataclass
class SagaStepExecution:
    """Saga step execution state"""
    step_id: str
    status: StepStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    result: Optional[Dict[str, Any]] = None
    compensation_result: Optional[Dict[str, Any]] = None

@dataclass
class SagaExecution:
    """Complete saga execution state"""
    saga_id: str
    saga_type: str
    status: SagaStatus
    correlation_id: str
    input_data: Dict[str, Any]
    steps: List[SagaStepExecution]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    timeout_at: Optional[datetime] = None
    error_message: Optional[str] = None
    compensation_steps: List[str] = None
    
    def __post_init__(self):
        if self.compensation_steps is None:
            self.compensation_steps = []

# =====================================================
# SAGA DEFINITION
# =====================================================

class SagaDefinition:
    """Saga definition with steps and compensation logic"""
    
    def __init__(self, saga_type: str, timeout_seconds: int = None):
        self.saga_type = saga_type
        self.timeout_seconds = timeout_seconds or SAGA_CONFIG['default_timeout_seconds']
        self.steps: List[SagaStep] = []
    
    def add_step(self, step: SagaStep) -> 'SagaDefinition':
        """Add a step to the saga"""
        self.steps.append(step)
        return self
    
    def validate(self) -> bool:
        """Validate saga definition"""
        step_ids = {step.step_id for step in self.steps}
        
        # Check for duplicate step IDs
        if len(step_ids) != len(self.steps):
            raise ValueError("Duplicate step IDs found")
        
        # Check dependencies
        for step in self.steps:
            for dep in step.depends_on:
                if dep not in step_ids:
                    raise ValueError(f"Step {step.step_id} depends on non-existent step {dep}")
        
        return True

# =====================================================
# SAGA COORDINATOR
# =====================================================

class SagaCoordinator:
    """Coordinates saga execution with compensation and recovery"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.saga_definitions: Dict[str, SagaDefinition] = {}
        self.active_sagas: Dict[str, SagaExecution] = {}
        self.cleanup_task: Optional[asyncio.Task] = None
        
        logger.info("🚀 Saga coordinator initialized")
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client for saga state persistence"""
        try:
            return redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
        except Exception as e:
            logger.error("Failed to initialize Redis for saga coordinator", error=str(e))
            return None
    
    def register_saga(self, definition: SagaDefinition):
        """Register a saga definition"""
        definition.validate()
        self.saga_definitions[definition.saga_type] = definition
        logger.info("Saga definition registered", saga_type=definition.saga_type)
    
    async def start_saga(self, 
                        saga_type: str, 
                        correlation_id: str, 
                        input_data: Dict[str, Any]) -> str:
        """Start a new saga execution"""
        
        if saga_type not in self.saga_definitions:
            raise ValueError(f"Unknown saga type: {saga_type}")
        
        saga_id = str(uuid.uuid4())
        definition = self.saga_definitions[saga_type]
        
        # Create saga execution state
        saga_execution = SagaExecution(
            saga_id=saga_id,
            saga_type=saga_type,
            status=SagaStatus.PENDING,
            correlation_id=correlation_id,
            input_data=input_data,
            steps=[
                SagaStepExecution(step.step_id, StepStatus.PENDING)
                for step in definition.steps
            ],
            created_at=datetime.utcnow(),
            timeout_at=datetime.utcnow() + timedelta(seconds=definition.timeout_seconds)
        )
        
        # Store in memory and Redis
        self.active_sagas[saga_id] = saga_execution
        await self._persist_saga_state(saga_execution)
        
        logger.info("Saga started", 
                   saga_id=saga_id, 
                   saga_type=saga_type,
                   correlation_id=correlation_id)
        
        # Start execution
        asyncio.create_task(self._execute_saga(saga_id))
        
        return saga_id
    
    async def _execute_saga(self, saga_id: str):
        """Execute saga steps with dependency resolution"""
        saga = self.active_sagas.get(saga_id)
        if not saga:
            logger.error("Saga not found for execution", saga_id=saga_id)
            return
        
        definition = self.saga_definitions[saga.saga_type]
        saga.status = SagaStatus.RUNNING
        saga.started_at = datetime.utcnow()
        
        try:
            with performance_context(f"saga_execution_{saga.saga_type}"):
                logger.info("🚀 Executing saga", 
                           saga_id=saga_id, 
                           saga_type=saga.saga_type)
                
                # Execute steps in dependency order
                completed_steps = set()
                
                while len(completed_steps) < len(definition.steps):
                    # Find steps that can be executed (dependencies met)
                    ready_steps = [
                        step for step in definition.steps
                        if (step.step_id not in completed_steps and
                            all(dep in completed_steps for dep in step.depends_on))
                    ]
                    
                    if not ready_steps:
                        # Check for circular dependencies or other issues
                        pending_steps = [s.step_id for s in definition.steps if s.step_id not in completed_steps]
                        raise Exception(f"No ready steps found. Pending: {pending_steps}")
                    
                    # Execute ready steps in parallel
                    step_tasks = [
                        self._execute_step(saga_id, step)
                        for step in ready_steps
                    ]
                    
                    step_results = await asyncio.gather(*step_tasks, return_exceptions=True)
                    
                    # Process results
                    for i, result in enumerate(step_results):
                        step = ready_steps[i]
                        
                        if isinstance(result, Exception):
                            if step.critical:
                                raise result
                            else:
                                # Non-critical step failed - mark as skipped
                                step_execution = self._get_step_execution(saga, step.step_id)
                                step_execution.status = StepStatus.SKIPPED
                                step_execution.error_message = str(result)
                                logger.warning("Non-critical step failed", 
                                             saga_id=saga_id,
                                             step_id=step.step_id,
                                             error=str(result))
                        
                        completed_steps.add(step.step_id)
                
                # All steps completed successfully
                saga.status = SagaStatus.COMPLETED
                saga.completed_at = datetime.utcnow()
                
                logger.info("✅ Saga completed successfully", 
                           saga_id=saga_id,
                           duration_seconds=(saga.completed_at - saga.started_at).total_seconds())
                
                # Record metrics
                metrics.increment_counter('saga_executions_total', {'status': 'completed', 'type': saga.saga_type})
                
        except Exception as e:
            logger.error("❌ Saga execution failed", 
                        saga_id=saga_id, 
                        error=str(e))
            
            saga.status = SagaStatus.FAILED
            saga.error_message = str(e)
            saga.completed_at = datetime.utcnow()
            
            # Start compensation
            await self._compensate_saga(saga_id)
            
            # Record metrics
            metrics.increment_counter('saga_executions_total', {'status': 'failed', 'type': saga.saga_type})
        
        finally:
            await self._persist_saga_state(saga)
            
            # Clean up from active sagas after delay
            asyncio.create_task(self._cleanup_completed_saga(saga_id, delay_seconds=300))
    
    async def _execute_step(self, saga_id: str, step: SagaStep) -> Dict[str, Any]:
        """Execute a single saga step with retry logic"""
        saga = self.active_sagas[saga_id]
        step_execution = self._get_step_execution(saga, step.step_id)
        
        step_execution.status = StepStatus.RUNNING
        step_execution.started_at = datetime.utcnow()
        
        for attempt in range(step.retry_attempts + 1):
            try:
                logger.info("Executing saga step", 
                           saga_id=saga_id,
                           step_id=step.step_id,
                           attempt=attempt + 1)
                
                # Execute step with timeout
                result = await asyncio.wait_for(
                    step.execute_func(saga.input_data, saga.correlation_id),
                    timeout=step.timeout_seconds
                )
                
                step_execution.status = StepStatus.COMPLETED
                step_execution.completed_at = datetime.utcnow()
                step_execution.result = result
                
                logger.info("✅ Saga step completed", 
                           saga_id=saga_id,
                           step_id=step.step_id)
                
                return result
                
            except asyncio.TimeoutError:
                error_msg = f"Step timeout after {step.timeout_seconds} seconds"
                logger.error("Saga step timeout", 
                           saga_id=saga_id,
                           step_id=step.step_id,
                           timeout_seconds=step.timeout_seconds)
                
                if attempt == step.retry_attempts:
                    step_execution.status = StepStatus.FAILED
                    step_execution.error_message = error_msg
                    raise Exception(error_msg)
                
            except Exception as e:
                error_msg = str(e)
                logger.error("Saga step failed", 
                           saga_id=saga_id,
                           step_id=step.step_id,
                           attempt=attempt + 1,
                           error=error_msg)
                
                if attempt == step.retry_attempts:
                    step_execution.status = StepStatus.FAILED
                    step_execution.error_message = error_msg
                    raise
                
                # Wait before retry
                if attempt < step.retry_attempts:
                    await asyncio.sleep(SAGA_CONFIG['retry_delay_seconds'])
                    step_execution.retry_count += 1
    
    async def _compensate_saga(self, saga_id: str):
        """Execute compensation steps for failed saga"""
        saga = self.active_sagas.get(saga_id)
        if not saga:
            return
        
        saga.status = SagaStatus.COMPENSATING
        definition = self.saga_definitions[saga.saga_type]
        
        logger.info("🔄 Starting saga compensation", saga_id=saga_id)
        
        try:
            # Compensate completed steps in reverse order
            completed_steps = [
                step for step in saga.steps
                if step.status == StepStatus.COMPLETED
            ]
            
            for step_execution in reversed(completed_steps):
                step_def = next(
                    (s for s in definition.steps if s.step_id == step_execution.step_id),
                    None
                )
                
                if step_def and step_def.compensate_func:
                    try:
                        logger.info("Compensating saga step", 
                                   saga_id=saga_id,
                                   step_id=step_execution.step_id)
                        
                        compensation_result = await asyncio.wait_for(
                            step_def.compensate_func(
                                step_execution.result,
                                saga.input_data,
                                saga.correlation_id
                            ),
                            timeout=SAGA_CONFIG['compensation_timeout_seconds']
                        )
                        
                        step_execution.status = StepStatus.COMPENSATED
                        step_execution.compensation_result = compensation_result
                        saga.compensation_steps.append(step_execution.step_id)
                        
                        logger.info("✅ Saga step compensated", 
                                   saga_id=saga_id,
                                   step_id=step_execution.step_id)
                        
                    except Exception as e:
                        logger.error("❌ Saga step compensation failed", 
                                   saga_id=saga_id,
                                   step_id=step_execution.step_id,
                                   error=str(e))
                        # Continue with other compensations
            
            saga.status = SagaStatus.COMPENSATED
            logger.info("✅ Saga compensation completed", saga_id=saga_id)
            
        except Exception as e:
            logger.error("❌ Saga compensation failed", 
                        saga_id=saga_id, 
                        error=str(e))
            # Saga remains in COMPENSATING status for manual intervention
    
    def _get_step_execution(self, saga: SagaExecution, step_id: str) -> SagaStepExecution:
        """Get step execution by ID"""
        for step in saga.steps:
            if step.step_id == step_id:
                return step
        raise ValueError(f"Step {step_id} not found in saga {saga.saga_id}")
    
    async def _persist_saga_state(self, saga: SagaExecution):
        """Persist saga state to Redis"""
        if not self.redis_client:
            return
        
        try:
            saga_key = f"saga:state:{saga.saga_id}"
            saga_data = {
                'saga_data': json.dumps(asdict(saga), default=str),
                'updated_at': datetime.utcnow().isoformat()
            }
            
            self.redis_client.hset(saga_key, mapping=saga_data)
            self.redis_client.expire(saga_key, SAGA_CONFIG['state_persistence_ttl'])
            
        except Exception as e:
            logger.error("Failed to persist saga state", 
                        saga_id=saga.saga_id, 
                        error=str(e))
    
    async def _cleanup_completed_saga(self, saga_id: str, delay_seconds: int = 300):
        """Clean up completed saga after delay"""
        await asyncio.sleep(delay_seconds)
        
        if saga_id in self.active_sagas:
            saga = self.active_sagas[saga_id]
            if saga.status in [SagaStatus.COMPLETED, SagaStatus.COMPENSATED]:
                del self.active_sagas[saga_id]
                logger.info("Cleaned up completed saga", saga_id=saga_id)
    
    async def get_saga_status(self, saga_id: str) -> Optional[Dict[str, Any]]:
        """Get current saga status"""
        saga = self.active_sagas.get(saga_id)
        if saga:
            return asdict(saga)
        
        # Try to load from Redis
        if self.redis_client:
            try:
                saga_key = f"saga:state:{saga_id}"
                saga_data = self.redis_client.hget(saga_key, 'saga_data')
                if saga_data:
                    return json.loads(saga_data)
            except Exception as e:
                logger.error("Failed to load saga from Redis", 
                           saga_id=saga_id, 
                           error=str(e))
        
        return None
    
    async def start_cleanup_task(self):
        """Start background cleanup task"""
        if self.cleanup_task:
            return
        
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Saga cleanup task started")
    
    async def _cleanup_loop(self):
        """Background cleanup of expired sagas"""
        while True:
            try:
                await asyncio.sleep(SAGA_CONFIG['cleanup_interval_seconds'])
                
                current_time = datetime.utcnow()
                expired_sagas = [
                    saga_id for saga_id, saga in self.active_sagas.items()
                    if saga.timeout_at and current_time > saga.timeout_at
                ]
                
                for saga_id in expired_sagas:
                    saga = self.active_sagas[saga_id]
                    if saga.status == SagaStatus.RUNNING:
                        saga.status = SagaStatus.TIMEOUT
                        saga.completed_at = current_time
                        await self._compensate_saga(saga_id)
                        logger.warning("Saga timed out", saga_id=saga_id)
                
            except Exception as e:
                logger.error("Error in saga cleanup loop", error=str(e))
    
    async def shutdown(self):
        """Shutdown saga coordinator"""
        logger.info("🛑 Shutting down saga coordinator")
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Wait for active sagas to complete or timeout
        active_saga_ids = list(self.active_sagas.keys())
        if active_saga_ids:
            logger.info(f"Waiting for {len(active_saga_ids)} active sagas to complete")
            # In production, you might want to force compensation here
        
        logger.info("✅ Saga coordinator shutdown complete")

# =====================================================
# GLOBAL INSTANCE
# =====================================================

_saga_coordinator: Optional[SagaCoordinator] = None

def get_saga_coordinator() -> SagaCoordinator:
    """Get singleton saga coordinator"""
    global _saga_coordinator
    if _saga_coordinator is None:
        _saga_coordinator = SagaCoordinator()
    return _saga_coordinator
